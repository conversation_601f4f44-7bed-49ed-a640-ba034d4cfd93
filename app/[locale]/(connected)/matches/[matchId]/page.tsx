import {FC} from "react";
import MatchPage from "@/src/Gaming/client/infrastructure/pages/Gaming/MatchPage/MatchPage";

type Props = {
  params: Promise<{ locale: string; matchId: string }>;
};

const MatchPageContainer: FC<Props> = async ({params}) => {
  const {locale, matchId} = await params;

  return (
    <MatchPage locale={locale} matchId={matchId}/>
  );
};

export default MatchPageContainer;
