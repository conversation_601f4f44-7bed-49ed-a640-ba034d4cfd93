import type {<PERSON>} from 'react';
import AccessDeniedPage from "@/src/Authentication/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage";

type Props = {
  params: Promise<{ locale: string }>;
};

const AccessDeniedPageContainer: FC<Props> = async ({params}) => {
  const {locale} = await params;

  return (
    <AccessDeniedPage locale={locale}/>
  );
};

export default AccessDeniedPageContainer;
