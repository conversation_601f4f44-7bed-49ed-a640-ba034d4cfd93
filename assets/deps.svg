<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.0 (20241103.1931)
 -->
<!-- Title: dependency&#45;cruiser output Pages: 1 -->
<svg width="2140pt" height="8194pt"
 viewBox="0.00 0.00 2139.88 8194.28" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 8190.28)">
<title>dependency&#45;cruiser output</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-8190.28 2135.88,-8190.28 2135.88,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster_app</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M20,-7352.28C20,-7352.28 310.38,-7352.28 310.38,-7352.28 316.38,-7352.28 322.38,-7358.28 322.38,-7364.28 322.38,-7364.28 322.38,-8166.28 322.38,-8166.28 322.38,-8172.28 316.38,-8178.28 310.38,-8178.28 310.38,-8178.28 20,-8178.28 20,-8178.28 14,-8178.28 8,-8172.28 8,-8166.28 8,-8166.28 8,-7364.28 8,-7364.28 8,-7358.28 14,-7352.28 20,-7352.28"/>
<text text-anchor="middle" x="165.19" y="-8165.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">app</text>
</g>
<g id="clust2" class="cluster">
<title>cluster_app/[locale]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M28,-7417.28C28,-7417.28 160.25,-7417.28 160.25,-7417.28 166.25,-7417.28 172.25,-7423.28 172.25,-7429.28 172.25,-7429.28 172.25,-8139.28 172.25,-8139.28 172.25,-8145.28 166.25,-8151.28 160.25,-8151.28 160.25,-8151.28 28,-8151.28 28,-8151.28 22,-8151.28 16,-8145.28 16,-8139.28 16,-8139.28 16,-7429.28 16,-7429.28 16,-7423.28 22,-7417.28 28,-7417.28"/>
<text text-anchor="middle" x="94.12" y="-8138.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[locale]</text>
</g>
<g id="clust3" class="cluster">
<title>cluster_app/[locale]/(connected)</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M36,-7634.28C36,-7634.28 152.25,-7634.28 152.25,-7634.28 158.25,-7634.28 164.25,-7640.28 164.25,-7646.28 164.25,-7646.28 164.25,-8112.28 164.25,-8112.28 164.25,-8118.28 158.25,-8124.28 152.25,-8124.28 152.25,-8124.28 36,-8124.28 36,-8124.28 30,-8124.28 24,-8118.28 24,-8112.28 24,-8112.28 24,-7646.28 24,-7646.28 24,-7640.28 30,-7634.28 36,-7634.28"/>
<text text-anchor="middle" x="94.12" y="-8111.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">(connected)</text>
</g>
<g id="clust4" class="cluster">
<title>cluster_app/[locale]/(connected)/games</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M44,-7738.28C44,-7738.28 144.25,-7738.28 144.25,-7738.28 150.25,-7738.28 156.25,-7744.28 156.25,-7750.28 156.25,-7750.28 156.25,-8085.28 156.25,-8085.28 156.25,-8091.28 150.25,-8097.28 144.25,-8097.28 144.25,-8097.28 44,-8097.28 44,-8097.28 38,-8097.28 32,-8091.28 32,-8085.28 32,-8085.28 32,-7750.28 32,-7750.28 32,-7744.28 38,-7738.28 44,-7738.28"/>
<text text-anchor="middle" x="94.12" y="-8084.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">games</text>
</g>
<g id="clust5" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M52,-7772.28C52,-7772.28 136.25,-7772.28 136.25,-7772.28 142.25,-7772.28 148.25,-7778.28 148.25,-7784.28 148.25,-7784.28 148.25,-8058.28 148.25,-8058.28 148.25,-8064.28 142.25,-8070.28 136.25,-8070.28 136.25,-8070.28 52,-8070.28 52,-8070.28 46,-8070.28 40,-8064.28 40,-8058.28 40,-8058.28 40,-7784.28 40,-7784.28 40,-7778.28 46,-7772.28 52,-7772.28"/>
<text text-anchor="middle" x="94.12" y="-8057.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[gameId]</text>
</g>
<g id="clust6" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/deck&#45;builder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M60,-7867.28C60,-7867.28 128.25,-7867.28 128.25,-7867.28 134.25,-7867.28 140.25,-7873.28 140.25,-7879.28 140.25,-7879.28 140.25,-8031.28 140.25,-8031.28 140.25,-8037.28 134.25,-8043.28 128.25,-8043.28 128.25,-8043.28 60,-8043.28 60,-8043.28 54,-8043.28 48,-8037.28 48,-8031.28 48,-8031.28 48,-7879.28 48,-7879.28 48,-7873.28 54,-7867.28 60,-7867.28"/>
<text text-anchor="middle" x="94.12" y="-8030.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">deck&#45;builder</text>
</g>
<g id="clust7" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M68,-7932.28C68,-7932.28 120.25,-7932.28 120.25,-7932.28 126.25,-7932.28 132.25,-7938.28 132.25,-7944.28 132.25,-7944.28 132.25,-8004.28 132.25,-8004.28 132.25,-8010.28 126.25,-8016.28 120.25,-8016.28 120.25,-8016.28 68,-8016.28 68,-8016.28 62,-8016.28 56,-8010.28 56,-8004.28 56,-8004.28 56,-7944.28 56,-7944.28 56,-7938.28 62,-7932.28 68,-7932.28"/>
<text text-anchor="middle" x="94.12" y="-8003.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[deckId]</text>
</g>
<g id="clust8" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/play</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M71.12,-7780.28C71.12,-7780.28 117.12,-7780.28 117.12,-7780.28 123.12,-7780.28 129.12,-7786.28 129.12,-7792.28 129.12,-7792.28 129.12,-7821.28 129.12,-7821.28 129.12,-7827.28 123.12,-7833.28 117.12,-7833.28 117.12,-7833.28 71.12,-7833.28 71.12,-7833.28 65.12,-7833.28 59.12,-7827.28 59.12,-7821.28 59.12,-7821.28 59.12,-7792.28 59.12,-7792.28 59.12,-7786.28 65.12,-7780.28 71.12,-7780.28"/>
<text text-anchor="middle" x="94.12" y="-7820.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">play</text>
</g>
<g id="clust9" class="cluster">
<title>cluster_app/[locale]/(connected)/matches</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M63.12,-7642.28C63.12,-7642.28 125.12,-7642.28 125.12,-7642.28 131.12,-7642.28 137.12,-7648.28 137.12,-7654.28 137.12,-7654.28 137.12,-7718.28 137.12,-7718.28 137.12,-7724.28 131.12,-7730.28 125.12,-7730.28 125.12,-7730.28 63.12,-7730.28 63.12,-7730.28 57.12,-7730.28 51.12,-7724.28 51.12,-7718.28 51.12,-7718.28 51.12,-7654.28 51.12,-7654.28 51.12,-7648.28 57.12,-7642.28 63.12,-7642.28"/>
<text text-anchor="middle" x="94.12" y="-7717.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">matches</text>
</g>
<g id="clust10" class="cluster">
<title>cluster_app/[locale]/(connected)/matches/[matchId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M71.12,-7650.28C71.12,-7650.28 117.12,-7650.28 117.12,-7650.28 123.12,-7650.28 129.12,-7656.28 129.12,-7662.28 129.12,-7662.28 129.12,-7691.28 129.12,-7691.28 129.12,-7697.28 123.12,-7703.28 117.12,-7703.28 117.12,-7703.28 71.12,-7703.28 71.12,-7703.28 65.12,-7703.28 59.12,-7697.28 59.12,-7691.28 59.12,-7691.28 59.12,-7662.28 59.12,-7662.28 59.12,-7656.28 65.12,-7650.28 71.12,-7650.28"/>
<text text-anchor="middle" x="94.12" y="-7690.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[matchId]</text>
</g>
<g id="clust11" class="cluster">
<title>cluster_app/[locale]/(not&#45;connected)</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M63.12,-7451.28C63.12,-7451.28 125.12,-7451.28 125.12,-7451.28 131.12,-7451.28 137.12,-7457.28 137.12,-7463.28 137.12,-7463.28 137.12,-7553.28 137.12,-7553.28 137.12,-7559.28 131.12,-7565.28 125.12,-7565.28 125.12,-7565.28 63.12,-7565.28 63.12,-7565.28 57.12,-7565.28 51.12,-7559.28 51.12,-7553.28 51.12,-7553.28 51.12,-7463.28 51.12,-7463.28 51.12,-7457.28 57.12,-7451.28 63.12,-7451.28"/>
<text text-anchor="middle" x="94.12" y="-7552.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">(not&#45;connected)</text>
</g>
<g id="clust12" class="cluster">
<title>cluster_app/[locale]/(not&#45;connected)/signin</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M71.12,-7485.28C71.12,-7485.28 117.12,-7485.28 117.12,-7485.28 123.12,-7485.28 129.12,-7491.28 129.12,-7497.28 129.12,-7497.28 129.12,-7526.28 129.12,-7526.28 129.12,-7532.28 123.12,-7538.28 117.12,-7538.28 117.12,-7538.28 71.12,-7538.28 71.12,-7538.28 65.12,-7538.28 59.12,-7532.28 59.12,-7526.28 59.12,-7526.28 59.12,-7497.28 59.12,-7497.28 59.12,-7491.28 65.12,-7485.28 71.12,-7485.28"/>
<text text-anchor="middle" x="94.12" y="-7525.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">signin</text>
</g>
<g id="clust13" class="cluster">
<title>cluster_app/[locale]/access&#45;denied</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M67.12,-7573.28C67.12,-7573.28 122.12,-7573.28 122.12,-7573.28 128.12,-7573.28 134.12,-7579.28 134.12,-7585.28 134.12,-7585.28 134.12,-7614.28 134.12,-7614.28 134.12,-7620.28 128.12,-7626.28 122.12,-7626.28 122.12,-7626.28 67.12,-7626.28 67.12,-7626.28 61.12,-7626.28 55.12,-7620.28 55.12,-7614.28 55.12,-7614.28 55.12,-7585.28 55.12,-7585.28 55.12,-7579.28 61.12,-7573.28 67.12,-7573.28"/>
<text text-anchor="middle" x="94.62" y="-7613.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">access&#45;denied</text>
</g>
<g id="clust14" class="cluster">
<title>cluster_src</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M192.25,-56.28C192.25,-56.28 2111.88,-56.28 2111.88,-56.28 2117.88,-56.28 2123.88,-62.28 2123.88,-68.28 2123.88,-68.28 2123.88,-7332.28 2123.88,-7332.28 2123.88,-7338.28 2117.88,-7344.28 2111.88,-7344.28 2111.88,-7344.28 192.25,-7344.28 192.25,-7344.28 186.25,-7344.28 180.25,-7338.28 180.25,-7332.28 180.25,-7332.28 180.25,-68.28 180.25,-68.28 180.25,-62.28 186.25,-56.28 192.25,-56.28"/>
<text text-anchor="middle" x="1152.06" y="-7331.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">src</text>
</g>
<g id="clust15" class="cluster">
<title>cluster_src/client</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M200.25,-64.28C200.25,-64.28 2103.88,-64.28 2103.88,-64.28 2109.88,-64.28 2115.88,-70.28 2115.88,-76.28 2115.88,-76.28 2115.88,-5475.28 2115.88,-5475.28 2115.88,-5481.28 2109.88,-5487.28 2103.88,-5487.28 2103.88,-5487.28 200.25,-5487.28 200.25,-5487.28 194.25,-5487.28 188.25,-5481.28 188.25,-5475.28 188.25,-5475.28 188.25,-76.28 188.25,-76.28 188.25,-70.28 194.25,-64.28 200.25,-64.28"/>
<text text-anchor="middle" x="1152.06" y="-5474.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">client</text>
</g>
<g id="clust16" class="cluster">
<title>cluster_src/client/application</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M935.75,-613.28C935.75,-613.28 1754.38,-613.28 1754.38,-613.28 1760.38,-613.28 1766.38,-619.28 1766.38,-625.28 1766.38,-625.28 1766.38,-2842.28 1766.38,-2842.28 1766.38,-2848.28 1760.38,-2854.28 1754.38,-2854.28 1754.38,-2854.28 935.75,-2854.28 935.75,-2854.28 929.75,-2854.28 923.75,-2848.28 923.75,-2842.28 923.75,-2842.28 923.75,-625.28 923.75,-625.28 923.75,-619.28 929.75,-613.28 935.75,-613.28"/>
<text text-anchor="middle" x="1345.06" y="-2841.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">application</text>
</g>
<g id="clust17" class="cluster">
<title>cluster_src/client/application/commands</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1117.75,-621.28C1117.75,-621.28 1606.88,-621.28 1606.88,-621.28 1612.88,-621.28 1618.88,-627.28 1618.88,-633.28 1618.88,-633.28 1618.88,-1429.28 1618.88,-1429.28 1618.88,-1435.28 1612.88,-1441.28 1606.88,-1441.28 1606.88,-1441.28 1117.75,-1441.28 1117.75,-1441.28 1111.75,-1441.28 1105.75,-1435.28 1105.75,-1429.28 1105.75,-1429.28 1105.75,-633.28 1105.75,-633.28 1105.75,-627.28 1111.75,-621.28 1117.75,-621.28"/>
<text text-anchor="middle" x="1362.31" y="-1428.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">commands</text>
</g>
<g id="clust18" class="cluster">
<title>cluster_src/client/application/commands/addCardToDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1162.5,-934.28C1162.5,-934.28 1439.88,-934.28 1439.88,-934.28 1445.88,-934.28 1451.88,-940.28 1451.88,-946.28 1451.88,-946.28 1451.88,-975.28 1451.88,-975.28 1451.88,-981.28 1445.88,-987.28 1439.88,-987.28 1439.88,-987.28 1162.5,-987.28 1162.5,-987.28 1156.5,-987.28 1150.5,-981.28 1150.5,-975.28 1150.5,-975.28 1150.5,-946.28 1150.5,-946.28 1150.5,-940.28 1156.5,-934.28 1162.5,-934.28"/>
<text text-anchor="middle" x="1301.19" y="-974.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">addCardToDeck</text>
</g>
<g id="clust19" class="cluster">
<title>cluster_src/client/application/commands/clearDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1165.5,-1300.28C1165.5,-1300.28 1243.25,-1300.28 1243.25,-1300.28 1249.25,-1300.28 1255.25,-1306.28 1255.25,-1312.28 1255.25,-1312.28 1255.25,-1341.28 1255.25,-1341.28 1255.25,-1347.28 1249.25,-1353.28 1243.25,-1353.28 1243.25,-1353.28 1165.5,-1353.28 1165.5,-1353.28 1159.5,-1353.28 1153.5,-1347.28 1153.5,-1341.28 1153.5,-1341.28 1153.5,-1312.28 1153.5,-1312.28 1153.5,-1306.28 1159.5,-1300.28 1165.5,-1300.28"/>
<text text-anchor="middle" x="1204.38" y="-1340.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">clearDeckDraft</text>
</g>
<g id="clust20" class="cluster">
<title>cluster_src/client/application/commands/filterCatalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1347.5,-1239.28C1347.5,-1239.28 1598.88,-1239.28 1598.88,-1239.28 1604.88,-1239.28 1610.88,-1245.28 1610.88,-1251.28 1610.88,-1251.28 1610.88,-1280.28 1610.88,-1280.28 1610.88,-1286.28 1604.88,-1292.28 1598.88,-1292.28 1598.88,-1292.28 1347.5,-1292.28 1347.5,-1292.28 1341.5,-1292.28 1335.5,-1286.28 1335.5,-1280.28 1335.5,-1280.28 1335.5,-1251.28 1335.5,-1251.28 1335.5,-1245.28 1341.5,-1239.28 1347.5,-1239.28"/>
<text text-anchor="middle" x="1473.19" y="-1279.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">filterCatalog</text>
</g>
<g id="clust21" class="cluster">
<title>cluster_src/client/application/commands/hideCardDetails</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1162.5,-629.28C1162.5,-629.28 1246.25,-629.28 1246.25,-629.28 1252.25,-629.28 1258.25,-635.28 1258.25,-641.28 1258.25,-641.28 1258.25,-670.28 1258.25,-670.28 1258.25,-676.28 1252.25,-682.28 1246.25,-682.28 1246.25,-682.28 1162.5,-682.28 1162.5,-682.28 1156.5,-682.28 1150.5,-676.28 1150.5,-670.28 1150.5,-670.28 1150.5,-641.28 1150.5,-641.28 1150.5,-635.28 1156.5,-629.28 1162.5,-629.28"/>
<text text-anchor="middle" x="1204.38" y="-669.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hideCardDetails</text>
</g>
<g id="clust22" class="cluster">
<title>cluster_src/client/application/commands/initializeDeckBuilderFromLocation</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1125.75,-1178.28C1125.75,-1178.28 1283,-1178.28 1283,-1178.28 1289,-1178.28 1295,-1184.28 1295,-1190.28 1295,-1190.28 1295,-1219.28 1295,-1219.28 1295,-1225.28 1289,-1231.28 1283,-1231.28 1283,-1231.28 1125.75,-1231.28 1125.75,-1231.28 1119.75,-1231.28 1113.75,-1225.28 1113.75,-1219.28 1113.75,-1219.28 1113.75,-1190.28 1113.75,-1190.28 1113.75,-1184.28 1119.75,-1178.28 1125.75,-1178.28"/>
<text text-anchor="middle" x="1204.38" y="-1218.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">initializeDeckBuilderFromLocation</text>
</g>
<g id="clust23" class="cluster">
<title>cluster_src/client/application/commands/loadCatalogCards</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1158.38,-995.28C1158.38,-995.28 1444,-995.28 1444,-995.28 1450,-995.28 1456,-1001.28 1456,-1007.28 1456,-1007.28 1456,-1036.28 1456,-1036.28 1456,-1042.28 1450,-1048.28 1444,-1048.28 1444,-1048.28 1158.38,-1048.28 1158.38,-1048.28 1152.38,-1048.28 1146.38,-1042.28 1146.38,-1036.28 1146.38,-1036.28 1146.38,-1007.28 1146.38,-1007.28 1146.38,-1001.28 1152.38,-995.28 1158.38,-995.28"/>
<text text-anchor="middle" x="1301.19" y="-1035.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadCatalogCards</text>
</g>
<g id="clust24" class="cluster">
<title>cluster_src/client/application/commands/loadDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1166.62,-1239.28C1166.62,-1239.28 1242.12,-1239.28 1242.12,-1239.28 1248.12,-1239.28 1254.12,-1245.28 1254.12,-1251.28 1254.12,-1251.28 1254.12,-1280.28 1254.12,-1280.28 1254.12,-1286.28 1248.12,-1292.28 1242.12,-1292.28 1242.12,-1292.28 1166.62,-1292.28 1166.62,-1292.28 1160.62,-1292.28 1154.62,-1286.28 1154.62,-1280.28 1154.62,-1280.28 1154.62,-1251.28 1154.62,-1251.28 1154.62,-1245.28 1160.62,-1239.28 1166.62,-1239.28"/>
<text text-anchor="middle" x="1204.38" y="-1279.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadDeckDraft</text>
</g>
<g id="clust25" class="cluster">
<title>cluster_src/client/application/commands/loadDeckIntoBuilder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1154.62,-751.28C1154.62,-751.28 1448.12,-751.28 1448.12,-751.28 1454.12,-751.28 1460.12,-757.28 1460.12,-763.28 1460.12,-763.28 1460.12,-792.28 1460.12,-792.28 1460.12,-798.28 1454.12,-804.28 1448.12,-804.28 1448.12,-804.28 1154.62,-804.28 1154.62,-804.28 1148.62,-804.28 1142.62,-798.28 1142.62,-792.28 1142.62,-792.28 1142.62,-763.28 1142.62,-763.28 1142.62,-757.28 1148.62,-751.28 1154.62,-751.28"/>
<text text-anchor="middle" x="1301.38" y="-791.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadDeckIntoBuilder</text>
</g>
<g id="clust26" class="cluster">
<title>cluster_src/client/application/commands/loadGameSettings</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1158,-1056.28C1158,-1056.28 1444.38,-1056.28 1444.38,-1056.28 1450.38,-1056.28 1456.38,-1062.28 1456.38,-1068.28 1456.38,-1068.28 1456.38,-1097.28 1456.38,-1097.28 1456.38,-1103.28 1450.38,-1109.28 1444.38,-1109.28 1444.38,-1109.28 1158,-1109.28 1158,-1109.28 1152,-1109.28 1146,-1103.28 1146,-1097.28 1146,-1097.28 1146,-1068.28 1146,-1068.28 1146,-1062.28 1152,-1056.28 1158,-1056.28"/>
<text text-anchor="middle" x="1301.19" y="-1096.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadGameSettings</text>
</g>
<g id="clust27" class="cluster">
<title>cluster_src/client/application/commands/removeCardFromDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1149.38,-1117.28C1149.38,-1117.28 1453,-1117.28 1453,-1117.28 1459,-1117.28 1465,-1123.28 1465,-1129.28 1465,-1129.28 1465,-1158.28 1465,-1158.28 1465,-1164.28 1459,-1170.28 1453,-1170.28 1453,-1170.28 1149.38,-1170.28 1149.38,-1170.28 1143.38,-1170.28 1137.38,-1164.28 1137.38,-1158.28 1137.38,-1158.28 1137.38,-1129.28 1137.38,-1129.28 1137.38,-1123.28 1143.38,-1117.28 1149.38,-1117.28"/>
<text text-anchor="middle" x="1301.19" y="-1157.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">removeCardFromDeck</text>
</g>
<g id="clust28" class="cluster">
<title>cluster_src/client/application/commands/saveDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1165.88,-1361.28C1165.88,-1361.28 1242.88,-1361.28 1242.88,-1361.28 1248.88,-1361.28 1254.88,-1367.28 1254.88,-1373.28 1254.88,-1373.28 1254.88,-1402.28 1254.88,-1402.28 1254.88,-1408.28 1248.88,-1414.28 1242.88,-1414.28 1242.88,-1414.28 1165.88,-1414.28 1165.88,-1414.28 1159.88,-1414.28 1153.88,-1408.28 1153.88,-1402.28 1153.88,-1402.28 1153.88,-1373.28 1153.88,-1373.28 1153.88,-1367.28 1159.88,-1361.28 1165.88,-1361.28"/>
<text text-anchor="middle" x="1204.38" y="-1401.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">saveDeckDraft</text>
</g>
<g id="clust29" class="cluster">
<title>cluster_src/client/application/commands/search</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1357.75,-1330.28C1357.75,-1330.28 1588,-1330.28 1588,-1330.28 1594,-1330.28 1600,-1336.28 1600,-1342.28 1600,-1342.28 1600,-1371.28 1600,-1371.28 1600,-1377.28 1594,-1383.28 1588,-1383.28 1588,-1383.28 1357.75,-1383.28 1357.75,-1383.28 1351.75,-1383.28 1345.75,-1377.28 1345.75,-1371.28 1345.75,-1371.28 1345.75,-1342.28 1345.75,-1342.28 1345.75,-1336.28 1351.75,-1330.28 1357.75,-1330.28"/>
<text text-anchor="middle" x="1472.88" y="-1370.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">search</text>
</g>
<g id="clust30" class="cluster">
<title>cluster_src/client/application/commands/showCardDetails</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1160.62,-690.28C1160.62,-690.28 1441.75,-690.28 1441.75,-690.28 1447.75,-690.28 1453.75,-696.28 1453.75,-702.28 1453.75,-702.28 1453.75,-731.28 1453.75,-731.28 1453.75,-737.28 1447.75,-743.28 1441.75,-743.28 1441.75,-743.28 1160.62,-743.28 1160.62,-743.28 1154.62,-743.28 1148.62,-737.28 1148.62,-731.28 1148.62,-731.28 1148.62,-702.28 1148.62,-702.28 1148.62,-696.28 1154.62,-690.28 1160.62,-690.28"/>
<text text-anchor="middle" x="1301.19" y="-730.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">showCardDetails</text>
</g>
<g id="clust31" class="cluster">
<title>cluster_src/client/application/commands/switchDeckBuilderView</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1148.25,-812.28C1148.25,-812.28 1454.5,-812.28 1454.5,-812.28 1460.5,-812.28 1466.5,-818.28 1466.5,-824.28 1466.5,-824.28 1466.5,-853.28 1466.5,-853.28 1466.5,-859.28 1460.5,-865.28 1454.5,-865.28 1454.5,-865.28 1148.25,-865.28 1148.25,-865.28 1142.25,-865.28 1136.25,-859.28 1136.25,-853.28 1136.25,-853.28 1136.25,-824.28 1136.25,-824.28 1136.25,-818.28 1142.25,-812.28 1148.25,-812.28"/>
<text text-anchor="middle" x="1301.38" y="-852.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">switchDeckBuilderView</text>
</g>
<g id="clust32" class="cluster">
<title>cluster_src/client/application/commands/updateAvailableFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1150.12,-873.28C1150.12,-873.28 1452.25,-873.28 1452.25,-873.28 1458.25,-873.28 1464.25,-879.28 1464.25,-885.28 1464.25,-885.28 1464.25,-914.28 1464.25,-914.28 1464.25,-920.28 1458.25,-926.28 1452.25,-926.28 1452.25,-926.28 1150.12,-926.28 1150.12,-926.28 1144.12,-926.28 1138.12,-920.28 1138.12,-914.28 1138.12,-914.28 1138.12,-885.28 1138.12,-885.28 1138.12,-879.28 1144.12,-873.28 1150.12,-873.28"/>
<text text-anchor="middle" x="1301.19" y="-913.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">updateAvailableFilters</text>
</g>
<g id="clust33" class="cluster">
<title>cluster_src/client/application/queries</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1315.12,-1449.28C1315.12,-1449.28 1601.25,-1449.28 1601.25,-1449.28 1607.25,-1449.28 1613.25,-1455.28 1613.25,-1461.28 1613.25,-1461.28 1613.25,-2623.28 1613.25,-2623.28 1613.25,-2629.28 1607.25,-2635.28 1601.25,-2635.28 1601.25,-2635.28 1315.12,-2635.28 1315.12,-2635.28 1309.12,-2635.28 1303.12,-2629.28 1303.12,-2623.28 1303.12,-2623.28 1303.12,-1461.28 1303.12,-1461.28 1303.12,-1455.28 1309.12,-1449.28 1315.12,-1449.28"/>
<text text-anchor="middle" x="1458.19" y="-2622.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">queries</text>
</g>
<g id="clust34" class="cluster">
<title>cluster_src/client/application/queries/applyFilterToCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1503.5,-2067.28C1503.5,-2067.28 1593.25,-2067.28 1593.25,-2067.28 1599.25,-2067.28 1605.25,-2073.28 1605.25,-2079.28 1605.25,-2079.28 1605.25,-2108.28 1605.25,-2108.28 1605.25,-2114.28 1599.25,-2120.28 1593.25,-2120.28 1593.25,-2120.28 1503.5,-2120.28 1503.5,-2120.28 1497.5,-2120.28 1491.5,-2114.28 1491.5,-2108.28 1491.5,-2108.28 1491.5,-2079.28 1491.5,-2079.28 1491.5,-2073.28 1497.5,-2067.28 1503.5,-2067.28"/>
<text text-anchor="middle" x="1548.38" y="-2107.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">applyFilterToCard</text>
</g>
<g id="clust35" class="cluster">
<title>cluster_src/client/application/queries/findDeckCardById</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1335.12,-1457.28C1335.12,-1457.28 1426.38,-1457.28 1426.38,-1457.28 1432.38,-1457.28 1438.38,-1463.28 1438.38,-1469.28 1438.38,-1469.28 1438.38,-1498.28 1438.38,-1498.28 1438.38,-1504.28 1432.38,-1510.28 1426.38,-1510.28 1426.38,-1510.28 1335.12,-1510.28 1335.12,-1510.28 1329.12,-1510.28 1323.12,-1504.28 1323.12,-1498.28 1323.12,-1498.28 1323.12,-1469.28 1323.12,-1469.28 1323.12,-1463.28 1329.12,-1457.28 1335.12,-1457.28"/>
<text text-anchor="middle" x="1380.75" y="-1497.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">findDeckCardById</text>
</g>
<g id="clust36" class="cluster">
<title>cluster_src/client/application/queries/getActiveFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1341.12,-1640.28C1341.12,-1640.28 1420.38,-1640.28 1420.38,-1640.28 1426.38,-1640.28 1432.38,-1646.28 1432.38,-1652.28 1432.38,-1652.28 1432.38,-1681.28 1432.38,-1681.28 1432.38,-1687.28 1426.38,-1693.28 1420.38,-1693.28 1420.38,-1693.28 1341.12,-1693.28 1341.12,-1693.28 1335.12,-1693.28 1329.12,-1687.28 1329.12,-1681.28 1329.12,-1681.28 1329.12,-1652.28 1329.12,-1652.28 1329.12,-1646.28 1335.12,-1640.28 1341.12,-1640.28"/>
<text text-anchor="middle" x="1380.75" y="-1680.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getActiveFilters</text>
</g>
<g id="clust37" class="cluster">
<title>cluster_src/client/application/queries/getAvailableFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1334.38,-1579.28C1334.38,-1579.28 1427.12,-1579.28 1427.12,-1579.28 1433.12,-1579.28 1439.12,-1585.28 1439.12,-1591.28 1439.12,-1591.28 1439.12,-1620.28 1439.12,-1620.28 1439.12,-1626.28 1433.12,-1632.28 1427.12,-1632.28 1427.12,-1632.28 1334.38,-1632.28 1334.38,-1632.28 1328.38,-1632.28 1322.38,-1626.28 1322.38,-1620.28 1322.38,-1620.28 1322.38,-1591.28 1322.38,-1591.28 1322.38,-1585.28 1328.38,-1579.28 1334.38,-1579.28"/>
<text text-anchor="middle" x="1380.75" y="-1619.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getAvailableFilters</text>
</g>
<g id="clust38" class="cluster">
<title>cluster_src/client/application/queries/getCardDetails</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1341.5,-1701.28C1341.5,-1701.28 1420,-1701.28 1420,-1701.28 1426,-1701.28 1432,-1707.28 1432,-1713.28 1432,-1713.28 1432,-1742.28 1432,-1742.28 1432,-1748.28 1426,-1754.28 1420,-1754.28 1420,-1754.28 1341.5,-1754.28 1341.5,-1754.28 1335.5,-1754.28 1329.5,-1748.28 1329.5,-1742.28 1329.5,-1742.28 1329.5,-1713.28 1329.5,-1713.28 1329.5,-1707.28 1335.5,-1701.28 1341.5,-1701.28"/>
<text text-anchor="middle" x="1380.75" y="-1741.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCardDetails</text>
</g>
<g id="clust39" class="cluster">
<title>cluster_src/client/application/queries/getCardsInDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1339.25,-1762.28C1339.25,-1762.28 1422.25,-1762.28 1422.25,-1762.28 1428.25,-1762.28 1434.25,-1768.28 1434.25,-1774.28 1434.25,-1774.28 1434.25,-1803.28 1434.25,-1803.28 1434.25,-1809.28 1428.25,-1815.28 1422.25,-1815.28 1422.25,-1815.28 1339.25,-1815.28 1339.25,-1815.28 1333.25,-1815.28 1327.25,-1809.28 1327.25,-1803.28 1327.25,-1803.28 1327.25,-1774.28 1327.25,-1774.28 1327.25,-1768.28 1333.25,-1762.28 1339.25,-1762.28"/>
<text text-anchor="middle" x="1380.75" y="-1802.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCardsInDeck</text>
</g>
<g id="clust40" class="cluster">
<title>cluster_src/client/application/queries/getCatalogCardById</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1330.62,-1518.28C1330.62,-1518.28 1430.88,-1518.28 1430.88,-1518.28 1436.88,-1518.28 1442.88,-1524.28 1442.88,-1530.28 1442.88,-1530.28 1442.88,-1559.28 1442.88,-1559.28 1442.88,-1565.28 1436.88,-1571.28 1430.88,-1571.28 1430.88,-1571.28 1330.62,-1571.28 1330.62,-1571.28 1324.62,-1571.28 1318.62,-1565.28 1318.62,-1559.28 1318.62,-1559.28 1318.62,-1530.28 1318.62,-1530.28 1318.62,-1524.28 1324.62,-1518.28 1330.62,-1518.28"/>
<text text-anchor="middle" x="1380.75" y="-1558.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCatalogCardById</text>
</g>
<g id="clust41" class="cluster">
<title>cluster_src/client/application/queries/getCatalogCards</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1337.38,-1823.28C1337.38,-1823.28 1424.12,-1823.28 1424.12,-1823.28 1430.12,-1823.28 1436.12,-1829.28 1436.12,-1835.28 1436.12,-1835.28 1436.12,-1864.28 1436.12,-1864.28 1436.12,-1870.28 1430.12,-1876.28 1424.12,-1876.28 1424.12,-1876.28 1337.38,-1876.28 1337.38,-1876.28 1331.38,-1876.28 1325.38,-1870.28 1325.38,-1864.28 1325.38,-1864.28 1325.38,-1835.28 1325.38,-1835.28 1325.38,-1829.28 1331.38,-1823.28 1337.38,-1823.28"/>
<text text-anchor="middle" x="1380.75" y="-1863.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCatalogCards</text>
</g>
<g id="clust42" class="cluster">
<title>cluster_src/client/application/queries/getCatalogError</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1340,-2006.28C1340,-2006.28 1421.5,-2006.28 1421.5,-2006.28 1427.5,-2006.28 1433.5,-2012.28 1433.5,-2018.28 1433.5,-2018.28 1433.5,-2047.28 1433.5,-2047.28 1433.5,-2053.28 1427.5,-2059.28 1421.5,-2059.28 1421.5,-2059.28 1340,-2059.28 1340,-2059.28 1334,-2059.28 1328,-2053.28 1328,-2047.28 1328,-2047.28 1328,-2018.28 1328,-2018.28 1328,-2012.28 1334,-2006.28 1340,-2006.28"/>
<text text-anchor="middle" x="1380.75" y="-2046.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCatalogError</text>
</g>
<g id="clust43" class="cluster">
<title>cluster_src/client/application/queries/getDeckBuilderView</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1331,-1884.28C1331,-1884.28 1430.5,-1884.28 1430.5,-1884.28 1436.5,-1884.28 1442.5,-1890.28 1442.5,-1896.28 1442.5,-1896.28 1442.5,-1925.28 1442.5,-1925.28 1442.5,-1931.28 1436.5,-1937.28 1430.5,-1937.28 1430.5,-1937.28 1331,-1937.28 1331,-1937.28 1325,-1937.28 1319,-1931.28 1319,-1925.28 1319,-1925.28 1319,-1896.28 1319,-1896.28 1319,-1890.28 1325,-1884.28 1331,-1884.28"/>
<text text-anchor="middle" x="1380.75" y="-1924.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getDeckBuilderView</text>
</g>
<g id="clust44" class="cluster">
<title>cluster_src/client/application/queries/getDeckName</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1343,-1945.28C1343,-1945.28 1418.5,-1945.28 1418.5,-1945.28 1424.5,-1945.28 1430.5,-1951.28 1430.5,-1957.28 1430.5,-1957.28 1430.5,-1986.28 1430.5,-1986.28 1430.5,-1992.28 1424.5,-1998.28 1418.5,-1998.28 1418.5,-1998.28 1343,-1998.28 1343,-1998.28 1337,-1998.28 1331,-1992.28 1331,-1986.28 1331,-1986.28 1331,-1957.28 1331,-1957.28 1331,-1951.28 1337,-1945.28 1343,-1945.28"/>
<text text-anchor="middle" x="1380.75" y="-1985.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getDeckName</text>
</g>
<g id="clust45" class="cluster">
<title>cluster_src/client/application/queries/getFilteredCards</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1338.12,-2067.28C1338.12,-2067.28 1423.38,-2067.28 1423.38,-2067.28 1429.38,-2067.28 1435.38,-2073.28 1435.38,-2079.28 1435.38,-2079.28 1435.38,-2108.28 1435.38,-2108.28 1435.38,-2114.28 1429.38,-2120.28 1423.38,-2120.28 1423.38,-2120.28 1338.12,-2120.28 1338.12,-2120.28 1332.12,-2120.28 1326.12,-2114.28 1326.12,-2108.28 1326.12,-2108.28 1326.12,-2079.28 1326.12,-2079.28 1326.12,-2073.28 1332.12,-2067.28 1338.12,-2067.28"/>
<text text-anchor="middle" x="1380.75" y="-2107.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getFilteredCards</text>
</g>
<g id="clust46" class="cluster">
<title>cluster_src/client/application/queries/getGameSettings</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1337,-2189.28C1337,-2189.28 1424.5,-2189.28 1424.5,-2189.28 1430.5,-2189.28 1436.5,-2195.28 1436.5,-2201.28 1436.5,-2201.28 1436.5,-2230.28 1436.5,-2230.28 1436.5,-2236.28 1430.5,-2242.28 1424.5,-2242.28 1424.5,-2242.28 1337,-2242.28 1337,-2242.28 1331,-2242.28 1325,-2236.28 1325,-2230.28 1325,-2230.28 1325,-2201.28 1325,-2201.28 1325,-2195.28 1331,-2189.28 1337,-2189.28"/>
<text text-anchor="middle" x="1380.75" y="-2229.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getGameSettings</text>
</g>
<g id="clust47" class="cluster">
<title>cluster_src/client/application/queries/getGameSettingsError</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1327.25,-2250.28C1327.25,-2250.28 1434.25,-2250.28 1434.25,-2250.28 1440.25,-2250.28 1446.25,-2256.28 1446.25,-2262.28 1446.25,-2262.28 1446.25,-2291.28 1446.25,-2291.28 1446.25,-2297.28 1440.25,-2303.28 1434.25,-2303.28 1434.25,-2303.28 1327.25,-2303.28 1327.25,-2303.28 1321.25,-2303.28 1315.25,-2297.28 1315.25,-2291.28 1315.25,-2291.28 1315.25,-2262.28 1315.25,-2262.28 1315.25,-2256.28 1321.25,-2250.28 1327.25,-2250.28"/>
<text text-anchor="middle" x="1380.75" y="-2290.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getGameSettingsError</text>
</g>
<g id="clust48" class="cluster">
<title>cluster_src/client/application/queries/getMaxCardsInDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1330.62,-2311.28C1330.62,-2311.28 1430.88,-2311.28 1430.88,-2311.28 1436.88,-2311.28 1442.88,-2317.28 1442.88,-2323.28 1442.88,-2323.28 1442.88,-2352.28 1442.88,-2352.28 1442.88,-2358.28 1436.88,-2364.28 1430.88,-2364.28 1430.88,-2364.28 1330.62,-2364.28 1330.62,-2364.28 1324.62,-2364.28 1318.62,-2358.28 1318.62,-2352.28 1318.62,-2352.28 1318.62,-2323.28 1318.62,-2323.28 1318.62,-2317.28 1324.62,-2311.28 1330.62,-2311.28"/>
<text text-anchor="middle" x="1380.75" y="-2351.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getMaxCardsInDeck</text>
</g>
<g id="clust49" class="cluster">
<title>cluster_src/client/application/queries/getSearchTerm</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1341.12,-2433.28C1341.12,-2433.28 1420.38,-2433.28 1420.38,-2433.28 1426.38,-2433.28 1432.38,-2439.28 1432.38,-2445.28 1432.38,-2445.28 1432.38,-2474.28 1432.38,-2474.28 1432.38,-2480.28 1426.38,-2486.28 1420.38,-2486.28 1420.38,-2486.28 1341.12,-2486.28 1341.12,-2486.28 1335.12,-2486.28 1329.12,-2480.28 1329.12,-2474.28 1329.12,-2474.28 1329.12,-2445.28 1329.12,-2445.28 1329.12,-2439.28 1335.12,-2433.28 1341.12,-2433.28"/>
<text text-anchor="middle" x="1380.75" y="-2473.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getSearchTerm</text>
</g>
<g id="clust50" class="cluster">
<title>cluster_src/client/application/queries/getTotalCardsInDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1329.5,-2494.28C1329.5,-2494.28 1432,-2494.28 1432,-2494.28 1438,-2494.28 1444,-2500.28 1444,-2506.28 1444,-2506.28 1444,-2535.28 1444,-2535.28 1444,-2541.28 1438,-2547.28 1432,-2547.28 1432,-2547.28 1329.5,-2547.28 1329.5,-2547.28 1323.5,-2547.28 1317.5,-2541.28 1317.5,-2535.28 1317.5,-2535.28 1317.5,-2506.28 1317.5,-2506.28 1317.5,-2500.28 1323.5,-2494.28 1329.5,-2494.28"/>
<text text-anchor="middle" x="1380.75" y="-2534.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getTotalCardsInDeck</text>
</g>
<g id="clust51" class="cluster">
<title>cluster_src/client/application/queries/hasDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1344.5,-2128.28C1344.5,-2128.28 1417,-2128.28 1417,-2128.28 1423,-2128.28 1429,-2134.28 1429,-2140.28 1429,-2140.28 1429,-2169.28 1429,-2169.28 1429,-2175.28 1423,-2181.28 1417,-2181.28 1417,-2181.28 1344.5,-2181.28 1344.5,-2181.28 1338.5,-2181.28 1332.5,-2175.28 1332.5,-2169.28 1332.5,-2169.28 1332.5,-2140.28 1332.5,-2140.28 1332.5,-2134.28 1338.5,-2128.28 1344.5,-2128.28"/>
<text text-anchor="middle" x="1380.75" y="-2168.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hasDeckDraft</text>
</g>
<g id="clust52" class="cluster">
<title>cluster_src/client/application/queries/isCatalogLoading</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1335.88,-2372.28C1335.88,-2372.28 1425.62,-2372.28 1425.62,-2372.28 1431.62,-2372.28 1437.62,-2378.28 1437.62,-2384.28 1437.62,-2384.28 1437.62,-2413.28 1437.62,-2413.28 1437.62,-2419.28 1431.62,-2425.28 1425.62,-2425.28 1425.62,-2425.28 1335.88,-2425.28 1335.88,-2425.28 1329.88,-2425.28 1323.88,-2419.28 1323.88,-2413.28 1323.88,-2413.28 1323.88,-2384.28 1323.88,-2384.28 1323.88,-2378.28 1329.88,-2372.28 1335.88,-2372.28"/>
<text text-anchor="middle" x="1380.75" y="-2412.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">isCatalogLoading</text>
</g>
<g id="clust53" class="cluster">
<title>cluster_src/client/application/queries/isGameSettingsLoading</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1323.12,-2555.28C1323.12,-2555.28 1438.38,-2555.28 1438.38,-2555.28 1444.38,-2555.28 1450.38,-2561.28 1450.38,-2567.28 1450.38,-2567.28 1450.38,-2596.28 1450.38,-2596.28 1450.38,-2602.28 1444.38,-2608.28 1438.38,-2608.28 1438.38,-2608.28 1323.12,-2608.28 1323.12,-2608.28 1317.12,-2608.28 1311.12,-2602.28 1311.12,-2596.28 1311.12,-2596.28 1311.12,-2567.28 1311.12,-2567.28 1311.12,-2561.28 1317.12,-2555.28 1323.12,-2555.28"/>
<text text-anchor="middle" x="1380.75" y="-2595.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">isGameSettingsLoading</text>
</g>
<g id="clust54" class="cluster">
<title>cluster_src/client/application/services</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1642.12,-2713.28C1642.12,-2713.28 1746.38,-2713.28 1746.38,-2713.28 1752.38,-2713.28 1758.38,-2719.28 1758.38,-2725.28 1758.38,-2725.28 1758.38,-2815.28 1758.38,-2815.28 1758.38,-2821.28 1752.38,-2827.28 1746.38,-2827.28 1746.38,-2827.28 1642.12,-2827.28 1642.12,-2827.28 1636.12,-2827.28 1630.12,-2821.28 1630.12,-2815.28 1630.12,-2815.28 1630.12,-2725.28 1630.12,-2725.28 1630.12,-2719.28 1636.12,-2713.28 1642.12,-2713.28"/>
<text text-anchor="middle" x="1694.25" y="-2814.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">services</text>
</g>
<g id="clust55" class="cluster">
<title>cluster_src/client/application/services/DeckDraftService</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1650.12,-2721.28C1650.12,-2721.28 1738.38,-2721.28 1738.38,-2721.28 1744.38,-2721.28 1750.38,-2727.28 1750.38,-2733.28 1750.38,-2733.28 1750.38,-2762.28 1750.38,-2762.28 1750.38,-2768.28 1744.38,-2774.28 1738.38,-2774.28 1738.38,-2774.28 1650.12,-2774.28 1650.12,-2774.28 1644.12,-2774.28 1638.12,-2768.28 1638.12,-2762.28 1638.12,-2762.28 1638.12,-2733.28 1638.12,-2733.28 1638.12,-2727.28 1644.12,-2721.28 1650.12,-2721.28"/>
<text text-anchor="middle" x="1694.25" y="-2761.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckDraftService</text>
</g>
<g id="clust56" class="cluster">
<title>cluster_src/client/application/store</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1521.12,-2643.28C1521.12,-2643.28 1575.62,-2643.28 1575.62,-2643.28 1581.62,-2643.28 1587.62,-2649.28 1587.62,-2655.28 1587.62,-2655.28 1587.62,-2684.28 1587.62,-2684.28 1587.62,-2690.28 1581.62,-2696.28 1575.62,-2696.28 1575.62,-2696.28 1521.12,-2696.28 1521.12,-2696.28 1515.12,-2696.28 1509.12,-2690.28 1509.12,-2684.28 1509.12,-2684.28 1509.12,-2655.28 1509.12,-2655.28 1509.12,-2649.28 1515.12,-2643.28 1521.12,-2643.28"/>
<text text-anchor="middle" x="1548.38" y="-2683.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">store</text>
</g>
<g id="clust57" class="cluster">
<title>cluster_src/client/application/subscribers</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M943.75,-1984.28C943.75,-1984.28 1050.75,-1984.28 1050.75,-1984.28 1056.75,-1984.28 1062.75,-1990.28 1062.75,-1996.28 1062.75,-1996.28 1062.75,-2025.28 1062.75,-2025.28 1062.75,-2031.28 1056.75,-2037.28 1050.75,-2037.28 1050.75,-2037.28 943.75,-2037.28 943.75,-2037.28 937.75,-2037.28 931.75,-2031.28 931.75,-2025.28 931.75,-2025.28 931.75,-1996.28 931.75,-1996.28 931.75,-1990.28 937.75,-1984.28 943.75,-1984.28"/>
<text text-anchor="middle" x="997.25" y="-2024.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">subscribers</text>
</g>
<g id="clust58" class="cluster">
<title>cluster_src/client/domain</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1486.5,-186.28C1486.5,-186.28 2095.88,-186.28 2095.88,-186.28 2101.88,-186.28 2107.88,-192.28 2107.88,-198.28 2107.88,-198.28 2107.88,-593.28 2107.88,-593.28 2107.88,-599.28 2101.88,-605.28 2095.88,-605.28 2095.88,-605.28 1486.5,-605.28 1486.5,-605.28 1480.5,-605.28 1474.5,-599.28 1474.5,-593.28 1474.5,-593.28 1474.5,-198.28 1474.5,-198.28 1474.5,-192.28 1480.5,-186.28 1486.5,-186.28"/>
<text text-anchor="middle" x="1791.19" y="-592.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">domain</text>
</g>
<g id="clust59" class="cluster">
<title>cluster_src/client/domain/CardData</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M2028.88,-525.28C2028.88,-525.28 2087.88,-525.28 2087.88,-525.28 2093.88,-525.28 2099.88,-531.28 2099.88,-537.28 2099.88,-537.28 2099.88,-566.28 2099.88,-566.28 2099.88,-572.28 2093.88,-578.28 2087.88,-578.28 2087.88,-578.28 2028.88,-578.28 2028.88,-578.28 2022.88,-578.28 2016.88,-572.28 2016.88,-566.28 2016.88,-566.28 2016.88,-537.28 2016.88,-537.28 2016.88,-531.28 2022.88,-525.28 2028.88,-525.28"/>
<text text-anchor="middle" x="2058.38" y="-565.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">CardData</text>
</g>
<g id="clust60" class="cluster">
<title>cluster_src/client/domain/Catalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1638.88,-361.28C1638.88,-361.28 2081.38,-361.28 2081.38,-361.28 2087.38,-361.28 2093.38,-367.28 2093.38,-373.28 2093.38,-373.28 2093.38,-505.28 2093.38,-505.28 2093.38,-511.28 2087.38,-517.28 2081.38,-517.28 2081.38,-517.28 1638.88,-517.28 1638.88,-517.28 1632.88,-517.28 1626.88,-511.28 1626.88,-505.28 1626.88,-505.28 1626.88,-373.28 1626.88,-373.28 1626.88,-367.28 1632.88,-361.28 1638.88,-361.28"/>
<text text-anchor="middle" x="1860.12" y="-504.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Catalog</text>
</g>
<g id="clust61" class="cluster">
<title>cluster_src/client/domain/DeckBuilder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1515.12,-194.28C1515.12,-194.28 1996.88,-194.28 1996.88,-194.28 2002.88,-194.28 2008.88,-200.28 2008.88,-206.28 2008.88,-206.28 2008.88,-266.28 2008.88,-266.28 2008.88,-272.28 2002.88,-278.28 1996.88,-278.28 1996.88,-278.28 1515.12,-278.28 1515.12,-278.28 1509.12,-278.28 1503.12,-272.28 1503.12,-266.28 1503.12,-266.28 1503.12,-206.28 1503.12,-206.28 1503.12,-200.28 1509.12,-194.28 1515.12,-194.28"/>
<text text-anchor="middle" x="1756" y="-265.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilder</text>
</g>
<g id="clust62" class="cluster">
<title>cluster_src/client/domain/GameSettings</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1494.5,-286.28C1494.5,-286.28 1868.88,-286.28 1868.88,-286.28 1874.88,-286.28 1880.88,-292.28 1880.88,-298.28 1880.88,-298.28 1880.88,-341.28 1880.88,-341.28 1880.88,-347.28 1874.88,-353.28 1868.88,-353.28 1868.88,-353.28 1494.5,-353.28 1494.5,-353.28 1488.5,-353.28 1482.5,-347.28 1482.5,-341.28 1482.5,-341.28 1482.5,-298.28 1482.5,-298.28 1482.5,-292.28 1488.5,-286.28 1494.5,-286.28"/>
<text text-anchor="middle" x="1681.69" y="-340.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameSettings</text>
</g>
<g id="clust63" class="cluster">
<title>cluster_src/client/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M208.25,-2990.28C208.25,-2990.28 1281,-2990.28 1281,-2990.28 1287,-2990.28 1293,-2996.28 1293,-3002.28 1293,-3002.28 1293,-5339.28 1293,-5339.28 1293,-5345.28 1287,-5351.28 1281,-5351.28 1281,-5351.28 208.25,-5351.28 208.25,-5351.28 202.25,-5351.28 196.25,-5345.28 196.25,-5339.28 196.25,-5339.28 196.25,-3002.28 196.25,-3002.28 196.25,-2996.28 202.25,-2990.28 208.25,-2990.28"/>
<text text-anchor="middle" x="744.62" y="-5338.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust64" class="cluster">
<title>cluster_src/client/infrastructure/builders</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M969.25,-5209.28C969.25,-5209.28 1025.25,-5209.28 1025.25,-5209.28 1031.25,-5209.28 1037.25,-5215.28 1037.25,-5221.28 1037.25,-5221.28 1037.25,-5250.28 1037.25,-5250.28 1037.25,-5256.28 1031.25,-5262.28 1025.25,-5262.28 1025.25,-5262.28 969.25,-5262.28 969.25,-5262.28 963.25,-5262.28 957.25,-5256.28 957.25,-5250.28 957.25,-5250.28 957.25,-5221.28 957.25,-5221.28 957.25,-5215.28 963.25,-5209.28 969.25,-5209.28"/>
<text text-anchor="middle" x="997.25" y="-5249.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">builders</text>
</g>
<g id="clust65" class="cluster">
<title>cluster_src/client/infrastructure/components</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M430,-3765.28C430,-3765.28 1261.12,-3765.28 1261.12,-3765.28 1267.12,-3765.28 1273.12,-3771.28 1273.12,-3777.28 1273.12,-3777.28 1273.12,-5189.28 1273.12,-5189.28 1273.12,-5195.28 1267.12,-5201.28 1261.12,-5201.28 1261.12,-5201.28 430,-5201.28 430,-5201.28 424,-5201.28 418,-5195.28 418,-5189.28 418,-5189.28 418,-3777.28 418,-3777.28 418,-3771.28 424,-3765.28 430,-3765.28"/>
<text text-anchor="middle" x="845.56" y="-5188.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">components</text>
</g>
<g id="clust66" class="cluster">
<title>cluster_src/client/infrastructure/components/app</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M438,-4087.28C438,-4087.28 831.38,-4087.28 831.38,-4087.28 837.38,-4087.28 843.38,-4093.28 843.38,-4099.28 843.38,-4099.28 843.38,-5162.28 843.38,-5162.28 843.38,-5168.28 837.38,-5174.28 831.38,-5174.28 831.38,-5174.28 438,-5174.28 438,-5174.28 432,-5174.28 426,-5168.28 426,-5162.28 426,-5162.28 426,-4099.28 426,-4099.28 426,-4093.28 432,-4087.28 438,-4087.28"/>
<text text-anchor="middle" x="634.69" y="-5161.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">app</text>
</g>
<g id="clust67" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Auth</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M467.25,-4095.28C467.25,-4095.28 810,-4095.28 810,-4095.28 816,-4095.28 822,-4101.28 822,-4107.28 822,-4107.28 822,-4237.28 822,-4237.28 822,-4243.28 816,-4249.28 810,-4249.28 810,-4249.28 467.25,-4249.28 467.25,-4249.28 461.25,-4249.28 455.25,-4243.28 455.25,-4237.28 455.25,-4237.28 455.25,-4107.28 455.25,-4107.28 455.25,-4101.28 461.25,-4095.28 467.25,-4095.28"/>
<text text-anchor="middle" x="638.62" y="-4236.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Auth</text>
</g>
<g id="clust68" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Auth/SignIn</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M475.25,-4103.28C475.25,-4103.28 802,-4103.28 802,-4103.28 808,-4103.28 814,-4109.28 814,-4115.28 814,-4115.28 814,-4210.28 814,-4210.28 814,-4216.28 808,-4222.28 802,-4222.28 802,-4222.28 475.25,-4222.28 475.25,-4222.28 469.25,-4222.28 463.25,-4216.28 463.25,-4210.28 463.25,-4210.28 463.25,-4115.28 463.25,-4115.28 463.25,-4109.28 469.25,-4103.28 475.25,-4103.28"/>
<text text-anchor="middle" x="638.62" y="-4209.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignIn</text>
</g>
<g id="clust69" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Auth/SignIn/SignInButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M483.25,-4111.28C483.25,-4111.28 557.25,-4111.28 557.25,-4111.28 563.25,-4111.28 569.25,-4117.28 569.25,-4123.28 569.25,-4123.28 569.25,-4152.28 569.25,-4152.28 569.25,-4158.28 563.25,-4164.28 557.25,-4164.28 557.25,-4164.28 483.25,-4164.28 483.25,-4164.28 477.25,-4164.28 471.25,-4158.28 471.25,-4152.28 471.25,-4152.28 471.25,-4123.28 471.25,-4123.28 471.25,-4117.28 477.25,-4111.28 483.25,-4111.28"/>
<text text-anchor="middle" x="520.25" y="-4151.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignInButton</text>
</g>
<g id="clust70" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Auth/SignIn/SignInForm</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M710.25,-4111.28C710.25,-4111.28 794,-4111.28 794,-4111.28 800,-4111.28 806,-4117.28 806,-4123.28 806,-4123.28 806,-4183.28 806,-4183.28 806,-4189.28 800,-4195.28 794,-4195.28 794,-4195.28 710.25,-4195.28 710.25,-4195.28 704.25,-4195.28 698.25,-4189.28 698.25,-4183.28 698.25,-4183.28 698.25,-4123.28 698.25,-4123.28 698.25,-4117.28 704.25,-4111.28 710.25,-4111.28"/>
<text text-anchor="middle" x="752.12" y="-4182.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignInForm</text>
</g>
<g id="clust71" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Catalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M461.75,-4780.28C461.75,-4780.28 578.75,-4780.28 578.75,-4780.28 584.75,-4780.28 590.75,-4786.28 590.75,-4792.28 590.75,-4792.28 590.75,-4917.28 590.75,-4917.28 590.75,-4923.28 584.75,-4929.28 578.75,-4929.28 578.75,-4929.28 461.75,-4929.28 461.75,-4929.28 455.75,-4929.28 449.75,-4923.28 449.75,-4917.28 449.75,-4917.28 449.75,-4792.28 449.75,-4792.28 449.75,-4786.28 455.75,-4780.28 461.75,-4780.28"/>
<text text-anchor="middle" x="520.25" y="-4916.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Catalog</text>
</g>
<g id="clust72" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Catalog/GameDetailsButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M469.75,-4788.28C469.75,-4788.28 570.75,-4788.28 570.75,-4788.28 576.75,-4788.28 582.75,-4794.28 582.75,-4800.28 582.75,-4800.28 582.75,-4829.28 582.75,-4829.28 582.75,-4835.28 576.75,-4841.28 570.75,-4841.28 570.75,-4841.28 469.75,-4841.28 469.75,-4841.28 463.75,-4841.28 457.75,-4835.28 457.75,-4829.28 457.75,-4829.28 457.75,-4800.28 457.75,-4800.28 457.75,-4794.28 463.75,-4788.28 469.75,-4788.28"/>
<text text-anchor="middle" x="520.25" y="-4828.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameDetailsButton</text>
</g>
<g id="clust73" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Catalog/PlayGameButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M475,-4849.28C475,-4849.28 565.5,-4849.28 565.5,-4849.28 571.5,-4849.28 577.5,-4855.28 577.5,-4861.28 577.5,-4861.28 577.5,-4890.28 577.5,-4890.28 577.5,-4896.28 571.5,-4902.28 565.5,-4902.28 565.5,-4902.28 475,-4902.28 475,-4902.28 469,-4902.28 463,-4896.28 463,-4890.28 463,-4890.28 463,-4861.28 463,-4861.28 463,-4855.28 469,-4849.28 475,-4849.28"/>
<text text-anchor="middle" x="520.25" y="-4889.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">PlayGameButton</text>
</g>
<g id="clust74" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M446,-4257.28C446,-4257.28 823.38,-4257.28 823.38,-4257.28 829.38,-4257.28 835.38,-4263.28 835.38,-4269.28 835.38,-4269.28 835.38,-4760.28 835.38,-4760.28 835.38,-4766.28 829.38,-4772.28 823.38,-4772.28 823.38,-4772.28 446,-4772.28 446,-4772.28 440,-4772.28 434,-4766.28 434,-4760.28 434,-4760.28 434,-4269.28 434,-4269.28 434,-4263.28 440,-4257.28 446,-4257.28"/>
<text text-anchor="middle" x="634.69" y="-4759.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilding</text>
</g>
<g id="clust75" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M463.38,-4387.28C463.38,-4387.28 577.12,-4387.28 577.12,-4387.28 583.12,-4387.28 589.12,-4393.28 589.12,-4399.28 589.12,-4399.28 589.12,-4428.28 589.12,-4428.28 589.12,-4434.28 583.12,-4440.28 577.12,-4440.28 577.12,-4440.28 463.38,-4440.28 463.38,-4440.28 457.38,-4440.28 451.38,-4434.28 451.38,-4428.28 451.38,-4428.28 451.38,-4399.28 451.38,-4399.28 451.38,-4393.28 457.38,-4387.28 463.38,-4387.28"/>
<text text-anchor="middle" x="520.25" y="-4427.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilderCardsGrid</text>
</g>
<g id="clust76" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M468.62,-4326.28C468.62,-4326.28 571.88,-4326.28 571.88,-4326.28 577.88,-4326.28 583.88,-4332.28 583.88,-4338.28 583.88,-4338.28 583.88,-4367.28 583.88,-4367.28 583.88,-4373.28 577.88,-4379.28 571.88,-4379.28 571.88,-4379.28 468.62,-4379.28 468.62,-4379.28 462.62,-4379.28 456.62,-4373.28 456.62,-4367.28 456.62,-4367.28 456.62,-4338.28 456.62,-4338.28 456.62,-4332.28 462.62,-4326.28 468.62,-4326.28"/>
<text text-anchor="middle" x="520.25" y="-4366.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilderInitializer</text>
</g>
<g id="clust77" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M472.38,-4509.28C472.38,-4509.28 792.5,-4509.28 792.5,-4509.28 798.5,-4509.28 804.5,-4515.28 804.5,-4521.28 804.5,-4521.28 804.5,-4550.28 804.5,-4550.28 804.5,-4556.28 798.5,-4562.28 792.5,-4562.28 792.5,-4562.28 472.38,-4562.28 472.38,-4562.28 466.38,-4562.28 460.38,-4556.28 460.38,-4550.28 460.38,-4550.28 460.38,-4521.28 460.38,-4521.28 460.38,-4515.28 466.38,-4509.28 472.38,-4509.28"/>
<text text-anchor="middle" x="632.44" y="-4549.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilderPanel</text>
</g>
<g id="clust78" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M703.88,-4295.28C703.88,-4295.28 800.38,-4295.28 800.38,-4295.28 806.38,-4295.28 812.38,-4301.28 812.38,-4307.28 812.38,-4307.28 812.38,-4336.28 812.38,-4336.28 812.38,-4342.28 806.38,-4348.28 800.38,-4348.28 800.38,-4348.28 703.88,-4348.28 703.88,-4348.28 697.88,-4348.28 691.88,-4342.28 691.88,-4336.28 691.88,-4336.28 691.88,-4307.28 691.88,-4307.28 691.88,-4301.28 697.88,-4295.28 703.88,-4295.28"/>
<text text-anchor="middle" x="752.12" y="-4335.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuildingCard</text>
</g>
<g id="clust79" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M469.75,-4692.28C469.75,-4692.28 781.62,-4692.28 781.62,-4692.28 787.62,-4692.28 793.62,-4698.28 793.62,-4704.28 793.62,-4704.28 793.62,-4733.28 793.62,-4733.28 793.62,-4739.28 787.62,-4745.28 781.62,-4745.28 781.62,-4745.28 469.75,-4745.28 469.75,-4745.28 463.75,-4745.28 457.75,-4739.28 457.75,-4733.28 457.75,-4733.28 457.75,-4704.28 457.75,-4704.28 457.75,-4698.28 463.75,-4692.28 469.75,-4692.28"/>
<text text-anchor="middle" x="625.69" y="-4732.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuildingFilters</text>
</g>
<g id="clust80" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M454,-4448.28C454,-4448.28 586.5,-4448.28 586.5,-4448.28 592.5,-4448.28 598.5,-4454.28 598.5,-4460.28 598.5,-4460.28 598.5,-4489.28 598.5,-4489.28 598.5,-4495.28 592.5,-4501.28 586.5,-4501.28 586.5,-4501.28 454,-4501.28 454,-4501.28 448,-4501.28 442,-4495.28 442,-4489.28 442,-4489.28 442,-4460.28 442,-4460.28 442,-4454.28 448,-4448.28 454,-4448.28"/>
<text text-anchor="middle" x="520.25" y="-4488.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuildingSkeletonCard</text>
</g>
<g id="clust81" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M693,-4570.28C693,-4570.28 811.25,-4570.28 811.25,-4570.28 817.25,-4570.28 823.25,-4576.28 823.25,-4582.28 823.25,-4582.28 823.25,-4611.28 823.25,-4611.28 823.25,-4617.28 817.25,-4623.28 811.25,-4623.28 811.25,-4623.28 693,-4623.28 693,-4623.28 687,-4623.28 681,-4617.28 681,-4611.28 681,-4611.28 681,-4582.28 681,-4582.28 681,-4576.28 687,-4570.28 693,-4570.28"/>
<text text-anchor="middle" x="752.12" y="-4610.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckCardDetailsDialog</text>
</g>
<g id="clust82" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M471.25,-4265.28C471.25,-4265.28 569.25,-4265.28 569.25,-4265.28 575.25,-4265.28 581.25,-4271.28 581.25,-4277.28 581.25,-4277.28 581.25,-4306.28 581.25,-4306.28 581.25,-4312.28 575.25,-4318.28 569.25,-4318.28 569.25,-4318.28 471.25,-4318.28 471.25,-4318.28 465.25,-4318.28 459.25,-4312.28 459.25,-4306.28 459.25,-4306.28 459.25,-4277.28 459.25,-4277.28 459.25,-4271.28 465.25,-4265.28 471.25,-4265.28"/>
<text text-anchor="middle" x="520.25" y="-4305.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckDraftInitializer</text>
</g>
<g id="clust83" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M473.12,-4631.28C473.12,-4631.28 567.38,-4631.28 567.38,-4631.28 573.38,-4631.28 579.38,-4637.28 579.38,-4643.28 579.38,-4643.28 579.38,-4672.28 579.38,-4672.28 579.38,-4678.28 573.38,-4684.28 567.38,-4684.28 567.38,-4684.28 473.12,-4684.28 473.12,-4684.28 467.12,-4684.28 461.12,-4678.28 461.12,-4672.28 461.12,-4672.28 461.12,-4643.28 461.12,-4643.28 461.12,-4637.28 467.12,-4631.28 473.12,-4631.28"/>
<text text-anchor="middle" x="520.25" y="-4671.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">EditDeckInitializer</text>
</g>
<g id="clust84" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M706.88,-4631.28C706.88,-4631.28 797.38,-4631.28 797.38,-4631.28 803.38,-4631.28 809.38,-4637.28 809.38,-4643.28 809.38,-4643.28 809.38,-4672.28 809.38,-4672.28 809.38,-4678.28 803.38,-4684.28 797.38,-4684.28 797.38,-4684.28 706.88,-4684.28 706.88,-4684.28 700.88,-4684.28 694.88,-4678.28 694.88,-4672.28 694.88,-4672.28 694.88,-4643.28 694.88,-4643.28 694.88,-4637.28 700.88,-4631.28 706.88,-4631.28"/>
<text text-anchor="middle" x="752.12" y="-4671.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SaveDeckDialog</text>
</g>
<g id="clust85" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M688.88,-4356.28C688.88,-4356.28 815.38,-4356.28 815.38,-4356.28 821.38,-4356.28 827.38,-4362.28 827.38,-4368.28 827.38,-4368.28 827.38,-4397.28 827.38,-4397.28 827.38,-4403.28 821.38,-4409.28 815.38,-4409.28 815.38,-4409.28 688.88,-4409.28 688.88,-4409.28 682.88,-4409.28 676.88,-4403.28 676.88,-4397.28 676.88,-4397.28 676.88,-4368.28 676.88,-4368.28 676.88,-4362.28 682.88,-4356.28 688.88,-4356.28"/>
<text text-anchor="middle" x="752.12" y="-4396.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">TotalCardsInDeckCounter</text>
</g>
<g id="clust86" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Gaming</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M463.25,-4937.28C463.25,-4937.28 577.25,-4937.28 577.25,-4937.28 583.25,-4937.28 589.25,-4943.28 589.25,-4949.28 589.25,-4949.28 589.25,-5135.28 589.25,-5135.28 589.25,-5141.28 583.25,-5147.28 577.25,-5147.28 577.25,-5147.28 463.25,-5147.28 463.25,-5147.28 457.25,-5147.28 451.25,-5141.28 451.25,-5135.28 451.25,-5135.28 451.25,-4949.28 451.25,-4949.28 451.25,-4943.28 457.25,-4937.28 463.25,-4937.28"/>
<text text-anchor="middle" x="520.25" y="-5134.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Gaming</text>
</g>
<g id="clust87" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Gaming/LeaveMatchButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M471.25,-4945.28C471.25,-4945.28 569.25,-4945.28 569.25,-4945.28 575.25,-4945.28 581.25,-4951.28 581.25,-4957.28 581.25,-4957.28 581.25,-4986.28 581.25,-4986.28 581.25,-4992.28 575.25,-4998.28 569.25,-4998.28 569.25,-4998.28 471.25,-4998.28 471.25,-4998.28 465.25,-4998.28 459.25,-4992.28 459.25,-4986.28 459.25,-4986.28 459.25,-4957.28 459.25,-4957.28 459.25,-4951.28 465.25,-4945.28 471.25,-4945.28"/>
<text text-anchor="middle" x="520.25" y="-4985.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">LeaveMatchButton</text>
</g>
<g id="clust88" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Gaming/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M497.12,-5067.28C497.12,-5067.28 543.38,-5067.28 543.38,-5067.28 549.38,-5067.28 555.38,-5073.28 555.38,-5079.28 555.38,-5079.28 555.38,-5108.28 555.38,-5108.28 555.38,-5114.28 549.38,-5120.28 543.38,-5120.28 543.38,-5120.28 497.12,-5120.28 497.12,-5120.28 491.12,-5120.28 485.12,-5114.28 485.12,-5108.28 485.12,-5108.28 485.12,-5079.28 485.12,-5079.28 485.12,-5073.28 491.12,-5067.28 497.12,-5067.28"/>
<text text-anchor="middle" x="520.25" y="-5107.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust89" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Gaming/StartGameButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M474.62,-5006.28C474.62,-5006.28 565.88,-5006.28 565.88,-5006.28 571.88,-5006.28 577.88,-5012.28 577.88,-5018.28 577.88,-5018.28 577.88,-5047.28 577.88,-5047.28 577.88,-5053.28 571.88,-5059.28 565.88,-5059.28 565.88,-5059.28 474.62,-5059.28 474.62,-5059.28 468.62,-5059.28 462.62,-5053.28 462.62,-5047.28 462.62,-5047.28 462.62,-5018.28 462.62,-5018.28 462.62,-5012.28 468.62,-5006.28 474.62,-5006.28"/>
<text text-anchor="middle" x="520.25" y="-5046.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">StartGameButton</text>
</g>
<g id="clust90" class="cluster">
<title>cluster_src/client/infrastructure/components/debug</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M443,-3869.28C443,-3869.28 597.5,-3869.28 597.5,-3869.28 603.5,-3869.28 609.5,-3875.28 609.5,-3881.28 609.5,-3881.28 609.5,-4067.28 609.5,-4067.28 609.5,-4073.28 603.5,-4079.28 597.5,-4079.28 597.5,-4079.28 443,-4079.28 443,-4079.28 437,-4079.28 431,-4073.28 431,-4067.28 431,-4067.28 431,-3881.28 431,-3881.28 431,-3875.28 437,-3869.28 443,-3869.28"/>
<text text-anchor="middle" x="520.25" y="-4066.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">debug</text>
</g>
<g id="clust91" class="cluster">
<title>cluster_src/client/infrastructure/components/debug/JsonObjectViewer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M472.75,-3877.28C472.75,-3877.28 567.75,-3877.28 567.75,-3877.28 573.75,-3877.28 579.75,-3883.28 579.75,-3889.28 579.75,-3889.28 579.75,-3918.28 579.75,-3918.28 579.75,-3924.28 573.75,-3930.28 567.75,-3930.28 567.75,-3930.28 472.75,-3930.28 472.75,-3930.28 466.75,-3930.28 460.75,-3924.28 460.75,-3918.28 460.75,-3918.28 460.75,-3889.28 460.75,-3889.28 460.75,-3883.28 466.75,-3877.28 472.75,-3877.28"/>
<text text-anchor="middle" x="520.25" y="-3917.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">JsonObjectViewer</text>
</g>
<g id="clust92" class="cluster">
<title>cluster_src/client/infrastructure/components/debug/MatchConsoleEvents</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M466,-3938.28C466,-3938.28 574.5,-3938.28 574.5,-3938.28 580.5,-3938.28 586.5,-3944.28 586.5,-3950.28 586.5,-3950.28 586.5,-3979.28 586.5,-3979.28 586.5,-3985.28 580.5,-3991.28 574.5,-3991.28 574.5,-3991.28 466,-3991.28 466,-3991.28 460,-3991.28 454,-3985.28 454,-3979.28 454,-3979.28 454,-3950.28 454,-3950.28 454,-3944.28 460,-3938.28 466,-3938.28"/>
<text text-anchor="middle" x="520.25" y="-3978.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchConsoleEvents</text>
</g>
<g id="clust93" class="cluster">
<title>cluster_src/client/infrastructure/components/debug/MatchMakingConsoleEvents</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M451,-3999.28C451,-3999.28 589.5,-3999.28 589.5,-3999.28 595.5,-3999.28 601.5,-4005.28 601.5,-4011.28 601.5,-4011.28 601.5,-4040.28 601.5,-4040.28 601.5,-4046.28 595.5,-4052.28 589.5,-4052.28 589.5,-4052.28 451,-4052.28 451,-4052.28 445,-4052.28 439,-4046.28 439,-4040.28 439,-4040.28 439,-4011.28 439,-4011.28 439,-4005.28 445,-3999.28 451,-3999.28"/>
<text text-anchor="middle" x="520.25" y="-4039.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchMakingConsoleEvents</text>
</g>
<g id="clust94" class="cluster">
<title>cluster_src/client/infrastructure/components/redirections</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M459.88,-3773.28C459.88,-3773.28 580.62,-3773.28 580.62,-3773.28 586.62,-3773.28 592.62,-3779.28 592.62,-3785.28 592.62,-3785.28 592.62,-3849.28 592.62,-3849.28 592.62,-3855.28 586.62,-3861.28 580.62,-3861.28 580.62,-3861.28 459.88,-3861.28 459.88,-3861.28 453.88,-3861.28 447.88,-3855.28 447.88,-3849.28 447.88,-3849.28 447.88,-3785.28 447.88,-3785.28 447.88,-3779.28 453.88,-3773.28 459.88,-3773.28"/>
<text text-anchor="middle" x="520.25" y="-3848.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">redirections</text>
</g>
<g id="clust95" class="cluster">
<title>cluster_src/client/infrastructure/components/redirections/RedirectToGameList</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M467.88,-3781.28C467.88,-3781.28 572.62,-3781.28 572.62,-3781.28 578.62,-3781.28 584.62,-3787.28 584.62,-3793.28 584.62,-3793.28 584.62,-3822.28 584.62,-3822.28 584.62,-3828.28 578.62,-3834.28 572.62,-3834.28 572.62,-3834.28 467.88,-3834.28 467.88,-3834.28 461.88,-3834.28 455.88,-3828.28 455.88,-3822.28 455.88,-3822.28 455.88,-3793.28 455.88,-3793.28 455.88,-3787.28 461.88,-3781.28 467.88,-3781.28"/>
<text text-anchor="middle" x="520.25" y="-3821.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">RedirectToGameList</text>
</g>
<g id="clust96" class="cluster">
<title>cluster_src/client/infrastructure/components/ui</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M947,-4012.28C947,-4012.28 1253.12,-4012.28 1253.12,-4012.28 1259.12,-4012.28 1265.12,-4018.28 1265.12,-4024.28 1265.12,-4024.28 1265.12,-4332.28 1265.12,-4332.28 1265.12,-4338.28 1259.12,-4344.28 1253.12,-4344.28 1253.12,-4344.28 947,-4344.28 947,-4344.28 941,-4344.28 935,-4338.28 935,-4332.28 935,-4332.28 935,-4024.28 935,-4024.28 935,-4018.28 941,-4012.28 947,-4012.28"/>
<text text-anchor="middle" x="1100.06" y="-4331.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ui</text>
</g>
<g id="clust97" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/Background</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M961.75,-4020.28C961.75,-4020.28 1032.75,-4020.28 1032.75,-4020.28 1038.75,-4020.28 1044.75,-4026.28 1044.75,-4032.28 1044.75,-4032.28 1044.75,-4061.28 1044.75,-4061.28 1044.75,-4067.28 1038.75,-4073.28 1032.75,-4073.28 1032.75,-4073.28 961.75,-4073.28 961.75,-4073.28 955.75,-4073.28 949.75,-4067.28 949.75,-4061.28 949.75,-4061.28 949.75,-4032.28 949.75,-4032.28 949.75,-4026.28 955.75,-4020.28 961.75,-4020.28"/>
<text text-anchor="middle" x="997.25" y="-4060.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Background</text>
</g>
<g id="clust98" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/ShiningButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M957.62,-4081.28C957.62,-4081.28 1245.12,-4081.28 1245.12,-4081.28 1251.12,-4081.28 1257.12,-4087.28 1257.12,-4093.28 1257.12,-4093.28 1257.12,-4122.28 1257.12,-4122.28 1257.12,-4128.28 1251.12,-4134.28 1245.12,-4134.28 1245.12,-4134.28 957.62,-4134.28 957.62,-4134.28 951.62,-4134.28 945.62,-4128.28 945.62,-4122.28 945.62,-4122.28 945.62,-4093.28 945.62,-4093.28 945.62,-4087.28 951.62,-4081.28 957.62,-4081.28"/>
<text text-anchor="middle" x="1101.38" y="-4121.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ShiningButton</text>
</g>
<g id="clust99" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/ShiningCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M960.62,-4142.28C960.62,-4142.28 1242.12,-4142.28 1242.12,-4142.28 1248.12,-4142.28 1254.12,-4148.28 1254.12,-4154.28 1254.12,-4154.28 1254.12,-4183.28 1254.12,-4183.28 1254.12,-4189.28 1248.12,-4195.28 1242.12,-4195.28 1242.12,-4195.28 960.62,-4195.28 960.62,-4195.28 954.62,-4195.28 948.62,-4189.28 948.62,-4183.28 948.62,-4183.28 948.62,-4154.28 948.62,-4154.28 948.62,-4148.28 954.62,-4142.28 960.62,-4142.28"/>
<text text-anchor="middle" x="1101.38" y="-4182.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ShiningCard</text>
</g>
<g id="clust100" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/SkeletonHelper</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M955,-4203.28C955,-4203.28 1039.5,-4203.28 1039.5,-4203.28 1045.5,-4203.28 1051.5,-4209.28 1051.5,-4215.28 1051.5,-4215.28 1051.5,-4244.28 1051.5,-4244.28 1051.5,-4250.28 1045.5,-4256.28 1039.5,-4256.28 1039.5,-4256.28 955,-4256.28 955,-4256.28 949,-4256.28 943,-4250.28 943,-4244.28 943,-4244.28 943,-4215.28 943,-4215.28 943,-4209.28 949,-4203.28 955,-4203.28"/>
<text text-anchor="middle" x="997.25" y="-4243.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SkeletonHelper</text>
</g>
<g id="clust101" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/Sparkles</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M968.5,-4264.28C968.5,-4264.28 1026,-4264.28 1026,-4264.28 1032,-4264.28 1038,-4270.28 1038,-4276.28 1038,-4276.28 1038,-4305.28 1038,-4305.28 1038,-4311.28 1032,-4317.28 1026,-4317.28 1026,-4317.28 968.5,-4317.28 968.5,-4317.28 962.5,-4317.28 956.5,-4311.28 956.5,-4305.28 956.5,-4305.28 956.5,-4276.28 956.5,-4276.28 956.5,-4270.28 962.5,-4264.28 968.5,-4264.28"/>
<text text-anchor="middle" x="997.25" y="-4304.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Sparkles</text>
</g>
<g id="clust102" class="cluster">
<title>cluster_src/client/infrastructure/hooks</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M903.12,-2998.28C903.12,-2998.28 1249.75,-2998.28 1249.75,-2998.28 1255.75,-2998.28 1261.75,-3004.28 1261.75,-3010.28 1261.75,-3010.28 1261.75,-3588.28 1261.75,-3588.28 1261.75,-3594.28 1255.75,-3600.28 1249.75,-3600.28 1249.75,-3600.28 903.12,-3600.28 903.12,-3600.28 897.12,-3600.28 891.12,-3594.28 891.12,-3588.28 891.12,-3588.28 891.12,-3010.28 891.12,-3010.28 891.12,-3004.28 897.12,-2998.28 903.12,-2998.28"/>
<text text-anchor="middle" x="1076.44" y="-3587.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hooks</text>
</g>
<g id="clust103" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useCatalogCardsByGameId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M941.75,-3067.28C941.75,-3067.28 1053.75,-3067.28 1053.75,-3067.28 1059.75,-3067.28 1065.75,-3073.28 1065.75,-3079.28 1065.75,-3079.28 1065.75,-3108.28 1065.75,-3108.28 1065.75,-3114.28 1059.75,-3120.28 1053.75,-3120.28 1053.75,-3120.28 941.75,-3120.28 941.75,-3120.28 935.75,-3120.28 929.75,-3114.28 929.75,-3108.28 929.75,-3108.28 929.75,-3079.28 929.75,-3079.28 929.75,-3073.28 935.75,-3067.28 941.75,-3067.28"/>
<text text-anchor="middle" x="997.75" y="-3107.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useCatalogCardsByGameId</text>
</g>
<g id="clust104" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useDebounce</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M974.25,-3128.28C974.25,-3128.28 1241.75,-3128.28 1241.75,-3128.28 1247.75,-3128.28 1253.75,-3134.28 1253.75,-3140.28 1253.75,-3140.28 1253.75,-3169.28 1253.75,-3169.28 1253.75,-3175.28 1247.75,-3181.28 1241.75,-3181.28 1241.75,-3181.28 974.25,-3181.28 974.25,-3181.28 968.25,-3181.28 962.25,-3175.28 962.25,-3169.28 962.25,-3169.28 962.25,-3140.28 962.25,-3140.28 962.25,-3134.28 968.25,-3128.28 974.25,-3128.28"/>
<text text-anchor="middle" x="1108" y="-3168.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDebounce</text>
</g>
<g id="clust105" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useDeckBuilder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M956.5,-3006.28C956.5,-3006.28 1038,-3006.28 1038,-3006.28 1044,-3006.28 1050,-3012.28 1050,-3018.28 1050,-3018.28 1050,-3047.28 1050,-3047.28 1050,-3053.28 1044,-3059.28 1038,-3059.28 1038,-3059.28 956.5,-3059.28 956.5,-3059.28 950.5,-3059.28 944.5,-3053.28 944.5,-3047.28 944.5,-3047.28 944.5,-3018.28 944.5,-3018.28 944.5,-3012.28 950.5,-3006.28 956.5,-3006.28"/>
<text text-anchor="middle" x="997.25" y="-3046.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDeckBuilder</text>
</g>
<g id="clust106" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useDeckById</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M959.5,-3189.28C959.5,-3189.28 1035,-3189.28 1035,-3189.28 1041,-3189.28 1047,-3195.28 1047,-3201.28 1047,-3201.28 1047,-3230.28 1047,-3230.28 1047,-3236.28 1041,-3242.28 1035,-3242.28 1035,-3242.28 959.5,-3242.28 959.5,-3242.28 953.5,-3242.28 947.5,-3236.28 947.5,-3230.28 947.5,-3230.28 947.5,-3201.28 947.5,-3201.28 947.5,-3195.28 953.5,-3189.28 959.5,-3189.28"/>
<text text-anchor="middle" x="997.25" y="-3229.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDeckById</text>
</g>
<g id="clust107" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useDeckId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M967,-3250.28C967,-3250.28 1027.5,-3250.28 1027.5,-3250.28 1033.5,-3250.28 1039.5,-3256.28 1039.5,-3262.28 1039.5,-3262.28 1039.5,-3291.28 1039.5,-3291.28 1039.5,-3297.28 1033.5,-3303.28 1027.5,-3303.28 1027.5,-3303.28 967,-3303.28 967,-3303.28 961,-3303.28 955,-3297.28 955,-3291.28 955,-3291.28 955,-3262.28 955,-3262.28 955,-3256.28 961,-3250.28 967,-3250.28"/>
<text text-anchor="middle" x="997.25" y="-3290.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDeckId</text>
</g>
<g id="clust108" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useGameId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M965.12,-3433.28C965.12,-3433.28 1029.38,-3433.28 1029.38,-3433.28 1035.38,-3433.28 1041.38,-3439.28 1041.38,-3445.28 1041.38,-3445.28 1041.38,-3474.28 1041.38,-3474.28 1041.38,-3480.28 1035.38,-3486.28 1029.38,-3486.28 1029.38,-3486.28 965.12,-3486.28 965.12,-3486.28 959.12,-3486.28 953.12,-3480.28 953.12,-3474.28 953.12,-3474.28 953.12,-3445.28 953.12,-3445.28 953.12,-3439.28 959.12,-3433.28 965.12,-3433.28"/>
<text text-anchor="middle" x="997.25" y="-3473.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useGameId</text>
</g>
<g id="clust109" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useGameSettingsByGameId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M928.75,-3520.28C928.75,-3520.28 1065.75,-3520.28 1065.75,-3520.28 1071.75,-3520.28 1077.75,-3526.28 1077.75,-3532.28 1077.75,-3532.28 1077.75,-3561.28 1077.75,-3561.28 1077.75,-3567.28 1071.75,-3573.28 1065.75,-3573.28 1065.75,-3573.28 928.75,-3573.28 928.75,-3573.28 922.75,-3573.28 916.75,-3567.28 916.75,-3561.28 916.75,-3561.28 916.75,-3532.28 916.75,-3532.28 916.75,-3526.28 922.75,-3520.28 928.75,-3520.28"/>
<text text-anchor="middle" x="997.25" y="-3560.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useGameSettingsByGameId</text>
</g>
<g id="clust110" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M911.12,-3311.28C911.12,-3311.28 1083.38,-3311.28 1083.38,-3311.28 1089.38,-3311.28 1095.38,-3317.28 1095.38,-3323.28 1095.38,-3323.28 1095.38,-3352.28 1095.38,-3352.28 1095.38,-3358.28 1089.38,-3364.28 1083.38,-3364.28 1083.38,-3364.28 911.12,-3364.28 911.12,-3364.28 905.12,-3364.28 899.12,-3358.28 899.12,-3352.28 899.12,-3352.28 899.12,-3323.28 899.12,-3323.28 899.12,-3317.28 905.12,-3311.28 911.12,-3311.28"/>
<text text-anchor="middle" x="997.25" y="-3351.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useInitializeDeckBuilderFromLocation</text>
</g>
<g id="clust111" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useLocale</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M967.38,-3372.28C967.38,-3372.28 1027.12,-3372.28 1027.12,-3372.28 1033.12,-3372.28 1039.12,-3378.28 1039.12,-3384.28 1039.12,-3384.28 1039.12,-3413.28 1039.12,-3413.28 1039.12,-3419.28 1033.12,-3425.28 1027.12,-3425.28 1027.12,-3425.28 967.38,-3425.28 967.38,-3425.28 961.38,-3425.28 955.38,-3419.28 955.38,-3413.28 955.38,-3413.28 955.38,-3384.28 955.38,-3384.28 955.38,-3378.28 961.38,-3372.28 967.38,-3372.28"/>
<text text-anchor="middle" x="997.25" y="-3412.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useLocale</text>
</g>
<g id="clust112" class="cluster">
<title>cluster_src/client/infrastructure/layouts</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M469.25,-3039.28C469.25,-3039.28 571.25,-3039.28 571.25,-3039.28 577.25,-3039.28 583.25,-3045.28 583.25,-3051.28 583.25,-3051.28 583.25,-3176.28 583.25,-3176.28 583.25,-3182.28 577.25,-3188.28 571.25,-3188.28 571.25,-3188.28 469.25,-3188.28 469.25,-3188.28 463.25,-3188.28 457.25,-3182.28 457.25,-3176.28 457.25,-3176.28 457.25,-3051.28 457.25,-3051.28 457.25,-3045.28 463.25,-3039.28 469.25,-3039.28"/>
<text text-anchor="middle" x="520.25" y="-3175.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">layouts</text>
</g>
<g id="clust113" class="cluster">
<title>cluster_src/client/infrastructure/layouts/FullPageLayout</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M477.25,-3108.28C477.25,-3108.28 563.25,-3108.28 563.25,-3108.28 569.25,-3108.28 575.25,-3114.28 575.25,-3120.28 575.25,-3120.28 575.25,-3149.28 575.25,-3149.28 575.25,-3155.28 569.25,-3161.28 563.25,-3161.28 563.25,-3161.28 477.25,-3161.28 477.25,-3161.28 471.25,-3161.28 465.25,-3155.28 465.25,-3149.28 465.25,-3149.28 465.25,-3120.28 465.25,-3120.28 465.25,-3114.28 471.25,-3108.28 477.25,-3108.28"/>
<text text-anchor="middle" x="520.25" y="-3148.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">FullPageLayout</text>
</g>
<g id="clust114" class="cluster">
<title>cluster_src/client/infrastructure/layouts/RootLayout</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M485.88,-3047.28C485.88,-3047.28 554.62,-3047.28 554.62,-3047.28 560.62,-3047.28 566.62,-3053.28 566.62,-3059.28 566.62,-3059.28 566.62,-3088.28 566.62,-3088.28 566.62,-3094.28 560.62,-3100.28 554.62,-3100.28 554.62,-3100.28 485.88,-3100.28 485.88,-3100.28 479.88,-3100.28 473.88,-3094.28 473.88,-3088.28 473.88,-3088.28 473.88,-3059.28 473.88,-3059.28 473.88,-3053.28 479.88,-3047.28 485.88,-3047.28"/>
<text text-anchor="middle" x="520.25" y="-3087.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">RootLayout</text>
</g>
<g id="clust115" class="cluster">
<title>cluster_src/client/infrastructure/lib</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1152,-5209.28C1152,-5209.28 1256.75,-5209.28 1256.75,-5209.28 1262.75,-5209.28 1268.75,-5215.28 1268.75,-5221.28 1268.75,-5221.28 1268.75,-5312.28 1268.75,-5312.28 1268.75,-5318.28 1262.75,-5324.28 1256.75,-5324.28 1256.75,-5324.28 1152,-5324.28 1152,-5324.28 1146,-5324.28 1140,-5318.28 1140,-5312.28 1140,-5312.28 1140,-5221.28 1140,-5221.28 1140,-5215.28 1146,-5209.28 1152,-5209.28"/>
<text text-anchor="middle" x="1204.38" y="-5311.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">lib</text>
</g>
<g id="clust116" class="cluster">
<title>cluster_src/client/infrastructure/pages</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M216.25,-3198.28C216.25,-3198.28 597.25,-3198.28 597.25,-3198.28 603.25,-3198.28 609.25,-3204.28 609.25,-3210.28 609.25,-3210.28 609.25,-3745.28 609.25,-3745.28 609.25,-3751.28 603.25,-3757.28 597.25,-3757.28 597.25,-3757.28 216.25,-3757.28 216.25,-3757.28 210.25,-3757.28 204.25,-3751.28 204.25,-3745.28 204.25,-3745.28 204.25,-3210.28 204.25,-3210.28 204.25,-3204.28 210.25,-3198.28 216.25,-3198.28"/>
<text text-anchor="middle" x="406.75" y="-3744.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">pages</text>
</g>
<g id="clust117" class="cluster">
<title>cluster_src/client/infrastructure/pages/Auth</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M224.25,-3363.28C224.25,-3363.28 342.75,-3363.28 342.75,-3363.28 348.75,-3363.28 354.75,-3369.28 354.75,-3375.28 354.75,-3375.28 354.75,-3500.28 354.75,-3500.28 354.75,-3506.28 348.75,-3512.28 342.75,-3512.28 342.75,-3512.28 224.25,-3512.28 224.25,-3512.28 218.25,-3512.28 212.25,-3506.28 212.25,-3500.28 212.25,-3500.28 212.25,-3375.28 212.25,-3375.28 212.25,-3369.28 218.25,-3363.28 224.25,-3363.28"/>
<text text-anchor="middle" x="283.5" y="-3499.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Auth</text>
</g>
<g id="clust118" class="cluster">
<title>cluster_src/client/infrastructure/pages/Auth/AccessDeniedPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M232.25,-3432.28C232.25,-3432.28 334.75,-3432.28 334.75,-3432.28 340.75,-3432.28 346.75,-3438.28 346.75,-3444.28 346.75,-3444.28 346.75,-3473.28 346.75,-3473.28 346.75,-3479.28 340.75,-3485.28 334.75,-3485.28 334.75,-3485.28 232.25,-3485.28 232.25,-3485.28 226.25,-3485.28 220.25,-3479.28 220.25,-3473.28 220.25,-3473.28 220.25,-3444.28 220.25,-3444.28 220.25,-3438.28 226.25,-3432.28 232.25,-3432.28"/>
<text text-anchor="middle" x="283.5" y="-3472.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AccessDeniedPage</text>
</g>
<g id="clust119" class="cluster">
<title>cluster_src/client/infrastructure/pages/Auth/SignInPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M248.75,-3371.28C248.75,-3371.28 318.25,-3371.28 318.25,-3371.28 324.25,-3371.28 330.25,-3377.28 330.25,-3383.28 330.25,-3383.28 330.25,-3412.28 330.25,-3412.28 330.25,-3418.28 324.25,-3424.28 318.25,-3424.28 318.25,-3424.28 248.75,-3424.28 248.75,-3424.28 242.75,-3424.28 236.75,-3418.28 236.75,-3412.28 236.75,-3412.28 236.75,-3383.28 236.75,-3383.28 236.75,-3377.28 242.75,-3371.28 248.75,-3371.28"/>
<text text-anchor="middle" x="283.5" y="-3411.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignInPage</text>
</g>
<g id="clust120" class="cluster">
<title>cluster_src/client/infrastructure/pages/Catalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M227.25,-3520.28C227.25,-3520.28 339.75,-3520.28 339.75,-3520.28 345.75,-3520.28 351.75,-3526.28 351.75,-3532.28 351.75,-3532.28 351.75,-3718.28 351.75,-3718.28 351.75,-3724.28 345.75,-3730.28 339.75,-3730.28 339.75,-3730.28 227.25,-3730.28 227.25,-3730.28 221.25,-3730.28 215.25,-3724.28 215.25,-3718.28 215.25,-3718.28 215.25,-3532.28 215.25,-3532.28 215.25,-3526.28 221.25,-3520.28 227.25,-3520.28"/>
<text text-anchor="middle" x="283.5" y="-3717.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Catalog</text>
</g>
<g id="clust121" class="cluster">
<title>cluster_src/client/infrastructure/pages/Catalog/GameDetailsPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M235.25,-3650.28C235.25,-3650.28 331.75,-3650.28 331.75,-3650.28 337.75,-3650.28 343.75,-3656.28 343.75,-3662.28 343.75,-3662.28 343.75,-3691.28 343.75,-3691.28 343.75,-3697.28 337.75,-3703.28 331.75,-3703.28 331.75,-3703.28 235.25,-3703.28 235.25,-3703.28 229.25,-3703.28 223.25,-3697.28 223.25,-3691.28 223.25,-3691.28 223.25,-3662.28 223.25,-3662.28 223.25,-3656.28 229.25,-3650.28 235.25,-3650.28"/>
<text text-anchor="middle" x="283.5" y="-3690.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameDetailsPage</text>
</g>
<g id="clust122" class="cluster">
<title>cluster_src/client/infrastructure/pages/Catalog/GameListPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M242.38,-3528.28C242.38,-3528.28 324.62,-3528.28 324.62,-3528.28 330.62,-3528.28 336.62,-3534.28 336.62,-3540.28 336.62,-3540.28 336.62,-3569.28 336.62,-3569.28 336.62,-3575.28 330.62,-3581.28 324.62,-3581.28 324.62,-3581.28 242.38,-3581.28 242.38,-3581.28 236.38,-3581.28 230.38,-3575.28 230.38,-3569.28 230.38,-3569.28 230.38,-3540.28 230.38,-3540.28 230.38,-3534.28 236.38,-3528.28 242.38,-3528.28"/>
<text text-anchor="middle" x="283.5" y="-3568.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameListPage</text>
</g>
<g id="clust123" class="cluster">
<title>cluster_src/client/infrastructure/pages/Catalog/PlayGamePage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M240.5,-3589.28C240.5,-3589.28 326.5,-3589.28 326.5,-3589.28 332.5,-3589.28 338.5,-3595.28 338.5,-3601.28 338.5,-3601.28 338.5,-3630.28 338.5,-3630.28 338.5,-3636.28 332.5,-3642.28 326.5,-3642.28 326.5,-3642.28 240.5,-3642.28 240.5,-3642.28 234.5,-3642.28 228.5,-3636.28 228.5,-3630.28 228.5,-3630.28 228.5,-3601.28 228.5,-3601.28 228.5,-3595.28 234.5,-3589.28 240.5,-3589.28"/>
<text text-anchor="middle" x="283.5" y="-3629.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">PlayGamePage</text>
</g>
<g id="clust124" class="cluster">
<title>cluster_src/client/infrastructure/pages/Gaming</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M241.5,-3206.28C241.5,-3206.28 589.25,-3206.28 589.25,-3206.28 595.25,-3206.28 601.25,-3212.28 601.25,-3218.28 601.25,-3218.28 601.25,-3343.28 601.25,-3343.28 601.25,-3349.28 595.25,-3355.28 589.25,-3355.28 589.25,-3355.28 241.5,-3355.28 241.5,-3355.28 235.5,-3355.28 229.5,-3349.28 229.5,-3343.28 229.5,-3343.28 229.5,-3218.28 229.5,-3218.28 229.5,-3212.28 235.5,-3206.28 241.5,-3206.28"/>
<text text-anchor="middle" x="415.38" y="-3342.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Gaming</text>
</g>
<g id="clust125" class="cluster">
<title>cluster_src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M459.25,-3214.28C459.25,-3214.28 581.25,-3214.28 581.25,-3214.28 587.25,-3214.28 593.25,-3220.28 593.25,-3226.28 593.25,-3226.28 593.25,-3255.28 593.25,-3255.28 593.25,-3261.28 587.25,-3267.28 581.25,-3267.28 581.25,-3267.28 459.25,-3267.28 459.25,-3267.28 453.25,-3267.28 447.25,-3261.28 447.25,-3255.28 447.25,-3255.28 447.25,-3226.28 447.25,-3226.28 447.25,-3220.28 453.25,-3214.28 459.25,-3214.28"/>
<text text-anchor="middle" x="520.25" y="-3254.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ErrorLoadingMatchPage</text>
</g>
<g id="clust126" class="cluster">
<title>cluster_src/client/infrastructure/pages/Gaming/FinishedMatchPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M468.62,-3275.28C468.62,-3275.28 571.88,-3275.28 571.88,-3275.28 577.88,-3275.28 583.88,-3281.28 583.88,-3287.28 583.88,-3287.28 583.88,-3316.28 583.88,-3316.28 583.88,-3322.28 577.88,-3328.28 571.88,-3328.28 571.88,-3328.28 468.62,-3328.28 468.62,-3328.28 462.62,-3328.28 456.62,-3322.28 456.62,-3316.28 456.62,-3316.28 456.62,-3287.28 456.62,-3287.28 456.62,-3281.28 462.62,-3275.28 468.62,-3275.28"/>
<text text-anchor="middle" x="520.25" y="-3315.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">FinishedMatchPage</text>
</g>
<g id="clust127" class="cluster">
<title>cluster_src/client/infrastructure/pages/Gaming/MatchPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M249.5,-3275.28C249.5,-3275.28 317.5,-3275.28 317.5,-3275.28 323.5,-3275.28 329.5,-3281.28 329.5,-3287.28 329.5,-3287.28 329.5,-3316.28 329.5,-3316.28 329.5,-3322.28 323.5,-3328.28 317.5,-3328.28 317.5,-3328.28 249.5,-3328.28 249.5,-3328.28 243.5,-3328.28 237.5,-3322.28 237.5,-3316.28 237.5,-3316.28 237.5,-3287.28 237.5,-3287.28 237.5,-3281.28 243.5,-3275.28 249.5,-3275.28"/>
<text text-anchor="middle" x="283.5" y="-3315.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchPage</text>
</g>
<g id="clust128" class="cluster">
<title>cluster_src/client/infrastructure/providers</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M696.75,-3457.28C696.75,-3457.28 807.5,-3457.28 807.5,-3457.28 813.5,-3457.28 819.5,-3463.28 819.5,-3469.28 819.5,-3469.28 819.5,-3560.28 819.5,-3560.28 819.5,-3566.28 813.5,-3572.28 807.5,-3572.28 807.5,-3572.28 696.75,-3572.28 696.75,-3572.28 690.75,-3572.28 684.75,-3566.28 684.75,-3560.28 684.75,-3560.28 684.75,-3469.28 684.75,-3469.28 684.75,-3463.28 690.75,-3457.28 696.75,-3457.28"/>
<text text-anchor="middle" x="752.12" y="-3559.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">providers</text>
</g>
<g id="clust129" class="cluster">
<title>cluster_src/client/infrastructure/services</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1135.75,-3608.28C1135.75,-3608.28 1273,-3608.28 1273,-3608.28 1279,-3608.28 1285,-3614.28 1285,-3620.28 1285,-3620.28 1285,-3745.28 1285,-3745.28 1285,-3751.28 1279,-3757.28 1273,-3757.28 1273,-3757.28 1135.75,-3757.28 1135.75,-3757.28 1129.75,-3757.28 1123.75,-3751.28 1123.75,-3745.28 1123.75,-3745.28 1123.75,-3620.28 1123.75,-3620.28 1123.75,-3614.28 1129.75,-3608.28 1135.75,-3608.28"/>
<text text-anchor="middle" x="1204.38" y="-3744.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">services</text>
</g>
<g id="clust130" class="cluster">
<title>cluster_src/client/infrastructure/services/deckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1143.75,-3616.28C1143.75,-3616.28 1265,-3616.28 1265,-3616.28 1271,-3616.28 1277,-3622.28 1277,-3628.28 1277,-3628.28 1277,-3657.28 1277,-3657.28 1277,-3663.28 1271,-3669.28 1265,-3669.28 1265,-3669.28 1143.75,-3669.28 1143.75,-3669.28 1137.75,-3669.28 1131.75,-3663.28 1131.75,-3657.28 1131.75,-3657.28 1131.75,-3628.28 1131.75,-3628.28 1131.75,-3622.28 1137.75,-3616.28 1143.75,-3616.28"/>
<text text-anchor="middle" x="1204.38" y="-3656.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">deckDraft</text>
</g>
<g id="clust131" class="cluster">
<title>cluster_src/client/infrastructure/services/location</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1146.38,-3677.28C1146.38,-3677.28 1262.38,-3677.28 1262.38,-3677.28 1268.38,-3677.28 1274.38,-3683.28 1274.38,-3689.28 1274.38,-3689.28 1274.38,-3718.28 1274.38,-3718.28 1274.38,-3724.28 1268.38,-3730.28 1262.38,-3730.28 1262.38,-3730.28 1146.38,-3730.28 1146.38,-3730.28 1140.38,-3730.28 1134.38,-3724.28 1134.38,-3718.28 1134.38,-3718.28 1134.38,-3689.28 1134.38,-3689.28 1134.38,-3683.28 1140.38,-3677.28 1146.38,-3677.28"/>
<text text-anchor="middle" x="1204.38" y="-3717.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">location</text>
</g>
<g id="clust132" class="cluster">
<title>cluster_src/client/infrastructure/store</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M974.25,-3612.28C974.25,-3612.28 1020.25,-3612.28 1020.25,-3612.28 1026.25,-3612.28 1032.25,-3618.28 1032.25,-3624.28 1032.25,-3624.28 1032.25,-3653.28 1032.25,-3653.28 1032.25,-3659.28 1026.25,-3665.28 1020.25,-3665.28 1020.25,-3665.28 974.25,-3665.28 974.25,-3665.28 968.25,-3665.28 962.25,-3659.28 962.25,-3653.28 962.25,-3653.28 962.25,-3624.28 962.25,-3624.28 962.25,-3618.28 968.25,-3612.28 974.25,-3612.28"/>
<text text-anchor="middle" x="997.25" y="-3652.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">store</text>
</g>
<g id="clust133" class="cluster">
<title>cluster_src/server</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M374.75,-5495.28C374.75,-5495.28 1278.75,-5495.28 1278.75,-5495.28 1284.75,-5495.28 1290.75,-5501.28 1290.75,-5507.28 1290.75,-5507.28 1290.75,-7305.28 1290.75,-7305.28 1290.75,-7311.28 1284.75,-7317.28 1278.75,-7317.28 1278.75,-7317.28 374.75,-7317.28 374.75,-7317.28 368.75,-7317.28 362.75,-7311.28 362.75,-7305.28 362.75,-7305.28 362.75,-5507.28 362.75,-5507.28 362.75,-5501.28 368.75,-5495.28 374.75,-5495.28"/>
<text text-anchor="middle" x="826.75" y="-7304.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">server</text>
</g>
<g id="clust134" class="cluster">
<title>cluster_src/server/application</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M382.75,-5553.28C382.75,-5553.28 873.5,-5553.28 873.5,-5553.28 879.5,-5553.28 885.5,-5559.28 885.5,-5565.28 885.5,-5565.28 885.5,-6505.28 885.5,-6505.28 885.5,-6511.28 879.5,-6517.28 873.5,-6517.28 873.5,-6517.28 382.75,-6517.28 382.75,-6517.28 376.75,-6517.28 370.75,-6511.28 370.75,-6505.28 370.75,-6505.28 370.75,-5565.28 370.75,-5565.28 370.75,-5559.28 376.75,-5553.28 382.75,-5553.28"/>
<text text-anchor="middle" x="628.12" y="-6504.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">application</text>
</g>
<g id="clust135" class="cluster">
<title>cluster_src/server/application/commands</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M390.75,-5561.28C390.75,-5561.28 865.5,-5561.28 865.5,-5561.28 871.5,-5561.28 877.5,-5567.28 877.5,-5573.28 877.5,-5573.28 877.5,-6169.28 877.5,-6169.28 877.5,-6175.28 871.5,-6181.28 865.5,-6181.28 865.5,-6181.28 390.75,-6181.28 390.75,-6181.28 384.75,-6181.28 378.75,-6175.28 378.75,-6169.28 378.75,-6169.28 378.75,-5573.28 378.75,-5573.28 378.75,-5567.28 384.75,-5561.28 390.75,-5561.28"/>
<text text-anchor="middle" x="628.12" y="-6168.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">commands</text>
</g>
<g id="clust136" class="cluster">
<title>cluster_src/server/application/commands/Deck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M445.25,-5569.28C445.25,-5569.28 811,-5569.28 811,-5569.28 817,-5569.28 823,-5575.28 823,-5581.28 823,-5581.28 823,-5645.28 823,-5645.28 823,-5651.28 817,-5657.28 811,-5657.28 811,-5657.28 445.25,-5657.28 445.25,-5657.28 439.25,-5657.28 433.25,-5651.28 433.25,-5645.28 433.25,-5645.28 433.25,-5581.28 433.25,-5581.28 433.25,-5575.28 439.25,-5569.28 445.25,-5569.28"/>
<text text-anchor="middle" x="628.12" y="-5644.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Deck</text>
</g>
<g id="clust137" class="cluster">
<title>cluster_src/server/application/commands/Deck/SaveDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M453.25,-5577.28C453.25,-5577.28 803,-5577.28 803,-5577.28 809,-5577.28 815,-5583.28 815,-5589.28 815,-5589.28 815,-5618.28 815,-5618.28 815,-5624.28 809,-5630.28 803,-5630.28 803,-5630.28 453.25,-5630.28 453.25,-5630.28 447.25,-5630.28 441.25,-5624.28 441.25,-5618.28 441.25,-5618.28 441.25,-5589.28 441.25,-5589.28 441.25,-5583.28 447.25,-5577.28 453.25,-5577.28"/>
<text text-anchor="middle" x="628.12" y="-5617.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SaveDeck</text>
</g>
<g id="clust138" class="cluster">
<title>cluster_src/server/application/commands/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M441.12,-5665.28C441.12,-5665.28 815.12,-5665.28 815.12,-5665.28 821.12,-5665.28 827.12,-5671.28 827.12,-5677.28 827.12,-5677.28 827.12,-5741.28 827.12,-5741.28 827.12,-5747.28 821.12,-5753.28 815.12,-5753.28 815.12,-5753.28 441.12,-5753.28 441.12,-5753.28 435.12,-5753.28 429.12,-5747.28 429.12,-5741.28 429.12,-5741.28 429.12,-5677.28 429.12,-5677.28 429.12,-5671.28 435.12,-5665.28 441.12,-5665.28"/>
<text text-anchor="middle" x="628.12" y="-5740.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust139" class="cluster">
<title>cluster_src/server/application/commands/Match/LeaveMatch</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M449.12,-5673.28C449.12,-5673.28 807.12,-5673.28 807.12,-5673.28 813.12,-5673.28 819.12,-5679.28 819.12,-5685.28 819.12,-5685.28 819.12,-5714.28 819.12,-5714.28 819.12,-5720.28 813.12,-5726.28 807.12,-5726.28 807.12,-5726.28 449.12,-5726.28 449.12,-5726.28 443.12,-5726.28 437.12,-5720.28 437.12,-5714.28 437.12,-5714.28 437.12,-5685.28 437.12,-5685.28 437.12,-5679.28 443.12,-5673.28 449.12,-5673.28"/>
<text text-anchor="middle" x="628.12" y="-5713.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">LeaveMatch</text>
</g>
<g id="clust140" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M398.75,-5761.28C398.75,-5761.28 857.5,-5761.28 857.5,-5761.28 863.5,-5761.28 869.5,-5767.28 869.5,-5773.28 869.5,-5773.28 869.5,-6142.28 869.5,-6142.28 869.5,-6148.28 863.5,-6154.28 857.5,-6154.28 857.5,-6154.28 398.75,-6154.28 398.75,-6154.28 392.75,-6154.28 386.75,-6148.28 386.75,-6142.28 386.75,-6142.28 386.75,-5773.28 386.75,-5773.28 386.75,-5767.28 392.75,-5761.28 398.75,-5761.28"/>
<text text-anchor="middle" x="628.12" y="-6141.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchMaking</text>
</g>
<g id="clust141" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M406.75,-5769.28C406.75,-5769.28 849.5,-5769.28 849.5,-5769.28 855.5,-5769.28 861.5,-5775.28 861.5,-5781.28 861.5,-5781.28 861.5,-5810.28 861.5,-5810.28 861.5,-5816.28 855.5,-5822.28 849.5,-5822.28 849.5,-5822.28 406.75,-5822.28 406.75,-5822.28 400.75,-5822.28 394.75,-5816.28 394.75,-5810.28 394.75,-5810.28 394.75,-5781.28 394.75,-5781.28 394.75,-5775.28 400.75,-5769.28 406.75,-5769.28"/>
<text text-anchor="middle" x="628.12" y="-5809.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AddPlayerToMatchMakingQueue</text>
</g>
<g id="clust142" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/CancelMatchRegistration</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M422.5,-5830.28C422.5,-5830.28 833.75,-5830.28 833.75,-5830.28 839.75,-5830.28 845.75,-5836.28 845.75,-5842.28 845.75,-5842.28 845.75,-5871.28 845.75,-5871.28 845.75,-5877.28 839.75,-5883.28 833.75,-5883.28 833.75,-5883.28 422.5,-5883.28 422.5,-5883.28 416.5,-5883.28 410.5,-5877.28 410.5,-5871.28 410.5,-5871.28 410.5,-5842.28 410.5,-5842.28 410.5,-5836.28 416.5,-5830.28 422.5,-5830.28"/>
<text text-anchor="middle" x="628.12" y="-5870.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">CancelMatchRegistration</text>
</g>
<g id="clust143" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M414.62,-5891.28C414.62,-5891.28 841.62,-5891.28 841.62,-5891.28 847.62,-5891.28 853.62,-5897.28 853.62,-5903.28 853.62,-5903.28 853.62,-5932.28 853.62,-5932.28 853.62,-5938.28 847.62,-5944.28 841.62,-5944.28 841.62,-5944.28 414.62,-5944.28 414.62,-5944.28 408.62,-5944.28 402.62,-5938.28 402.62,-5932.28 402.62,-5932.28 402.62,-5903.28 402.62,-5903.28 402.62,-5897.28 408.62,-5891.28 414.62,-5891.28"/>
<text text-anchor="middle" x="628.12" y="-5931.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">CleanUpMatchMakingQueue</text>
</g>
<g id="clust144" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/MakeMatch</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M450.62,-5952.28C450.62,-5952.28 805.62,-5952.28 805.62,-5952.28 811.62,-5952.28 817.62,-5958.28 817.62,-5964.28 817.62,-5964.28 817.62,-5993.28 817.62,-5993.28 817.62,-5999.28 811.62,-6005.28 805.62,-6005.28 805.62,-6005.28 450.62,-6005.28 450.62,-6005.28 444.62,-6005.28 438.62,-5999.28 438.62,-5993.28 438.62,-5993.28 438.62,-5964.28 438.62,-5964.28 438.62,-5958.28 444.62,-5952.28 450.62,-5952.28"/>
<text text-anchor="middle" x="628.12" y="-5992.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MakeMatch</text>
</g>
<g id="clust145" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/UpdatePlayersStatus</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M431.12,-6013.28C431.12,-6013.28 825.12,-6013.28 825.12,-6013.28 831.12,-6013.28 837.12,-6019.28 837.12,-6025.28 837.12,-6025.28 837.12,-6054.28 837.12,-6054.28 837.12,-6060.28 831.12,-6066.28 825.12,-6066.28 825.12,-6066.28 431.12,-6066.28 431.12,-6066.28 425.12,-6066.28 419.12,-6060.28 419.12,-6054.28 419.12,-6054.28 419.12,-6025.28 419.12,-6025.28 419.12,-6019.28 425.12,-6013.28 431.12,-6013.28"/>
<text text-anchor="middle" x="628.12" y="-6053.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">UpdatePlayersStatus</text>
</g>
<g id="clust146" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M420.25,-6074.28C420.25,-6074.28 836,-6074.28 836,-6074.28 842,-6074.28 848,-6080.28 848,-6086.28 848,-6086.28 848,-6115.28 848,-6115.28 848,-6121.28 842,-6127.28 836,-6127.28 836,-6127.28 420.25,-6127.28 420.25,-6127.28 414.25,-6127.28 408.25,-6121.28 408.25,-6115.28 408.25,-6115.28 408.25,-6086.28 408.25,-6086.28 408.25,-6080.28 414.25,-6074.28 420.25,-6074.28"/>
<text text-anchor="middle" x="628.12" y="-6114.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">UpdateSinglePlayerStatus</text>
</g>
<g id="clust147" class="cluster">
<title>cluster_src/server/application/ports</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M680.62,-6189.28C680.62,-6189.28 823.62,-6189.28 823.62,-6189.28 829.62,-6189.28 835.62,-6195.28 835.62,-6201.28 835.62,-6201.28 835.62,-6478.28 835.62,-6478.28 835.62,-6484.28 829.62,-6490.28 823.62,-6490.28 823.62,-6490.28 680.62,-6490.28 680.62,-6490.28 674.62,-6490.28 668.62,-6484.28 668.62,-6478.28 668.62,-6478.28 668.62,-6201.28 668.62,-6201.28 668.62,-6195.28 674.62,-6189.28 680.62,-6189.28"/>
<text text-anchor="middle" x="752.12" y="-6477.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ports</text>
</g>
<g id="clust148" class="cluster">
<title>cluster_src/server/application/queries</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M439.75,-6189.28C439.75,-6189.28 600.75,-6189.28 600.75,-6189.28 606.75,-6189.28 612.75,-6195.28 612.75,-6201.28 612.75,-6201.28 612.75,-6354.28 612.75,-6354.28 612.75,-6360.28 606.75,-6366.28 600.75,-6366.28 600.75,-6366.28 439.75,-6366.28 439.75,-6366.28 433.75,-6366.28 427.75,-6360.28 427.75,-6354.28 427.75,-6354.28 427.75,-6201.28 427.75,-6201.28 427.75,-6195.28 433.75,-6189.28 439.75,-6189.28"/>
<text text-anchor="middle" x="520.25" y="-6353.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">queries</text>
</g>
<g id="clust149" class="cluster">
<title>cluster_src/server/domain</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M917.25,-6289.28C917.25,-6289.28 1270.75,-6289.28 1270.75,-6289.28 1276.75,-6289.28 1282.75,-6295.28 1282.75,-6301.28 1282.75,-6301.28 1282.75,-6863.28 1282.75,-6863.28 1282.75,-6869.28 1276.75,-6875.28 1270.75,-6875.28 1270.75,-6875.28 917.25,-6875.28 917.25,-6875.28 911.25,-6875.28 905.25,-6869.28 905.25,-6863.28 905.25,-6863.28 905.25,-6301.28 905.25,-6301.28 905.25,-6295.28 911.25,-6289.28 917.25,-6289.28"/>
<text text-anchor="middle" x="1094" y="-6862.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">domain</text>
</g>
<g id="clust150" class="cluster">
<title>cluster_src/server/domain/AppUser</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M971.12,-6633.28C971.12,-6633.28 1242.62,-6633.28 1242.62,-6633.28 1248.62,-6633.28 1254.62,-6639.28 1254.62,-6645.28 1254.62,-6645.28 1254.62,-6709.28 1254.62,-6709.28 1254.62,-6715.28 1248.62,-6721.28 1242.62,-6721.28 1242.62,-6721.28 971.12,-6721.28 971.12,-6721.28 965.12,-6721.28 959.12,-6715.28 959.12,-6709.28 959.12,-6709.28 959.12,-6645.28 959.12,-6645.28 959.12,-6639.28 965.12,-6633.28 971.12,-6633.28"/>
<text text-anchor="middle" x="1106.88" y="-6708.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AppUser</text>
</g>
<g id="clust151" class="cluster">
<title>cluster_src/server/domain/AppUser/valueObjects</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1174.12,-6641.28C1174.12,-6641.28 1234.62,-6641.28 1234.62,-6641.28 1240.62,-6641.28 1246.62,-6647.28 1246.62,-6653.28 1246.62,-6653.28 1246.62,-6682.28 1246.62,-6682.28 1246.62,-6688.28 1240.62,-6694.28 1234.62,-6694.28 1234.62,-6694.28 1174.12,-6694.28 1174.12,-6694.28 1168.12,-6694.28 1162.12,-6688.28 1162.12,-6682.28 1162.12,-6682.28 1162.12,-6653.28 1162.12,-6653.28 1162.12,-6647.28 1168.12,-6641.28 1174.12,-6641.28"/>
<text text-anchor="middle" x="1204.38" y="-6681.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">valueObjects</text>
</g>
<g id="clust152" class="cluster">
<title>cluster_src/server/domain/Deck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M938.75,-6450.28C938.75,-6450.28 1055.75,-6450.28 1055.75,-6450.28 1061.75,-6450.28 1067.75,-6456.28 1067.75,-6462.28 1067.75,-6462.28 1067.75,-6552.28 1067.75,-6552.28 1067.75,-6558.28 1061.75,-6564.28 1055.75,-6564.28 1055.75,-6564.28 938.75,-6564.28 938.75,-6564.28 932.75,-6564.28 926.75,-6558.28 926.75,-6552.28 926.75,-6552.28 926.75,-6462.28 926.75,-6462.28 926.75,-6456.28 932.75,-6450.28 938.75,-6450.28"/>
<text text-anchor="middle" x="997.25" y="-6551.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Deck</text>
</g>
<g id="clust153" class="cluster">
<title>cluster_src/server/domain/Deck/errors</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M946.75,-6458.28C946.75,-6458.28 1047.75,-6458.28 1047.75,-6458.28 1053.75,-6458.28 1059.75,-6464.28 1059.75,-6470.28 1059.75,-6470.28 1059.75,-6499.28 1059.75,-6499.28 1059.75,-6505.28 1053.75,-6511.28 1047.75,-6511.28 1047.75,-6511.28 946.75,-6511.28 946.75,-6511.28 940.75,-6511.28 934.75,-6505.28 934.75,-6499.28 934.75,-6499.28 934.75,-6470.28 934.75,-6470.28 934.75,-6464.28 940.75,-6458.28 946.75,-6458.28"/>
<text text-anchor="middle" x="997.25" y="-6498.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">errors</text>
</g>
<g id="clust154" class="cluster">
<title>cluster_src/server/domain/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M925.25,-6297.28C925.25,-6297.28 1069.25,-6297.28 1069.25,-6297.28 1075.25,-6297.28 1081.25,-6303.28 1081.25,-6309.28 1081.25,-6309.28 1081.25,-6430.28 1081.25,-6430.28 1081.25,-6436.28 1075.25,-6442.28 1069.25,-6442.28 1069.25,-6442.28 925.25,-6442.28 925.25,-6442.28 919.25,-6442.28 913.25,-6436.28 913.25,-6430.28 913.25,-6430.28 913.25,-6309.28 913.25,-6309.28 913.25,-6303.28 919.25,-6297.28 925.25,-6297.28"/>
<text text-anchor="middle" x="997.25" y="-6429.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust155" class="cluster">
<title>cluster_src/server/domain/Match/errors</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M933.25,-6305.28C933.25,-6305.28 1061.25,-6305.28 1061.25,-6305.28 1067.25,-6305.28 1073.25,-6311.28 1073.25,-6317.28 1073.25,-6317.28 1073.25,-6377.28 1073.25,-6377.28 1073.25,-6383.28 1067.25,-6389.28 1061.25,-6389.28 1061.25,-6389.28 933.25,-6389.28 933.25,-6389.28 927.25,-6389.28 921.25,-6383.28 921.25,-6377.28 921.25,-6377.28 921.25,-6317.28 921.25,-6317.28 921.25,-6311.28 927.25,-6305.28 933.25,-6305.28"/>
<text text-anchor="middle" x="997.25" y="-6376.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">errors</text>
</g>
<g id="clust156" class="cluster">
<title>cluster_src/server/domain/MatchmakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M947.5,-6572.28C947.5,-6572.28 1262.75,-6572.28 1262.75,-6572.28 1268.75,-6572.28 1274.75,-6578.28 1274.75,-6584.28 1274.75,-6584.28 1274.75,-6613.28 1274.75,-6613.28 1274.75,-6619.28 1268.75,-6625.28 1262.75,-6625.28 1262.75,-6625.28 947.5,-6625.28 947.5,-6625.28 941.5,-6625.28 935.5,-6619.28 935.5,-6613.28 935.5,-6613.28 935.5,-6584.28 935.5,-6584.28 935.5,-6578.28 941.5,-6572.28 947.5,-6572.28"/>
<text text-anchor="middle" x="1105.12" y="-6612.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchmakingQueue</text>
</g>
<g id="clust157" class="cluster">
<title>cluster_src/server/domain/User</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M926,-6729.28C926,-6729.28 1068.5,-6729.28 1068.5,-6729.28 1074.5,-6729.28 1080.5,-6735.28 1080.5,-6741.28 1080.5,-6741.28 1080.5,-6836.28 1080.5,-6836.28 1080.5,-6842.28 1074.5,-6848.28 1068.5,-6848.28 1068.5,-6848.28 926,-6848.28 926,-6848.28 920,-6848.28 914,-6842.28 914,-6836.28 914,-6836.28 914,-6741.28 914,-6741.28 914,-6735.28 920,-6729.28 926,-6729.28"/>
<text text-anchor="middle" x="997.25" y="-6835.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">User</text>
</g>
<g id="clust158" class="cluster">
<title>cluster_src/server/domain/User/errors</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M934,-6737.28C934,-6737.28 1060.5,-6737.28 1060.5,-6737.28 1066.5,-6737.28 1072.5,-6743.28 1072.5,-6749.28 1072.5,-6749.28 1072.5,-6809.28 1072.5,-6809.28 1072.5,-6815.28 1066.5,-6821.28 1060.5,-6821.28 1060.5,-6821.28 934,-6821.28 934,-6821.28 928,-6821.28 922,-6815.28 922,-6809.28 922,-6809.28 922,-6749.28 922,-6749.28 922,-6743.28 928,-6737.28 934,-6737.28"/>
<text text-anchor="middle" x="997.25" y="-6808.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">errors</text>
</g>
<g id="clust159" class="cluster">
<title>cluster_src/server/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M412.5,-6525.28C412.5,-6525.28 628,-6525.28 628,-6525.28 634,-6525.28 640,-6531.28 640,-6537.28 640,-6537.28 640,-7252.28 640,-7252.28 640,-7258.28 634,-7264.28 628,-7264.28 628,-7264.28 412.5,-7264.28 412.5,-7264.28 406.5,-7264.28 400.5,-7258.28 400.5,-7252.28 400.5,-7252.28 400.5,-6537.28 400.5,-6537.28 400.5,-6531.28 406.5,-6525.28 412.5,-6525.28"/>
<text text-anchor="middle" x="520.25" y="-7251.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust160" class="cluster">
<title>cluster_src/server/infrastructure/IdentityProvider</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M470.5,-6533.28C470.5,-6533.28 570,-6533.28 570,-6533.28 576,-6533.28 582,-6539.28 582,-6545.28 582,-6545.28 582,-6574.28 582,-6574.28 582,-6580.28 576,-6586.28 570,-6586.28 570,-6586.28 470.5,-6586.28 470.5,-6586.28 464.5,-6586.28 458.5,-6580.28 458.5,-6574.28 458.5,-6574.28 458.5,-6545.28 458.5,-6545.28 458.5,-6539.28 464.5,-6533.28 470.5,-6533.28"/>
<text text-anchor="middle" x="520.25" y="-6573.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">IdentityProvider</text>
</g>
<g id="clust161" class="cluster">
<title>cluster_src/server/infrastructure/gateways</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M441.12,-6594.28C441.12,-6594.28 599.38,-6594.28 599.38,-6594.28 605.38,-6594.28 611.38,-6600.28 611.38,-6606.28 611.38,-6606.28 611.38,-6792.28 611.38,-6792.28 611.38,-6798.28 605.38,-6804.28 599.38,-6804.28 599.38,-6804.28 441.12,-6804.28 441.12,-6804.28 435.12,-6804.28 429.12,-6798.28 429.12,-6792.28 429.12,-6792.28 429.12,-6606.28 429.12,-6606.28 429.12,-6600.28 435.12,-6594.28 441.12,-6594.28"/>
<text text-anchor="middle" x="520.25" y="-6791.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">gateways</text>
</g>
<g id="clust162" class="cluster">
<title>cluster_src/server/infrastructure/gateways/AuthenticationGateway</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M449.12,-6724.28C449.12,-6724.28 591.38,-6724.28 591.38,-6724.28 597.38,-6724.28 603.38,-6730.28 603.38,-6736.28 603.38,-6736.28 603.38,-6765.28 603.38,-6765.28 603.38,-6771.28 597.38,-6777.28 591.38,-6777.28 591.38,-6777.28 449.12,-6777.28 449.12,-6777.28 443.12,-6777.28 437.12,-6771.28 437.12,-6765.28 437.12,-6765.28 437.12,-6736.28 437.12,-6736.28 437.12,-6730.28 443.12,-6724.28 449.12,-6724.28"/>
<text text-anchor="middle" x="520.25" y="-6764.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AuthenticationGateway</text>
</g>
<g id="clust163" class="cluster">
<title>cluster_src/server/infrastructure/gateways/Context</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M480.25,-6602.28C480.25,-6602.28 560.25,-6602.28 560.25,-6602.28 566.25,-6602.28 572.25,-6608.28 572.25,-6614.28 572.25,-6614.28 572.25,-6643.28 572.25,-6643.28 572.25,-6649.28 566.25,-6655.28 560.25,-6655.28 560.25,-6655.28 480.25,-6655.28 480.25,-6655.28 474.25,-6655.28 468.25,-6649.28 468.25,-6643.28 468.25,-6643.28 468.25,-6614.28 468.25,-6614.28 468.25,-6608.28 474.25,-6602.28 480.25,-6602.28"/>
<text text-anchor="middle" x="520.25" y="-6642.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Context</text>
</g>
<g id="clust164" class="cluster">
<title>cluster_src/server/infrastructure/gateways/Time</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M476.5,-6663.28C476.5,-6663.28 564,-6663.28 564,-6663.28 570,-6663.28 576,-6669.28 576,-6675.28 576,-6675.28 576,-6704.28 576,-6704.28 576,-6710.28 570,-6716.28 564,-6716.28 564,-6716.28 476.5,-6716.28 476.5,-6716.28 470.5,-6716.28 464.5,-6710.28 464.5,-6704.28 464.5,-6704.28 464.5,-6675.28 464.5,-6675.28 464.5,-6669.28 470.5,-6663.28 476.5,-6663.28"/>
<text text-anchor="middle" x="520.25" y="-6703.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Time</text>
</g>
<g id="clust165" class="cluster">
<title>cluster_src/server/infrastructure/repositories</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M420.5,-6812.28C420.5,-6812.28 620,-6812.28 620,-6812.28 626,-6812.28 632,-6818.28 632,-6824.28 632,-6824.28 632,-7225.28 632,-7225.28 632,-7231.28 626,-7237.28 620,-7237.28 620,-7237.28 420.5,-7237.28 420.5,-7237.28 414.5,-7237.28 408.5,-7231.28 408.5,-7225.28 408.5,-7225.28 408.5,-6824.28 408.5,-6824.28 408.5,-6818.28 414.5,-6812.28 420.5,-6812.28"/>
<text text-anchor="middle" x="520.25" y="-7224.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">repositories</text>
</g>
<g id="clust166" class="cluster">
<title>cluster_src/server/infrastructure/repositories/AppUser</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M456.25,-7035.28C456.25,-7035.28 584.25,-7035.28 584.25,-7035.28 590.25,-7035.28 596.25,-7041.28 596.25,-7047.28 596.25,-7047.28 596.25,-7076.28 596.25,-7076.28 596.25,-7082.28 590.25,-7088.28 584.25,-7088.28 584.25,-7088.28 456.25,-7088.28 456.25,-7088.28 450.25,-7088.28 444.25,-7082.28 444.25,-7076.28 444.25,-7076.28 444.25,-7047.28 444.25,-7047.28 444.25,-7041.28 450.25,-7035.28 456.25,-7035.28"/>
<text text-anchor="middle" x="520.25" y="-7075.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AppUser</text>
</g>
<g id="clust167" class="cluster">
<title>cluster_src/server/infrastructure/repositories/Deck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M463.75,-7096.28C463.75,-7096.28 576.75,-7096.28 576.75,-7096.28 582.75,-7096.28 588.75,-7102.28 588.75,-7108.28 588.75,-7108.28 588.75,-7137.28 588.75,-7137.28 588.75,-7143.28 582.75,-7149.28 576.75,-7149.28 576.75,-7149.28 463.75,-7149.28 463.75,-7149.28 457.75,-7149.28 451.75,-7143.28 451.75,-7137.28 451.75,-7137.28 451.75,-7108.28 451.75,-7108.28 451.75,-7102.28 457.75,-7096.28 463.75,-7096.28"/>
<text text-anchor="middle" x="520.25" y="-7136.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Deck</text>
</g>
<g id="clust168" class="cluster">
<title>cluster_src/server/infrastructure/repositories/InMemory</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M428.5,-6881.28C428.5,-6881.28 612,-6881.28 612,-6881.28 618,-6881.28 624,-6887.28 624,-6893.28 624,-6893.28 624,-7015.28 624,-7015.28 624,-7021.28 618,-7027.28 612,-7027.28 612,-7027.28 428.5,-7027.28 428.5,-7027.28 422.5,-7027.28 416.5,-7021.28 416.5,-7015.28 416.5,-7015.28 416.5,-6893.28 416.5,-6893.28 416.5,-6887.28 422.5,-6881.28 428.5,-6881.28"/>
<text text-anchor="middle" x="520.25" y="-7014.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">InMemory</text>
</g>
<g id="clust169" class="cluster">
<title>cluster_src/server/infrastructure/repositories/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M461.88,-6820.28C461.88,-6820.28 578.62,-6820.28 578.62,-6820.28 584.62,-6820.28 590.62,-6826.28 590.62,-6832.28 590.62,-6832.28 590.62,-6861.28 590.62,-6861.28 590.62,-6867.28 584.62,-6873.28 578.62,-6873.28 578.62,-6873.28 461.88,-6873.28 461.88,-6873.28 455.88,-6873.28 449.88,-6867.28 449.88,-6861.28 449.88,-6861.28 449.88,-6832.28 449.88,-6832.28 449.88,-6826.28 455.88,-6820.28 461.88,-6820.28"/>
<text text-anchor="middle" x="520.25" y="-6860.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust170" class="cluster">
<title>cluster_src/server/infrastructure/repositories/MatchmakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M433,-7157.28C433,-7157.28 607.5,-7157.28 607.5,-7157.28 613.5,-7157.28 619.5,-7163.28 619.5,-7169.28 619.5,-7169.28 619.5,-7198.28 619.5,-7198.28 619.5,-7204.28 613.5,-7210.28 607.5,-7210.28 607.5,-7210.28 433,-7210.28 433,-7210.28 427,-7210.28 421,-7204.28 421,-7198.28 421,-7198.28 421,-7169.28 421,-7169.28 421,-7163.28 427,-7157.28 433,-7157.28"/>
<text text-anchor="middle" x="520.25" y="-7197.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchmakingQueue</text>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/loading.tsx -->
<g id="node1" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/loading.tsx</title>
<g id="a_node1"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/[deckId]/loading.tsx" xlink:title="loading.tsx">
<path fill="#ccffcc" stroke="black" d="M118.08,-7958.53C118.08,-7958.53 70.17,-7958.53 70.17,-7958.53 67.08,-7958.53 64,-7955.45 64,-7952.36 64,-7952.36 64,-7946.2 64,-7946.2 64,-7943.11 67.08,-7940.03 70.17,-7940.03 70.17,-7940.03 118.08,-7940.03 118.08,-7940.03 121.17,-7940.03 124.25,-7943.11 124.25,-7946.2 124.25,-7946.2 124.25,-7952.36 124.25,-7952.36 124.25,-7955.45 121.17,-7958.53 118.08,-7958.53"/>
<text text-anchor="start" x="72" y="-7945.98" font-family="Helvetica,sans-Serif" font-size="9.00">loading.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx -->
<g id="node2" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx</title>
<g id="a_node2"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/[deckId]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7989.53C114.96,-7989.53 73.29,-7989.53 73.29,-7989.53 70.21,-7989.53 67.12,-7986.45 67.12,-7983.36 67.12,-7983.36 67.12,-7977.2 67.12,-7977.2 67.12,-7974.11 70.21,-7971.03 73.29,-7971.03 73.29,-7971.03 114.96,-7971.03 114.96,-7971.03 118.04,-7971.03 121.12,-7974.11 121.12,-7977.2 121.12,-7977.2 121.12,-7983.36 121.12,-7983.36 121.12,-7986.45 118.04,-7989.53 114.96,-7989.53"/>
<text text-anchor="start" x="76.88" y="-7976.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx -->
<g id="node3" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx</title>
<g id="a_node3"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx" xlink:title="DeckBuilderCardsGrid.tsx">
<polygon fill="#6cbaff" stroke="black" points="581.12,-4413.53 459.38,-4413.53 459.38,-4395.03 581.12,-4395.03 581.12,-4413.53"/>
<text text-anchor="start" x="467.38" y="-4400.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderCardsGrid.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx -->
<g id="edge1" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.57,-7982.54C138.84,-7982.41 160.32,-7978.9 172.25,-7964.28 186.87,-7946.36 169.85,-4653.94 180.25,-4633.28 223.74,-4546.9 305.54,-4586.54 354.75,-4503.28 367.99,-4480.88 345.48,-4464.74 362.75,-4445.28 384.56,-4420.7 419.13,-4410.03 450.23,-4405.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="450.19,-4407.8 455.89,-4404.98 449.68,-4403.63 450.19,-4407.8"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx -->
<g id="node4" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx</title>
<g id="a_node4"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx" xlink:title="DeckBuilderPanel.tsx">
<polygon fill="#6cbaff" stroke="black" points="572.12,-4535.53 468.38,-4535.53 468.38,-4517.03 572.12,-4517.03 572.12,-4535.53"/>
<text text-anchor="start" x="476.38" y="-4522.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderPanel.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx -->
<g id="edge2" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.57,-7982.54C138.84,-7982.41 160.32,-7978.9 172.25,-7964.28 185.92,-7947.53 172.95,-4871.63 180.25,-4851.28 237.09,-4692.72 422.98,-4578.41 492.6,-4540.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="493.57,-4542.07 497.84,-4537.36 491.56,-4538.38 493.57,-4542.07"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx -->
<g id="node5" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx</title>
<g id="a_node5"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx" xlink:title="DeckBuildingFilters.tsx">
<polygon fill="#6cbaff" stroke="black" points="574.75,-4718.53 465.75,-4718.53 465.75,-4700.03 574.75,-4700.03 574.75,-4718.53"/>
<text text-anchor="start" x="473.75" y="-4705.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuildingFilters.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx -->
<g id="edge3" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.52,-7982.5C138.77,-7982.35 160.24,-7978.84 172.25,-7964.28 183.12,-7951.1 179.38,-7366.34 180.25,-7349.28 224.76,-6474.53 310.11,-6261.03 354.75,-5386.28 355.6,-5369.61 353.8,-4799.37 362.75,-4785.28 384.42,-4751.18 426.18,-4731.96 461.11,-4721.39"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="461.27,-4723.53 466.46,-4719.85 460.11,-4719.49 461.27,-4723.53"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx -->
<g id="node6" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx</title>
<g id="a_node6"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx" xlink:title="DeckDraftInitializer.tsx">
<polygon fill="#6cbaff" stroke="black" points="573.25,-4291.53 467.25,-4291.53 467.25,-4273.03 573.25,-4273.03 573.25,-4291.53"/>
<text text-anchor="start" x="475.25" y="-4278.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckDraftInitializer.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx -->
<g id="edge4" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.57,-7982.54C138.84,-7982.41 160.32,-7978.9 172.25,-7964.28 187.46,-7945.64 170.85,-4522.42 180.25,-4500.28 185.77,-4487.28 350.45,-4330.21 362.75,-4323.28 392.04,-4306.78 428.15,-4296.74 458.3,-4290.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="458.56,-4292.82 464.07,-4289.64 457.78,-4288.7 458.56,-4292.82"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx -->
<g id="node7" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx</title>
<g id="a_node7"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx" xlink:title="EditDeckInitializer.tsx">
<polygon fill="#6cbaff" stroke="black" points="571.38,-4657.53 469.12,-4657.53 469.12,-4639.03 571.38,-4639.03 571.38,-4657.53"/>
<text text-anchor="start" x="477.12" y="-4644.98" font-family="Helvetica,sans-Serif" font-size="9.00">EditDeckInitializer.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx -->
<g id="edge5" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.57,-7982.53C138.84,-7982.41 160.32,-7978.9 172.25,-7964.28 183.57,-7950.42 177.42,-5404.95 180.25,-5387.28 219.9,-5139.48 309.35,-5097.09 354.75,-4850.28 357.99,-4832.67 351.46,-4703.18 362.75,-4689.28 385.83,-4660.86 425.98,-4650.75 459.84,-4647.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="459.9,-4649.78 465.73,-4647.24 459.59,-4645.59 459.9,-4649.78"/>
</g>
<!-- src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="node106" class="node">
<title>src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<g id="a_node106"><a xlink:href="src/client/infrastructure/hooks/useLocale/useLocale.ts" xlink:title="useLocale.ts">
<polygon fill="#6cbaff" stroke="black" points="1031.12,-3398.53 963.38,-3398.53 963.38,-3380.03 1031.12,-3380.03 1031.12,-3398.53"/>
<text text-anchor="start" x="971.38" y="-3385.98" font-family="Helvetica,sans-Serif" font-size="9.00">useLocale.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge146" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M581.33,-4407.94C603.06,-4405.89 625.73,-4399.19 640,-4382.28 667.02,-4350.26 621.87,-3658.63 650.75,-3628.28 686.79,-3590.41 849.15,-3650.85 885.5,-3613.28 899.64,-3598.66 879.39,-3446.9 891.12,-3430.28 905.54,-3409.87 931.69,-3399.62 954.3,-3394.47"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="954.66,-3396.54 960.12,-3393.28 953.82,-3392.43 954.66,-3396.54"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx -->
<g id="node109" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx</title>
<g id="a_node109"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx" xlink:title="DeckBuildingCard.tsx">
<polygon fill="#6cbaff" stroke="black" points="804.38,-4321.53 699.88,-4321.53 699.88,-4303.03 804.38,-4303.03 804.38,-4321.53"/>
<text text-anchor="start" x="707.88" y="-4308.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuildingCard.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx -->
<g id="edge140" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M581.62,-4404.81C602.11,-4402.15 623.88,-4395.85 640,-4382.28 650.52,-4373.43 641.19,-4363.16 650.75,-4353.28 663.52,-4340.09 681.13,-4330.99 698.04,-4324.79"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="698.4,-4326.89 703.39,-4322.96 697.04,-4322.92 698.4,-4326.89"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx -->
<g id="node110" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx</title>
<g id="a_node110"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx" xlink:title="TotalCardsInDeckCounter.tsx">
<polygon fill="#6cbaff" stroke="black" points="819.38,-4382.53 684.88,-4382.53 684.88,-4364.03 819.38,-4364.03 819.38,-4382.53"/>
<text text-anchor="start" x="692.88" y="-4369.98" font-family="Helvetica,sans-Serif" font-size="9.00">TotalCardsInDeckCounter.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx -->
<g id="edge141" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M581.35,-4396.18C610.16,-4392.29 644.99,-4387.59 675.57,-4383.47"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="675.83,-4385.55 681.5,-4382.67 675.27,-4381.39 675.83,-4385.55"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx -->
<g id="node111" class="node">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx</title>
<g id="a_node111"><a xlink:href="src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx" xlink:title="useCardsByGameId.tsx">
<polygon fill="#6cbaff" stroke="black" points="1053.25,-3093.53 941.25,-3093.53 941.25,-3075.03 1053.25,-3075.03 1053.25,-3093.53"/>
<text text-anchor="start" x="949.25" y="-3080.98" font-family="Helvetica,sans-Serif" font-size="9.00">useCardsByGameId.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx -->
<g id="edge142" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M581.33,-4407.94C603.06,-4405.9 625.74,-4399.19 640,-4382.28 668.01,-4349.07 620.81,-3631.76 650.75,-3600.28 686.78,-3562.4 849.5,-3623.18 885.5,-3585.28 894.3,-3576.01 883.85,-3135.79 891.12,-3125.28 900.91,-3111.15 916.33,-3101.89 932.37,-3095.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="932.94,-3097.85 937.93,-3093.92 931.57,-3093.88 932.94,-3097.85"/>
</g>
<!-- src/client/infrastructure/hooks/useDebounce/index.ts -->
<g id="node112" class="node">
<title>src/client/infrastructure/hooks/useDebounce/index.ts</title>
<g id="a_node112"><a xlink:href="src/client/infrastructure/hooks/useDebounce/index.ts" xlink:title="index.ts">
<polygon fill="#6cbaff" stroke="black" points="1024.25,-3154.53 970.25,-3154.53 970.25,-3136.03 1024.25,-3136.03 1024.25,-3154.53"/>
<text text-anchor="start" x="981.5" y="-3141.98" font-family="Helvetica,sans-Serif" font-size="9.00">index.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useDebounce/index.ts -->
<g id="edge143" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useDebounce/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M581.33,-4407.94C603.06,-4405.9 625.74,-4399.19 640,-4382.28 667.52,-4349.67 621.34,-3645.2 650.75,-3614.28 686.78,-3576.41 849.48,-3637.16 885.5,-3599.28 893.41,-3590.97 884.59,-3195.71 891.12,-3186.28 906.86,-3163.58 937.15,-3153.45 961.19,-3148.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.49,-3151.01 967.07,-3147.95 960.8,-3146.86 961.49,-3151.01"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="node113" class="node">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<g id="a_node113"><a xlink:href="src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts" xlink:title="useDeckBuilder.ts">
<polygon fill="#6cbaff" stroke="black" points="1042,-3032.53 952.5,-3032.53 952.5,-3014.03 1042,-3014.03 1042,-3032.53"/>
<text text-anchor="start" x="960.5" y="-3019.98" font-family="Helvetica,sans-Serif" font-size="9.00">useDeckBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge144" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M581.33,-4407.94C603.07,-4405.9 625.74,-4399.19 640,-4382.28 668.51,-4348.47 620,-3618.07 650.75,-3586.28 668.9,-3567.51 867.37,-3594.07 885.5,-3575.28 895.36,-3565.07 883.05,-3075.96 891.12,-3064.28 903.16,-3046.88 923.75,-3036.86 943.54,-3031.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="944.05,-3033.13 949.32,-3029.58 942.98,-3029.07 944.05,-3033.13"/>
</g>
<!-- src/client/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="node114" class="node">
<title>src/client/infrastructure/hooks/useGameId/useGameId.ts</title>
<g id="a_node114"><a xlink:href="src/client/infrastructure/hooks/useGameId/useGameId.ts" xlink:title="useGameId.ts">
<polygon fill="#6cbaff" stroke="black" points="1033.38,-3459.53 961.12,-3459.53 961.12,-3441.03 1033.38,-3441.03 1033.38,-3459.53"/>
<text text-anchor="start" x="969.12" y="-3446.98" font-family="Helvetica,sans-Serif" font-size="9.00">useGameId.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="edge145" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M581.33,-4407.94C603.06,-4405.89 625.73,-4399.18 640,-4382.28 666.52,-4350.86 622.4,-3672.06 650.75,-3642.28 686.79,-3604.42 849.09,-3664.79 885.5,-3627.28 898.2,-3614.2 878.82,-3476.73 891.12,-3463.28 906.11,-3446.9 930.35,-3443.08 951.78,-3443.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="951.63,-3445.71 957.73,-3443.93 951.85,-3441.51 951.63,-3445.71"/>
</g>
<!-- src/client/infrastructure/hooks/useResponsiveColumnCount.tsx -->
<g id="node115" class="node">
<title>src/client/infrastructure/hooks/useResponsiveColumnCount.tsx</title>
<g id="a_node115"><a xlink:href="src/client/infrastructure/hooks/useResponsiveColumnCount.tsx" xlink:title="useResponsiveColumnCount.tsx">
<polygon fill="#6cbaff" stroke="black" points="1072.38,-3512.53 922.12,-3512.53 922.12,-3494.03 1072.38,-3494.03 1072.38,-3512.53"/>
<text text-anchor="start" x="930.12" y="-3499.98" font-family="Helvetica,sans-Serif" font-size="9.00">useResponsiveColumnCount.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useResponsiveColumnCount.tsx -->
<g id="edge147" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useResponsiveColumnCount.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M581.62,-4407.9C603.25,-4405.81 625.79,-4399.09 640,-4382.28 663.89,-4354.02 628.69,-3745.99 650.75,-3716.28 716.06,-3628.35 818.91,-3728.25 885.5,-3641.28 893.88,-3630.33 881.81,-3527.45 891.12,-3517.28 897.24,-3510.61 904.87,-3505.99 913.23,-3502.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="913.78,-3504.9 918.89,-3501.12 912.54,-3500.88 913.78,-3504.9"/>
</g>
<!-- src/client/infrastructure/lib/chunk.ts -->
<g id="node116" class="node">
<title>src/client/infrastructure/lib/chunk.ts</title>
<g id="a_node116"><a xlink:href="src/client/infrastructure/lib/chunk.ts" xlink:title="chunk.ts">
<polygon fill="#6cbaff" stroke="black" points="1231.38,-5266.53 1177.38,-5266.53 1177.38,-5248.03 1231.38,-5248.03 1231.38,-5266.53"/>
<text text-anchor="start" x="1187.5" y="-5253.98" font-family="Helvetica,sans-Serif" font-size="9.00">chunk.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/lib/chunk.ts -->
<g id="edge148" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/lib/chunk.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M581.62,-4410.87C603.53,-4416.47 626.25,-4426.69 640,-4445.28 660.03,-4472.36 627.63,-4723.78 650.75,-4748.28 686.71,-4786.39 842.49,-4740.36 885.5,-4770.28 1062.04,-4893.09 1017.93,-5005.65 1095.38,-5206.28 1101.22,-5221.42 1093.58,-5230.55 1105.75,-5241.28 1122.58,-5256.12 1147.67,-5259.98 1168.19,-5260.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1167.96,-5262.31 1173.94,-5260.13 1167.91,-5258.11 1167.96,-5262.31"/>
</g>
<!-- src/client/application/store/appStore.ts -->
<g id="node36" class="node">
<title>src/client/application/store/appStore.ts</title>
<g id="a_node36"><a xlink:href="src/client/application/store/appStore.ts" xlink:title="appStore.ts">
<polygon fill="#fb6969" stroke="black" points="1579.62,-2669.53 1517.12,-2669.53 1517.12,-2651.03 1579.62,-2651.03 1579.62,-2669.53"/>
<text text-anchor="start" x="1525.12" y="-2656.98" font-family="Helvetica,sans-Serif" font-size="9.00">appStore.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge153" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M540.41,-4535.98C568.24,-4551.27 618.83,-4583.88 640,-4628.28 658.08,-4666.21 636.04,-4966.92 650.75,-5006.28 709.73,-5164.05 745.94,-5213.88 891.12,-5299.28 1046.56,-5390.72 1153.43,-5449.98 1295,-5338.28 1552.23,-5135.32 1425.04,-4946.3 1466.5,-4621.28 1478.37,-4528.24 1471.39,-4293.02 1474.5,-4199.28 1495.23,-3573.93 1538.92,-2806.47 1546.3,-2678.7"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1548.4,-2678.88 1546.65,-2672.77 1544.2,-2678.64 1548.4,-2678.88"/>
</g>
<!-- src/client/application/commands/clearDeckDraft/clearDeckDraft.ts -->
<g id="node39" class="node">
<title>src/client/application/commands/clearDeckDraft/clearDeckDraft.ts</title>
<g id="a_node39"><a xlink:href="src/client/application/commands/clearDeckDraft/clearDeckDraft.ts" xlink:title="clearDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1247.25,-1326.53 1161.5,-1326.53 1161.5,-1308.03 1247.25,-1308.03 1247.25,-1326.53"/>
<text text-anchor="start" x="1169.5" y="-1313.98" font-family="Helvetica,sans-Serif" font-size="9.00">clearDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/commands/clearDeckDraft/clearDeckDraft.ts -->
<g id="edge151" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/commands/clearDeckDraft/clearDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.51,-4530.59C596.75,-4529.61 623.85,-4523.57 640,-4504.28 655.04,-4486.31 641.51,-2838.81 650.75,-2817.28 707.86,-2684.19 797.01,-2705.93 885.5,-2591.28 1002.2,-2440.08 1046.06,-2400.8 1095.38,-2216.28 1101.53,-2193.25 1092.44,-1378.05 1105.75,-1358.28 1116.57,-1342.22 1135.02,-1332.43 1152.99,-1326.47"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1153.14,-1328.62 1158.28,-1324.87 1151.93,-1324.6 1153.14,-1328.62"/>
</g>
<!-- src/client/application/queries/hasDeckDraft/hasDeckDraft.ts -->
<g id="node86" class="node">
<title>src/client/application/queries/hasDeckDraft/hasDeckDraft.ts</title>
<g id="a_node86"><a xlink:href="src/client/application/queries/hasDeckDraft/hasDeckDraft.ts" xlink:title="hasDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1421,-2154.53 1340.5,-2154.53 1340.5,-2136.03 1421,-2136.03 1421,-2154.53"/>
<text text-anchor="start" x="1348.5" y="-2141.98" font-family="Helvetica,sans-Serif" font-size="9.00">hasDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/queries/hasDeckDraft/hasDeckDraft.ts -->
<g id="edge152" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/queries/hasDeckDraft/hasDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.5,-4530.58C596.74,-4529.61 623.84,-4523.57 640,-4504.28 653.47,-4488.2 643.61,-3014 650.75,-2994.28 706.71,-2839.65 781.97,-2836.05 885.5,-2708.28 1072.52,-2477.46 1074.53,-2376.16 1303,-2186.28 1316.53,-2175.04 1333.63,-2165.54 1348.25,-2158.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1348.78,-2160.6 1353.34,-2156.17 1347.01,-2156.79 1348.78,-2160.6"/>
</g>
<!-- src/client/infrastructure/builders/urlBuilder.ts -->
<g id="node100" class="node">
<title>src/client/infrastructure/builders/urlBuilder.ts</title>
<g id="a_node100"><a xlink:href="src/client/infrastructure/builders/urlBuilder.ts" xlink:title="urlBuilder.ts">
<polygon fill="#6cbaff" stroke="black" points="1029.25,-5235.53 965.25,-5235.53 965.25,-5217.03 1029.25,-5217.03 1029.25,-5235.53"/>
<text text-anchor="start" x="973.25" y="-5222.98" font-family="Helvetica,sans-Serif" font-size="9.00">urlBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge154" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M539.68,-4535.86C567.06,-4551.21 617.66,-4584.15 640,-4628.28 654.19,-4656.32 631.79,-4744.22 650.75,-4769.28 717.81,-4857.88 809.37,-4774.35 885.5,-4855.28 983.9,-4959.88 995.19,-5148.28 996.24,-5207.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="994.14,-5207.74 996.3,-5213.72 998.34,-5207.7 994.14,-5207.74"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge161" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.48,-4530.56C596.71,-4529.58 623.82,-4523.55 640,-4504.28 659.57,-4480.99 629.76,-3431.3 650.75,-3409.28 671.27,-3387.75 866.71,-3387.56 954.23,-3388.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="954.05,-3390.68 960.08,-3388.65 954.11,-3386.48 954.05,-3390.68"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx -->
<g id="edge157" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.41,-4524.41C594.41,-4521.74 619.68,-4516.06 640,-4504.28 688.38,-4476.24 725.5,-4419.01 741.9,-4390.38"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="743.57,-4391.7 744.67,-4385.44 739.91,-4389.65 743.57,-4391.7"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge158" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.49,-4530.57C596.72,-4529.59 623.82,-4523.55 640,-4504.28 660.53,-4479.84 639.81,-3385.27 650.75,-3355.28 708.25,-3197.69 755.2,-3162.58 891.12,-3064.28 908.69,-3051.58 930.7,-3042.2 950.04,-3035.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="950.49,-3037.7 955.55,-3033.85 949.19,-3033.71 950.49,-3037.7"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="edge160" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.48,-4530.56C596.71,-4529.58 623.81,-4523.54 640,-4504.28 658.77,-4481.95 630.14,-3474.92 650.75,-3454.28 691.8,-3413.17 868.52,-3431.98 952.11,-3443.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="951.63,-3445.62 957.87,-3444.38 952.22,-3441.47 951.63,-3445.62"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx -->
<g id="node118" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx</title>
<g id="a_node118"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx" xlink:title="DeckCardRow.tsx">
<polygon fill="#6cbaff" stroke="black" points="796.5,-4535.53 707.75,-4535.53 707.75,-4517.03 796.5,-4517.03 796.5,-4535.53"/>
<text text-anchor="start" x="715.75" y="-4522.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckCardRow.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx -->
<g id="edge150" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.55,-4526.28C610.04,-4526.28 660.71,-4526.28 698.5,-4526.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="698.3,-4528.38 704.3,-4526.28 698.3,-4524.18 698.3,-4528.38"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx -->
<g id="node119" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx</title>
<g id="a_node119"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx" xlink:title="DeckCardDetailsDialog.tsx">
<polygon fill="#6cbaff" stroke="black" points="815.25,-4596.53 689,-4596.53 689,-4578.03 815.25,-4578.03 815.25,-4596.53"/>
<text text-anchor="start" x="697" y="-4583.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckCardDetailsDialog.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx -->
<g id="edge155" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M551.16,-4536C577.29,-4544.32 616.32,-4556.34 650.75,-4565.28 664.83,-4568.94 680.13,-4572.5 694.44,-4575.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="693.94,-4577.69 700.25,-4576.92 694.83,-4573.59 693.94,-4577.69"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx -->
<g id="node120" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx</title>
<g id="a_node120"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx" xlink:title="SaveDeckDialog.tsx">
<polygon fill="#6cbaff" stroke="black" points="801.38,-4657.53 702.88,-4657.53 702.88,-4639.03 801.38,-4639.03 801.38,-4657.53"/>
<text text-anchor="start" x="710.88" y="-4644.98" font-family="Helvetica,sans-Serif" font-size="9.00">SaveDeckDialog.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx -->
<g id="edge156" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M530.63,-4536C550.69,-4556.15 600.04,-4602.61 650.75,-4626.28 664.2,-4632.56 679.5,-4637.08 694.04,-4640.32"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="693.22,-4642.29 699.52,-4641.47 694.08,-4638.18 693.22,-4642.29"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckId/useDeckId.ts -->
<g id="node121" class="node">
<title>src/client/infrastructure/hooks/useDeckId/useDeckId.ts</title>
<g id="a_node121"><a xlink:href="src/client/infrastructure/hooks/useDeckId/useDeckId.ts" xlink:title="useDeckId.ts">
<polygon fill="#6cbaff" stroke="black" points="1031.5,-3276.53 963,-3276.53 963,-3258.03 1031.5,-3258.03 1031.5,-3276.53"/>
<text text-anchor="start" x="971" y="-3263.98" font-family="Helvetica,sans-Serif" font-size="9.00">useDeckId.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckId/useDeckId.ts -->
<g id="edge159" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckId/useDeckId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.48,-4530.56C596.71,-4529.59 623.82,-4523.55 640,-4504.28 659.99,-4480.48 630.44,-3408.81 650.75,-3385.28 719.76,-3305.32 811.44,-3423.58 885.5,-3348.28 898.09,-3335.48 879.99,-3322.36 891.12,-3308.28 906.36,-3289.02 931.88,-3278.79 953.97,-3273.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="954.19,-3275.47 959.59,-3272.11 953.28,-3271.37 954.19,-3275.47"/>
</g>
<!-- src/client/infrastructure/providers/ToastProvider.tsx -->
<g id="node122" class="node">
<title>src/client/infrastructure/providers/ToastProvider.tsx</title>
<g id="a_node122"><a xlink:href="src/client/infrastructure/providers/ToastProvider.tsx" xlink:title="ToastProvider.tsx">
<polygon fill="#6cbaff" stroke="black" points="794.62,-3514.53 709.62,-3514.53 709.62,-3496.03 794.62,-3496.03 794.62,-3514.53"/>
<text text-anchor="start" x="717.62" y="-3501.98" font-family="Helvetica,sans-Serif" font-size="9.00">ToastProvider.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/providers/ToastProvider.tsx -->
<g id="edge162" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/providers/ToastProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.47,-4530.55C596.7,-4529.57 623.81,-4523.54 640,-4504.28 657.58,-4483.38 632.91,-3541.96 650.75,-3521.28 662.88,-3507.22 682.01,-3502.01 700.33,-3500.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="700.28,-3502.86 706.2,-3500.55 700.13,-3498.67 700.28,-3502.86"/>
</g>
<!-- src/client/infrastructure/store/store.ts -->
<g id="node123" class="node">
<title>src/client/infrastructure/store/store.ts</title>
<g id="a_node123"><a xlink:href="src/client/infrastructure/store/store.ts" xlink:title="store.ts">
<polygon fill="#6cbaff" stroke="black" points="1024.25,-3638.53 970.25,-3638.53 970.25,-3620.03 1024.25,-3620.03 1024.25,-3638.53"/>
<text text-anchor="start" x="982.62" y="-3625.98" font-family="Helvetica,sans-Serif" font-size="9.00">store.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/store/store.ts -->
<g id="edge163" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/store/store.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.38,-4530.48C596.59,-4529.48 623.7,-4523.45 640,-4504.28 657.5,-4483.71 640.88,-4043.42 650.75,-4018.28 720.7,-3840.13 911.55,-3690.4 975.24,-3644.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="976.26,-3645.93 979.9,-3640.72 973.81,-3642.53 976.26,-3645.93"/>
</g>
<!-- src/client/domain/Catalog/Filter.ts -->
<g id="node68" class="node">
<title>src/client/domain/Catalog/Filter.ts</title>
<g id="a_node68"><a xlink:href="src/client/domain/Catalog/Filter.ts" xlink:title="Filter.ts">
<polygon fill="#fa9f36" stroke="black" points="2085.38,-490.53 2031.38,-490.53 2031.38,-472.03 2085.38,-472.03 2085.38,-490.53"/>
<text text-anchor="start" x="2044.12" y="-477.98" font-family="Helvetica,sans-Serif" font-size="9.00">Filter.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge168" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M551.04,-4719C579.56,-4730.08 620.98,-4751.19 640,-4785.28 657.63,-4816.87 625.82,-5410.07 650.75,-5436.28 745.65,-5536.04 1793.28,-5478.61 1888.62,-5379.28 2002.03,-5261.14 2002,-4073.9 2008.88,-3910.28 2009.86,-3886.77 2007.91,-544.03 2016.88,-522.28 2020.96,-512.37 2028.72,-503.57 2036.37,-496.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2037.67,-498.33 2040.93,-492.87 2034.98,-495.11 2037.67,-498.33"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge170" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M575.23,-4713.44C598.74,-4712.12 624.44,-4705.83 640,-4687.28 662.98,-4659.89 634.97,-3432.36 650.75,-3400.28 709.27,-3281.33 825.29,-3334.38 885.5,-3216.28 893.18,-3201.22 881.33,-3078.06 891.12,-3064.28 903.34,-3047.1 923.85,-3037.12 943.53,-3031.32"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="944,-3033.37 949.26,-3029.79 942.92,-3029.31 944,-3033.37"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx -->
<g id="node124" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx</title>
<g id="a_node124"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx" xlink:title="FilterItem.tsx">
<polygon fill="#6cbaff" stroke="black" points="785.62,-4718.53 718.62,-4718.53 718.62,-4700.03 785.62,-4700.03 785.62,-4718.53"/>
<text text-anchor="start" x="726.62" y="-4705.98" font-family="Helvetica,sans-Serif" font-size="9.00">FilterItem.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx -->
<g id="edge169" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M575.15,-4709.28C616.18,-4709.28 671.83,-4709.28 709.48,-4709.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="709.15,-4711.38 715.15,-4709.28 709.15,-4707.18 709.15,-4711.38"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge176" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M573.71,-4286.94C597.93,-4292 624.67,-4302.34 640,-4323.28 653.95,-4342.34 645.36,-4725.29 650.75,-4748.28 708.58,-4994.99 698.69,-5100.41 891.12,-5265.28 966.53,-5329.88 1007.44,-5313.37 1105.75,-5327.28 1147.39,-5333.17 1263.89,-5355.58 1295,-5327.28 1399.6,-5232.11 1533.29,-2909.68 1546.34,-2678.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1548.43,-2678.97 1546.67,-2672.86 1544.24,-2678.73 1548.43,-2678.97"/>
</g>
<!-- src/client/application/commands/loadDeckDraft/loadDeckDraft.ts -->
<g id="node50" class="node">
<title>src/client/application/commands/loadDeckDraft/loadDeckDraft.ts</title>
<g id="a_node50"><a xlink:href="src/client/application/commands/loadDeckDraft/loadDeckDraft.ts" xlink:title="loadDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1246.12,-1265.53 1162.62,-1265.53 1162.62,-1247.03 1246.12,-1247.03 1246.12,-1265.53"/>
<text text-anchor="start" x="1170.62" y="-1252.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/commands/loadDeckDraft/loadDeckDraft.ts -->
<g id="edge174" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/commands/loadDeckDraft/loadDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M537.44,-4272.75C564.67,-4255.65 618.76,-4216.88 640,-4167.28 655.22,-4131.73 640.22,-2812.49 650.75,-2775.28 754.75,-2407.93 990.23,-2407.3 1095.38,-2040.28 1101.06,-2020.44 1094.2,-1314.39 1105.75,-1297.28 1116.76,-1280.96 1135.65,-1271.12 1153.89,-1265.19"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1154.13,-1267.31 1159.29,-1263.6 1152.94,-1263.28 1154.13,-1267.31"/>
</g>
<!-- src/client/application/queries/isCatalogLoading/isCatalogLoading.ts -->
<g id="node88" class="node">
<title>src/client/application/queries/isCatalogLoading/isCatalogLoading.ts</title>
<g id="a_node88"><a xlink:href="src/client/application/queries/isCatalogLoading/isCatalogLoading.ts" xlink:title="isCatalogLoading.ts">
<polygon fill="#fb6969" stroke="black" points="1429.62,-2398.53 1331.88,-2398.53 1331.88,-2380.03 1429.62,-2380.03 1429.62,-2398.53"/>
<text text-anchor="start" x="1339.88" y="-2385.98" font-family="Helvetica,sans-Serif" font-size="9.00">isCatalogLoading.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/queries/isCatalogLoading/isCatalogLoading.ts -->
<g id="edge175" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/queries/isCatalogLoading/isCatalogLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M537.44,-4272.75C564.67,-4255.65 618.75,-4216.88 640,-4167.28 654.61,-4133.17 632.51,-2863.6 650.75,-2831.28 802.73,-2562 1192.15,-2437.86 1330.26,-2401.31"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1330.78,-2403.34 1336.06,-2399.79 1329.72,-2399.28 1330.78,-2403.34"/>
</g>
<!-- src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="node34" class="node">
<title>src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<g id="a_node34"><a xlink:href="src/client/application/queries/getCatalogCardById/getCatalogCardById.ts" xlink:title="getCatalogCardById.ts">
<polygon fill="#fb6969" stroke="black" points="1434.88,-1544.53 1326.62,-1544.53 1326.62,-1526.03 1434.88,-1526.03 1434.88,-1544.53"/>
<text text-anchor="start" x="1334.62" y="-1531.98" font-family="Helvetica,sans-Serif" font-size="9.00">getCatalogCardById.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge178" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M571.66,-4652.45C596.45,-4657.33 624.32,-4667.65 640,-4689.28 661.59,-4719.06 624.53,-5325.48 650.75,-5351.28 688.02,-5387.95 1252.45,-5372.3 1295,-5327.28 1312.89,-5308.35 1289.58,-1598.61 1303,-1576.28 1310.54,-1563.74 1323.37,-1554.81 1336.4,-1548.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.07,-1550.57 1341.72,-1546.23 1335.38,-1546.72 1337.07,-1550.57"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge181" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M571.67,-4652.44C596.47,-4657.32 624.33,-4667.64 640,-4689.28 663.65,-4721.96 623.09,-5385.92 650.75,-5415.28 699.83,-5467.39 1237.82,-5457.34 1295,-5414.28 1461.34,-5289.02 1424.15,-5182.16 1466.5,-4978.28 1516.01,-4739.96 1544.14,-2884.01 1547.11,-2678.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1549.21,-2679.02 1547.19,-2672.99 1545.01,-2678.96 1549.21,-2679.02"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts -->
<g id="node51" class="node">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts</title>
<g id="a_node51"><a xlink:href="src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts" xlink:title="loadDeckIntoBuilder.ts">
<polygon fill="#fb6969" stroke="black" points="1258.12,-777.53 1150.62,-777.53 1150.62,-759.03 1258.12,-759.03 1258.12,-777.53"/>
<text text-anchor="start" x="1158.62" y="-764.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadDeckIntoBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts -->
<g id="edge177" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M547.51,-4638.54C576.15,-4626.32 620.53,-4602.36 640,-4565.28 662.39,-4522.63 638.41,-2877.84 650.75,-2831.28 754.09,-2441.46 991.6,-2429.99 1095.38,-2040.28 1104.17,-2007.24 1086.7,-837.68 1105.75,-809.28 1114.61,-796.08 1128.62,-787.12 1143.3,-781.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1143.78,-783.1 1148.66,-779.02 1142.3,-779.17 1143.78,-783.1"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/hasDeckDraft/hasDeckDraft.ts -->
<g id="edge179" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/hasDeckDraft/hasDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M571.66,-4652.45C596.46,-4657.32 624.33,-4667.64 640,-4689.28 662.94,-4720.96 622.88,-5365.83 650.75,-5393.28 701.75,-5443.51 1244.32,-5443.83 1295,-5393.28 1310.77,-5377.55 1291.52,-2205.37 1303,-2186.28 1310.54,-2173.74 1323.37,-2164.81 1336.4,-2158.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.07,-2160.57 1341.72,-2156.24 1335.39,-2156.72 1337.07,-2160.57"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/isCatalogLoading/isCatalogLoading.ts -->
<g id="edge180" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/isCatalogLoading/isCatalogLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M547.24,-4638.64C575.87,-4626.45 620.44,-4602.45 640,-4565.28 657.49,-4532.04 635.08,-3247.41 650.75,-3213.28 760.17,-2974.92 945.04,-3040.19 1095.38,-2825.28 1102.74,-2814.75 1100.37,-2809.96 1105.75,-2798.28 1179.61,-2638.03 1218.62,-2608.34 1295,-2449.28 1298.97,-2441.02 1296.83,-2437.05 1303,-2430.28 1313.98,-2418.24 1329.32,-2409.07 1343.41,-2402.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1344.11,-2404.48 1348.75,-2400.13 1342.41,-2400.63 1344.11,-2404.48"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/infrastructure/store/store.ts -->
<g id="edge182" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/infrastructure/store/store.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M547.13,-4638.58C575.66,-4626.34 620.15,-4602.3 640,-4565.28 664,-4520.52 630.82,-4155 650.75,-4108.28 708.79,-3972.2 801.33,-3993.95 885.5,-3872.28 938.6,-3795.52 976.68,-3689.21 990.52,-3647.3"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="992.51,-3647.95 992.37,-3641.6 988.52,-3646.66 992.51,-3647.95"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx -->
<g id="node8" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx</title>
<g id="a_node8"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M115.08,-7924.53C115.08,-7924.53 73.17,-7924.53 73.17,-7924.53 70.08,-7924.53 67,-7921.45 67,-7918.36 67,-7918.36 67,-7912.2 67,-7912.2 67,-7909.11 70.08,-7906.03 73.17,-7906.03 73.17,-7906.03 115.08,-7906.03 115.08,-7906.03 118.17,-7906.03 121.25,-7909.11 121.25,-7912.2 121.25,-7912.2 121.25,-7918.36 121.25,-7918.36 121.25,-7921.45 118.17,-7924.53 115.08,-7924.53"/>
<text text-anchor="start" x="75" y="-7911.98" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts -->
<g id="node9" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts</title>
<g id="a_node9"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts" xlink:title="DeckBuilderInitializer.ts">
<polygon fill="#6cbaff" stroke="black" points="575.88,-4352.53 464.62,-4352.53 464.62,-4334.03 575.88,-4334.03 575.88,-4352.53"/>
<text text-anchor="start" x="472.62" y="-4339.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderInitializer.ts</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts -->
<g id="edge6" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.57,-7917.54C138.84,-7917.41 160.32,-7913.9 172.25,-7899.28 186.76,-7881.5 172.19,-4615.77 180.25,-4594.28 223.69,-4478.51 257.71,-4449.52 362.75,-4384.28 390.65,-4366.95 425.73,-4356.9 455.59,-4351.09"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="455.78,-4353.19 461.3,-4350.04 455.02,-4349.06 455.78,-4353.19"/>
</g>
<!-- src/client/infrastructure/providers/ReduxProvider.tsx -->
<g id="node10" class="node">
<title>src/client/infrastructure/providers/ReduxProvider.tsx</title>
<g id="a_node10"><a xlink:href="src/client/infrastructure/providers/ReduxProvider.tsx" xlink:title="ReduxProvider.tsx">
<polygon fill="#6cbaff" stroke="black" points="797.25,-3545.53 707,-3545.53 707,-3527.03 797.25,-3527.03 797.25,-3545.53"/>
<text text-anchor="start" x="715" y="-3532.98" font-family="Helvetica,sans-Serif" font-size="9.00">ReduxProvider.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/infrastructure/providers/ReduxProvider.tsx -->
<g id="edge7" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/infrastructure/providers/ReduxProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.52,-7917.49C138.76,-7917.34 160.23,-7913.83 172.25,-7899.28 191.71,-7875.72 159.17,-7371.41 180.25,-7349.28 250.86,-7275.16 569.8,-7394.79 640,-7320.28 655.3,-7304.05 648.78,-4130.5 650.75,-4108.28 670.29,-3888.15 729.95,-3625.64 746.71,-3554.7"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="748.73,-3555.26 748.08,-3548.94 744.65,-3554.29 748.73,-3555.26"/>
</g>
<!-- src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts -->
<g id="node117" class="node">
<title>src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts</title>
<g id="a_node117"><a xlink:href="src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts" xlink:title="useInitializeDeckBuilderFromLocation.ts">
<polygon fill="#6cbaff" stroke="black" points="1087.38,-3337.53 907.12,-3337.53 907.12,-3319.03 1087.38,-3319.03 1087.38,-3337.53"/>
<text text-anchor="start" x="915.12" y="-3324.98" font-family="Helvetica,sans-Serif" font-size="9.00">useInitializeDeckBuilderFromLocation.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts&#45;&gt;src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts -->
<g id="edge149" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts&#45;&gt;src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M576.12,-4347.36C599.38,-4345.93 624.61,-4339.57 640,-4321.28 656.72,-4301.41 633.86,-3406.01 650.75,-3386.28 719.21,-3306.31 853.03,-3307.96 932.53,-3317.42"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="932.26,-3319.51 938.47,-3318.17 932.78,-3315.34 932.26,-3319.51"/>
</g>
<!-- src/client/infrastructure/providers/ReduxProvider.tsx&#45;&gt;src/client/infrastructure/store/store.ts -->
<g id="edge242" class="edge">
<title>src/client/infrastructure/providers/ReduxProvider.tsx&#45;&gt;src/client/infrastructure/store/store.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M778.23,-3545.88C822.27,-3562.73 913.45,-3597.61 962.79,-3616.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.74,-3618.33 968.09,-3618.51 963.24,-3614.4 961.74,-3618.33"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx -->
<g id="node11" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx</title>
<g id="a_node11"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7893.53C114.96,-7893.53 73.29,-7893.53 73.29,-7893.53 70.21,-7893.53 67.12,-7890.45 67.12,-7887.36 67.12,-7887.36 67.12,-7881.2 67.12,-7881.2 67.12,-7878.11 70.21,-7875.03 73.29,-7875.03 73.29,-7875.03 114.96,-7875.03 114.96,-7875.03 118.04,-7875.03 121.12,-7878.11 121.12,-7881.2 121.12,-7881.2 121.12,-7887.36 121.12,-7887.36 121.12,-7890.45 118.04,-7893.53 114.96,-7893.53"/>
<text text-anchor="start" x="76.88" y="-7880.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx -->
<g id="edge8" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.45,-7887.65C138.67,-7888.24 160.13,-7885.58 172.25,-7871.28 187.09,-7853.77 169.33,-4585.48 180.25,-4565.28 233.41,-4466.99 367.48,-4428.14 450.2,-4413.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="450.5,-4415.28 456.05,-4412.18 449.78,-4411.14 450.5,-4415.28"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx -->
<g id="edge9" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.45,-7887.65C138.67,-7888.24 160.13,-7885.57 172.25,-7871.28 186.53,-7854.44 168.54,-4711 180.25,-4692.28 241.7,-4594.05 379.97,-4552.32 459.64,-4535.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="459.67,-4538.04 465.14,-4534.81 458.84,-4533.93 459.67,-4538.04"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx -->
<g id="edge10" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.44,-7887.64C138.64,-7888.21 160.1,-7885.55 172.25,-7871.28 183.12,-7858.52 179.38,-6681.02 180.25,-6664.28 181.61,-6638.1 347.78,-4806.81 362.75,-4785.28 386.3,-4751.42 429.26,-4732.06 464.21,-4721.36"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="464.38,-4723.5 469.55,-4719.8 463.2,-4719.47 464.38,-4723.5"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx -->
<g id="edge11" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-7887.65C138.67,-7888.24 160.13,-7885.58 172.25,-7871.28 188.02,-7852.68 165.01,-4378.32 180.25,-4359.28 246.06,-4277.04 379.69,-4271.14 458.09,-4275.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="457.76,-4277.81 463.88,-4276.1 458.03,-4273.61 457.76,-4277.81"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx -->
<g id="edge12" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.45,-7887.65C138.67,-7888.23 160.13,-7885.57 172.25,-7871.28 185.87,-7855.22 171.23,-4858.31 180.25,-4839.28 225.23,-4744.41 269.24,-4737.03 362.75,-4689.28 393.14,-4673.76 429.94,-4663.74 460.22,-4657.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="460.54,-4659.57 466.01,-4656.34 459.72,-4655.45 460.54,-4659.57"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/page.tsx -->
<g id="node12" class="node">
<title>app/[locale]/(connected)/games/[gameId]/page.tsx</title>
<g id="a_node12"><a xlink:href="app/[locale]/(connected)/games/[gameId]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7859.53C114.96,-7859.53 73.29,-7859.53 73.29,-7859.53 70.21,-7859.53 67.12,-7856.45 67.12,-7853.36 67.12,-7853.36 67.12,-7847.2 67.12,-7847.2 67.12,-7844.11 70.21,-7841.03 73.29,-7841.03 73.29,-7841.03 114.96,-7841.03 114.96,-7841.03 118.04,-7841.03 121.12,-7844.11 121.12,-7847.2 121.12,-7847.2 121.12,-7853.36 121.12,-7853.36 121.12,-7856.45 118.04,-7859.53 114.96,-7859.53"/>
<text text-anchor="start" x="76.88" y="-7846.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx -->
<g id="node13" class="node">
<title>src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx</title>
<g id="a_node13"><a xlink:href="src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx" xlink:title="GameDetailsPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="335.75,-3676.53 231.25,-3676.53 231.25,-3658.03 335.75,-3658.03 335.75,-3676.53"/>
<text text-anchor="start" x="239.25" y="-3663.98" font-family="Helvetica,sans-Serif" font-size="9.00">GameDetailsPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx -->
<g id="edge13" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.5,-7853.28C138.73,-7853.63 160.2,-7850.68 172.25,-7836.28 180.13,-7826.86 179.25,-4310.52 180.25,-4298.28 200.21,-4053.75 261.95,-3760.99 278.42,-3685.66"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="280.47,-3686.12 279.71,-3679.8 276.37,-3685.21 280.47,-3686.12"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="node105" class="node">
<title>src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<g id="a_node105"><a xlink:href="src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx" xlink:title="ShiningCard.tsx">
<polygon fill="#6cbaff" stroke="black" points="1037.88,-4168.53 956.62,-4168.53 956.62,-4150.03 1037.88,-4150.03 1037.88,-4168.53"/>
<text text-anchor="start" x="964.62" y="-4155.98" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningCard.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge232" class="edge">
<title>src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M336.2,-3659.45C343.43,-3656.18 350,-3651.62 354.75,-3645.28 369.21,-3625.98 345.54,-3228.18 362.75,-3211.28 406.72,-3168.12 595.8,-3168.36 640,-3211.28 654.52,-3225.37 646.08,-3555.6 650.75,-3575.28 707.74,-3815.73 820.47,-3840.88 885.5,-4079.28 888.91,-4091.77 882.48,-4127.64 891.12,-4137.28 905.11,-4152.89 927.07,-4159.03 947.31,-4161.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="947,-4163.1 953.13,-4161.42 947.3,-4158.91 947,-4163.1"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx -->
<g id="node108" class="node">
<title>src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx</title>
<g id="a_node108"><a xlink:href="src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx" xlink:title="PlayGameButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="569.5,-4875.53 471,-4875.53 471,-4857.03 569.5,-4857.03 569.5,-4875.53"/>
<text text-anchor="start" x="479" y="-4862.98" font-family="Helvetica,sans-Serif" font-size="9.00">PlayGameButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx -->
<g id="edge231" class="edge">
<title>src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M284.42,-3676.97C283.59,-3784.57 278.5,-4753.61 362.75,-4844.28 387.35,-4870.76 428.07,-4875.81 461.81,-4874.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="461.78,-4876.58 467.65,-4874.15 461.54,-4872.39 461.78,-4876.58"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/play/page.tsx -->
<g id="node14" class="node">
<title>app/[locale]/(connected)/games/[gameId]/play/page.tsx</title>
<g id="a_node14"><a xlink:href="app/[locale]/(connected)/games/[gameId]/play/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7806.53C114.96,-7806.53 73.29,-7806.53 73.29,-7806.53 70.21,-7806.53 67.12,-7803.45 67.12,-7800.36 67.12,-7800.36 67.12,-7794.2 67.12,-7794.2 67.12,-7791.11 70.21,-7788.03 73.29,-7788.03 73.29,-7788.03 114.96,-7788.03 114.96,-7788.03 118.04,-7788.03 121.12,-7791.11 121.12,-7794.2 121.12,-7794.2 121.12,-7800.36 121.12,-7800.36 121.12,-7803.45 118.04,-7806.53 114.96,-7806.53"/>
<text text-anchor="start" x="76.88" y="-7793.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx -->
<g id="node15" class="node">
<title>src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx</title>
<g id="a_node15"><a xlink:href="src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx" xlink:title="PlayGamePage.tsx">
<polygon fill="#6cbaff" stroke="black" points="330.5,-3615.53 236.5,-3615.53 236.5,-3597.03 330.5,-3597.03 330.5,-3615.53"/>
<text text-anchor="start" x="244.5" y="-3602.98" font-family="Helvetica,sans-Serif" font-size="9.00">PlayGamePage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/play/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx -->
<g id="edge14" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/play/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-7800.65C138.67,-7801.24 160.14,-7798.58 172.25,-7784.28 190.82,-7762.36 164.13,-3671.06 180.25,-3647.28 191.18,-3631.15 209.61,-3621.36 227.84,-3615.43"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="228.11,-3617.54 233.27,-3613.82 226.92,-3613.51 228.11,-3617.54"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx -->
<g id="node129" class="node">
<title>src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx</title>
<g id="a_node129"><a xlink:href="src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx" xlink:title="StartGameButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="569.88,-5032.53 470.62,-5032.53 470.62,-5014.03 569.88,-5014.03 569.88,-5032.53"/>
<text text-anchor="start" x="478.62" y="-5019.98" font-family="Helvetica,sans-Serif" font-size="9.00">StartGameButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx -->
<g id="edge235" class="edge">
<title>src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M315.01,-3615.96C329.77,-3622.36 346.13,-3632.37 354.75,-3647.28 364.16,-3663.56 350.37,-4987.12 362.75,-5001.28 386.54,-5028.49 427.29,-5033.52 461.24,-5032"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="461.28,-5034.1 467.14,-5031.63 461.02,-5029.91 461.28,-5034.1"/>
</g>
<!-- app/[locale]/(connected)/games/page.tsx -->
<g id="node16" class="node">
<title>app/[locale]/(connected)/games/page.tsx</title>
<g id="a_node16"><a xlink:href="app/[locale]/(connected)/games/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7764.53C114.96,-7764.53 73.29,-7764.53 73.29,-7764.53 70.21,-7764.53 67.12,-7761.45 67.12,-7758.36 67.12,-7758.36 67.12,-7752.2 67.12,-7752.2 67.12,-7749.11 70.21,-7746.03 73.29,-7746.03 73.29,-7746.03 114.96,-7746.03 114.96,-7746.03 118.04,-7746.03 121.12,-7749.11 121.12,-7752.2 121.12,-7752.2 121.12,-7758.36 121.12,-7758.36 121.12,-7761.45 118.04,-7764.53 114.96,-7764.53"/>
<text text-anchor="start" x="76.88" y="-7751.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx -->
<g id="node17" class="node">
<title>src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx</title>
<g id="a_node17"><a xlink:href="src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx" xlink:title="GameListPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="328.62,-3554.53 238.38,-3554.53 238.38,-3536.03 328.62,-3536.03 328.62,-3554.53"/>
<text text-anchor="start" x="246.38" y="-3541.98" font-family="Helvetica,sans-Serif" font-size="9.00">GameListPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx -->
<g id="edge15" class="edge">
<title>app/[locale]/(connected)/games/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.3,-7746.36C139.16,-7738.75 161.46,-7725.87 172.25,-7706.28 186.05,-7681.22 164.2,-3609.96 180.25,-3586.28 191.51,-3569.67 210.72,-3559.79 229.47,-3553.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="229.91,-3555.97 235.11,-3552.31 228.76,-3551.93 229.91,-3555.97"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx -->
<g id="node107" class="node">
<title>src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx</title>
<g id="a_node107"><a xlink:href="src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx" xlink:title="GameDetailsButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="574.75,-4814.53 465.75,-4814.53 465.75,-4796.03 574.75,-4796.03 574.75,-4814.53"/>
<text text-anchor="start" x="473.75" y="-4801.98" font-family="Helvetica,sans-Serif" font-size="9.00">GameDetailsButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx&#45;&gt;src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx -->
<g id="edge233" class="edge">
<title>src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx&#45;&gt;src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M315,-3554.97C329.76,-3561.36 346.12,-3571.37 354.75,-3586.28 370.92,-3614.22 344.15,-4721.9 362.75,-4748.28 384.1,-4778.56 423.29,-4792.81 457.11,-4799.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="456.32,-4801.48 462.6,-4800.49 457.07,-4797.35 456.32,-4801.48"/>
</g>
<!-- src/server/application/queries/loadGameList.ts -->
<g id="node142" class="node">
<title>src/server/application/queries/loadGameList.ts</title>
<g id="a_node142"><a xlink:href="src/server/application/queries/loadGameList.ts" xlink:title="loadGameList.ts">
<polygon fill="#dd1c1c" stroke="black" points="561.25,-6246.53 479.25,-6246.53 479.25,-6228.03 561.25,-6228.03 561.25,-6246.53"/>
<text text-anchor="start" x="487.25" y="-6233.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadGameList.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx&#45;&gt;src/server/application/queries/loadGameList.ts -->
<g id="edge234" class="edge">
<title>src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx&#45;&gt;src/server/application/queries/loadGameList.ts</title>
<g id="a_edge234"><a xlink:title="no&#45;cross&#45;boundary&#45;client&#45;server">
<path fill="none" stroke="red" stroke-width="2" d="M315.02,-3554.95C329.79,-3561.34 346.15,-3571.35 354.75,-3586.28 363.89,-3602.14 350.47,-6207.72 362.75,-6221.28 389.03,-6250.3 434.95,-6251.84 470.28,-6247.57"/>
<polygon fill="red" stroke="red" stroke-width="2" points="470.35,-6249.68 476.01,-6246.78 469.78,-6245.52 470.35,-6249.68"/>
</a>
</g>
<text text-anchor="middle" x="292.32" y="-4891.77" font-family="Helvetica,sans-Serif" font-size="9.00" fill="red">no&#45;cross&#45;boundary&#45;client&#45;server</text>
</g>
<!-- app/[locale]/(connected)/matches/[matchId]/page.tsx -->
<g id="node18" class="node">
<title>app/[locale]/(connected)/matches/[matchId]/page.tsx</title>
<g id="a_node18"><a xlink:href="app/[locale]/(connected)/matches/[matchId]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7676.53C114.96,-7676.53 73.29,-7676.53 73.29,-7676.53 70.21,-7676.53 67.12,-7673.45 67.12,-7670.36 67.12,-7670.36 67.12,-7664.2 67.12,-7664.2 67.12,-7661.11 70.21,-7658.03 73.29,-7658.03 73.29,-7658.03 114.96,-7658.03 114.96,-7658.03 118.04,-7658.03 121.12,-7661.11 121.12,-7664.2 121.12,-7664.2 121.12,-7670.36 121.12,-7670.36 121.12,-7673.45 118.04,-7676.53 114.96,-7676.53"/>
<text text-anchor="start" x="76.88" y="-7663.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx -->
<g id="node19" class="node">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx</title>
<g id="a_node19"><a xlink:href="src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx" xlink:title="MatchPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="321.5,-3301.53 245.5,-3301.53 245.5,-3283.03 321.5,-3283.03 321.5,-3301.53"/>
<text text-anchor="start" x="253.5" y="-3288.98" font-family="Helvetica,sans-Serif" font-size="9.00">MatchPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/matches/[matchId]/page.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx -->
<g id="edge16" class="edge">
<title>app/[locale]/(connected)/matches/[matchId]/page.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.47,-7661.87C139.17,-7656.72 161.19,-7647.01 172.25,-7629.28 187.9,-7604.17 166.93,-3394.71 180.25,-3368.28 195,-3339 226.96,-3318.15 251.24,-3305.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="251.96,-3307.76 256.43,-3303.24 250.12,-3303.99 251.96,-3307.76"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx -->
<g id="node127" class="node">
<title>src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx</title>
<g id="a_node127"><a xlink:href="src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx" xlink:title="LeaveMatchButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="573.25,-4971.53 467.25,-4971.53 467.25,-4953.03 573.25,-4953.03 573.25,-4971.53"/>
<text text-anchor="start" x="475.25" y="-4958.98" font-family="Helvetica,sans-Serif" font-size="9.00">LeaveMatchButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx -->
<g id="edge236" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.27,-3301.95C315.27,-3314.82 343.35,-3339.36 354.75,-3368.28 362.58,-3388.14 350.46,-4887.82 362.75,-4905.28 384.39,-4936.02 424.4,-4950.23 458.55,-4956.79"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="457.83,-4958.79 464.11,-4957.77 458.56,-4954.66 457.83,-4958.79"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/Match/Match.tsx -->
<g id="node128" class="node">
<title>src/client/infrastructure/components/app/Gaming/Match/Match.tsx</title>
<g id="a_node128"><a xlink:href="src/client/infrastructure/components/app/Gaming/Match/Match.tsx" xlink:title="Match.tsx">
<polygon fill="#6cbaff" stroke="black" points="547.38,-5093.53 493.12,-5093.53 493.12,-5075.03 547.38,-5075.03 547.38,-5093.53"/>
<text text-anchor="start" x="501.12" y="-5080.98" font-family="Helvetica,sans-Serif" font-size="9.00">Match.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/Match/Match.tsx -->
<g id="edge237" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/Match/Match.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.27,-3301.95C315.28,-3314.82 343.35,-3339.36 354.75,-3368.28 363.38,-3390.17 347.27,-5044.56 362.75,-5062.28 392.2,-5096 447.69,-5095.67 484.06,-5091"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="484.23,-5093.1 489.87,-5090.17 483.63,-5088.94 484.23,-5093.1"/>
</g>
<!-- src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx -->
<g id="node131" class="node">
<title>src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx</title>
<g id="a_node131"><a xlink:href="src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx" xlink:title="MatchConsoleEvents.tsx">
<polygon fill="#6cbaff" stroke="black" points="578.5,-3964.53 462,-3964.53 462,-3946.03 578.5,-3946.03 578.5,-3964.53"/>
<text text-anchor="start" x="470" y="-3951.98" font-family="Helvetica,sans-Serif" font-size="9.00">MatchConsoleEvents.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx -->
<g id="edge238" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.23,-3301.96C315.19,-3314.86 343.22,-3339.42 354.75,-3368.28 360.57,-3382.86 352.37,-3921.51 362.75,-3933.28 384.64,-3958.11 420.67,-3964.42 452.63,-3964.16"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="452.56,-3966.26 458.5,-3964 452.45,-3962.06 452.56,-3966.26"/>
</g>
<!-- src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx -->
<g id="node143" class="node">
<title>src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx</title>
<g id="a_node143"><a xlink:href="src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx" xlink:title="ErrorLoadingMatchPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="585.25,-3240.53 455.25,-3240.53 455.25,-3222.03 585.25,-3222.03 585.25,-3240.53"/>
<text text-anchor="start" x="463.25" y="-3227.98" font-family="Helvetica,sans-Serif" font-size="9.00">ErrorLoadingMatchPage.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx -->
<g id="edge239" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M321.96,-3282.55C363.1,-3271.86 429.18,-3254.69 473.39,-3243.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="473.72,-3245.28 479,-3241.74 472.66,-3241.22 473.72,-3245.28"/>
</g>
<!-- src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx -->
<g id="node144" class="node">
<title>src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx</title>
<g id="a_node144"><a xlink:href="src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx" xlink:title="FinishedMatchPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="575.88,-3301.53 464.62,-3301.53 464.62,-3283.03 575.88,-3283.03 575.88,-3301.53"/>
<text text-anchor="start" x="472.62" y="-3288.98" font-family="Helvetica,sans-Serif" font-size="9.00">FinishedMatchPage.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx -->
<g id="edge240" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M321.96,-3292.28C357.85,-3292.28 412.73,-3292.28 455.5,-3292.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="455.41,-3294.38 461.41,-3292.28 455.41,-3290.18 455.41,-3294.38"/>
</g>
<!-- src/server/application/queries/loadMatchById.ts -->
<g id="node145" class="node">
<title>src/server/application/queries/loadMatchById.ts</title>
<g id="a_node145"><a xlink:href="src/server/application/queries/loadMatchById.ts" xlink:title="loadMatchById.ts">
<polygon fill="#dd1c1c" stroke="black" points="563.12,-6215.53 477.38,-6215.53 477.38,-6197.03 563.12,-6197.03 563.12,-6215.53"/>
<text text-anchor="start" x="485.38" y="-6202.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadMatchById.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/server/application/queries/loadMatchById.ts -->
<g id="edge241" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/server/application/queries/loadMatchById.ts</title>
<g id="a_edge241"><a xlink:title="no&#45;cross&#45;boundary&#45;client&#45;server">
<path fill="none" stroke="red" stroke-width="2" d="M298.28,-3301.94C315.29,-3314.81 343.38,-3339.35 354.75,-3368.28 361.77,-3386.13 352.55,-6114.04 362.75,-6130.28 385.8,-6166.98 432.18,-6186.42 468.5,-6196.41"/>
<polygon fill="red" stroke="red" stroke-width="2" points="467.75,-6198.39 474.09,-6197.87 468.82,-6194.33 467.75,-6198.39"/>
</a>
</g>
<text text-anchor="middle" x="292.31" y="-4741.84" font-family="Helvetica,sans-Serif" font-size="9.00" fill="red">no&#45;cross&#45;boundary&#45;client&#45;server</text>
</g>
<!-- app/[locale]/(not&#45;connected)/layout.tsx -->
<g id="node20" class="node">
<title>app/[locale]/(not&#45;connected)/layout.tsx</title>
<g id="a_node20"><a xlink:href="app/[locale]/(not-connected)/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M115.08,-7477.53C115.08,-7477.53 73.17,-7477.53 73.17,-7477.53 70.08,-7477.53 67,-7474.45 67,-7471.36 67,-7471.36 67,-7465.2 67,-7465.2 67,-7462.11 70.08,-7459.03 73.17,-7459.03 73.17,-7459.03 115.08,-7459.03 115.08,-7459.03 118.17,-7459.03 121.25,-7462.11 121.25,-7465.2 121.25,-7465.2 121.25,-7471.36 121.25,-7471.36 121.25,-7474.45 118.17,-7477.53 115.08,-7477.53"/>
<text text-anchor="start" x="75" y="-7464.98" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx -->
<g id="node21" class="node">
<title>src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx</title>
<g id="a_node21"><a xlink:href="src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx" xlink:title="FullPageLayout.tsx">
<polygon fill="#6cbaff" stroke="black" points="567.25,-3134.53 473.25,-3134.53 473.25,-3116.03 567.25,-3116.03 567.25,-3134.53"/>
<text text-anchor="start" x="481.25" y="-3121.98" font-family="Helvetica,sans-Serif" font-size="9.00">FullPageLayout.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(not&#45;connected)/layout.tsx&#45;&gt;src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx -->
<g id="edge17" class="edge">
<title>app/[locale]/(not&#45;connected)/layout.tsx&#45;&gt;src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-7471.65C138.67,-7472.24 160.14,-7469.58 172.25,-7455.28 191.03,-7433.12 164.29,-3296.55 180.25,-3272.28 243.72,-3175.78 385.62,-3142.3 464.21,-3130.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="464.36,-3133.04 470.01,-3130.13 463.78,-3128.88 464.36,-3133.04"/>
</g>
<!-- app/[locale]/(not&#45;connected)/signin/page.tsx -->
<g id="node22" class="node">
<title>app/[locale]/(not&#45;connected)/signin/page.tsx</title>
<g id="a_node22"><a xlink:href="app/[locale]/(not-connected)/signin/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7511.53C114.96,-7511.53 73.29,-7511.53 73.29,-7511.53 70.21,-7511.53 67.12,-7508.45 67.12,-7505.36 67.12,-7505.36 67.12,-7499.2 67.12,-7499.2 67.12,-7496.11 70.21,-7493.03 73.29,-7493.03 73.29,-7493.03 114.96,-7493.03 114.96,-7493.03 118.04,-7493.03 121.12,-7496.11 121.12,-7499.2 121.12,-7499.2 121.12,-7505.36 121.12,-7505.36 121.12,-7508.45 118.04,-7511.53 114.96,-7511.53"/>
<text text-anchor="start" x="76.88" y="-7498.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx -->
<g id="node23" class="node">
<title>src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx</title>
<g id="a_node23"><a xlink:href="src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx" xlink:title="SignInPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="322.25,-3397.53 244.75,-3397.53 244.75,-3379.03 322.25,-3379.03 322.25,-3397.53"/>
<text text-anchor="start" x="252.75" y="-3384.98" font-family="Helvetica,sans-Serif" font-size="9.00">SignInPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(not&#45;connected)/signin/page.tsx&#45;&gt;src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx -->
<g id="edge18" class="edge">
<title>app/[locale]/(not&#45;connected)/signin/page.tsx&#45;&gt;src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-7505.65C138.67,-7506.24 160.14,-7503.58 172.25,-7489.28 190.48,-7467.77 164.43,-3452.62 180.25,-3429.28 192.74,-3410.86 215,-3400.71 235.57,-3395.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="235.99,-3397.19 241.31,-3393.72 234.99,-3393.11 235.99,-3397.19"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx -->
<g id="node104" class="node">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx</title>
<g id="a_node104"><a xlink:href="src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx" xlink:title="SignInForm.tsx">
<polygon fill="#6cbaff" stroke="black" points="790.5,-4168.53 713.75,-4168.53 713.75,-4150.03 790.5,-4150.03 790.5,-4168.53"/>
<text text-anchor="start" x="721.75" y="-4155.98" font-family="Helvetica,sans-Serif" font-size="9.00">SignInForm.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx -->
<g id="edge229" class="edge">
<title>src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M315.01,-3397.96C329.78,-3404.35 346.14,-3414.36 354.75,-3429.28 366.51,-3449.66 346.07,-5106.68 362.75,-5123.28 450.09,-5210.21 552.31,-5209.85 640,-5123.28 656.43,-5107.06 643.28,-4314.12 650.75,-4292.28 667.46,-4243.46 709.62,-4198.09 733.65,-4175.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="735.05,-4176.64 737.99,-4171.01 732.18,-4173.58 735.05,-4176.64"/>
</g>
<!-- src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx -->
<g id="node137" class="node">
<title>src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx</title>
<g id="a_node137"><a xlink:href="src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx" xlink:title="Sparkles.tsx">
<polygon fill="#6cbaff" stroke="black" points="1030,-4290.53 964.5,-4290.53 964.5,-4272.03 1030,-4272.03 1030,-4290.53"/>
<text text-anchor="start" x="972.5" y="-4277.98" font-family="Helvetica,sans-Serif" font-size="9.00">Sparkles.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx -->
<g id="edge230" class="edge">
<title>src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M305.44,-3398C320.12,-3405.38 339.72,-3416.44 354.75,-3429.28 507.07,-3559.4 516.79,-3620.32 640,-3778.28 752.43,-3922.42 824.81,-3935.85 885.5,-4108.28 891.07,-4124.12 880.3,-4246.45 891.12,-4259.28 906.62,-4277.65 933.16,-4283.1 955.7,-4283.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="955.37,-4286.08 961.4,-4284.07 955.43,-4281.88 955.37,-4286.08"/>
</g>
<!-- app/[locale]/access&#45;denied/page.tsx -->
<g id="node24" class="node">
<title>app/[locale]/access&#45;denied/page.tsx</title>
<g id="a_node24"><a xlink:href="app/[locale]/access-denied/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7599.53C114.96,-7599.53 73.29,-7599.53 73.29,-7599.53 70.21,-7599.53 67.12,-7596.45 67.12,-7593.36 67.12,-7593.36 67.12,-7587.2 67.12,-7587.2 67.12,-7584.11 70.21,-7581.03 73.29,-7581.03 73.29,-7581.03 114.96,-7581.03 114.96,-7581.03 118.04,-7581.03 121.12,-7584.11 121.12,-7587.2 121.12,-7587.2 121.12,-7593.36 121.12,-7593.36 121.12,-7596.45 118.04,-7599.53 114.96,-7599.53"/>
<text text-anchor="start" x="76.88" y="-7586.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx -->
<g id="node25" class="node">
<title>src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx</title>
<g id="a_node25"><a xlink:href="src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx" xlink:title="AccessDeniedPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="338.75,-3458.53 228.25,-3458.53 228.25,-3440.03 338.75,-3440.03 338.75,-3458.53"/>
<text text-anchor="start" x="236.25" y="-3445.98" font-family="Helvetica,sans-Serif" font-size="9.00">AccessDeniedPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/access&#45;denied/page.tsx&#45;&gt;src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx -->
<g id="edge19" class="edge">
<title>app/[locale]/access&#45;denied/page.tsx&#45;&gt;src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.3,-7581.36C139.16,-7573.75 161.46,-7560.87 172.25,-7541.28 185.7,-7516.85 167.7,-3550.19 180.25,-3525.28 195.01,-3496 226.96,-3475.15 251.24,-3462.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="251.97,-3464.76 256.43,-3460.24 250.12,-3460.99 251.97,-3464.76"/>
</g>
<!-- app/[locale]/page.tsx -->
<g id="node26" class="node">
<title>app/[locale]/page.tsx</title>
<g id="a_node26"><a xlink:href="app/[locale]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7443.53C114.96,-7443.53 73.29,-7443.53 73.29,-7443.53 70.21,-7443.53 67.12,-7440.45 67.12,-7437.36 67.12,-7437.36 67.12,-7431.2 67.12,-7431.2 67.12,-7428.11 70.21,-7425.03 73.29,-7425.03 73.29,-7425.03 114.96,-7425.03 114.96,-7425.03 118.04,-7425.03 121.12,-7428.11 121.12,-7431.2 121.12,-7431.2 121.12,-7437.36 121.12,-7437.36 121.12,-7440.45 118.04,-7443.53 114.96,-7443.53"/>
<text text-anchor="start" x="76.88" y="-7430.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx -->
<g id="node27" class="node">
<title>src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx</title>
<g id="a_node27"><a xlink:href="src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx" xlink:title="RedirectToGameList.tsx">
<polygon fill="#6cbaff" stroke="black" points="576.62,-3807.53 463.88,-3807.53 463.88,-3789.03 576.62,-3789.03 576.62,-3807.53"/>
<text text-anchor="start" x="471.88" y="-3794.98" font-family="Helvetica,sans-Serif" font-size="9.00">RedirectToGameList.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/page.tsx&#45;&gt;src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx -->
<g id="edge20" class="edge">
<title>app/[locale]/page.tsx&#45;&gt;src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-7437.65C138.67,-7438.24 160.14,-7435.58 172.25,-7421.28 190.87,-7399.3 159.86,-3292.63 180.25,-3272.28 235.14,-3217.49 296.34,-3221.26 354.75,-3272.28 364.71,-3280.99 359.75,-3318.4 362.75,-3331.28 404.58,-3510.69 486.84,-3718.87 511.86,-3780.34"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="509.89,-3781.06 514.1,-3785.82 513.77,-3779.47 509.89,-3781.06"/>
</g>
<!-- app/globals.css -->
<g id="node28" class="node">
<title>app/globals.css</title>
<g id="a_node28"><a xlink:href="app/globals.css" xlink:title="globals.css">
<path fill="#ffffcc" stroke="black" d="M308.21,-7378.53C308.21,-7378.53 258.79,-7378.53 258.79,-7378.53 255.71,-7378.53 252.62,-7375.45 252.62,-7372.36 252.62,-7372.36 252.62,-7366.2 252.62,-7366.2 252.62,-7363.11 255.71,-7360.03 258.79,-7360.03 258.79,-7360.03 308.21,-7360.03 308.21,-7360.03 311.29,-7360.03 314.38,-7363.11 314.38,-7366.2 314.38,-7366.2 314.38,-7372.36 314.38,-7372.36 314.38,-7375.45 311.29,-7378.53 308.21,-7378.53"/>
<text text-anchor="start" x="260.62" y="-7365.98" font-family="Helvetica,sans-Serif" font-size="9.00">globals.css</text>
</a>
</g>
</g>
<!-- app/layout.tsx -->
<g id="node29" class="node">
<title>app/layout.tsx</title>
<g id="a_node29"><a xlink:href="app/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M115.08,-7378.53C115.08,-7378.53 73.17,-7378.53 73.17,-7378.53 70.08,-7378.53 67,-7375.45 67,-7372.36 67,-7372.36 67,-7366.2 67,-7366.2 67,-7363.11 70.08,-7360.03 73.17,-7360.03 73.17,-7360.03 115.08,-7360.03 115.08,-7360.03 118.17,-7360.03 121.25,-7363.11 121.25,-7366.2 121.25,-7366.2 121.25,-7372.36 121.25,-7372.36 121.25,-7375.45 118.17,-7378.53 115.08,-7378.53"/>
<text text-anchor="start" x="75" y="-7365.98" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;app/globals.css -->
<g id="edge21" class="edge">
<title>app/layout.tsx&#45;&gt;app/globals.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.57,-7369.28C153.29,-7369.28 206.72,-7369.28 243.25,-7369.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="243.12,-7371.38 249.12,-7369.28 243.12,-7367.18 243.12,-7371.38"/>
</g>
<!-- src/client/infrastructure/components/ui/Background/Background.tsx -->
<g id="node30" class="node">
<title>src/client/infrastructure/components/ui/Background/Background.tsx</title>
<g id="a_node30"><a xlink:href="src/client/infrastructure/components/ui/Background/Background.tsx" xlink:title="Background.tsx">
<polygon fill="#6cbaff" stroke="black" points="1036.75,-4046.53 957.75,-4046.53 957.75,-4028.03 1036.75,-4028.03 1036.75,-4046.53"/>
<text text-anchor="start" x="965.75" y="-4033.98" font-family="Helvetica,sans-Serif" font-size="9.00">Background.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;src/client/infrastructure/components/ui/Background/Background.tsx -->
<g id="edge22" class="edge">
<title>app/layout.tsx&#45;&gt;src/client/infrastructure/components/ui/Background/Background.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M95.13,-7359.61C95.18,-7136.57 97.33,-3128.48 180.25,-3046.28 208.22,-3018.55 576.45,-3008.26 640,-3044.28 824.78,-3149.01 826.21,-3250.33 885.5,-3454.28 892.14,-3477.12 887.53,-3644.77 891.12,-3668.28 912.33,-3806.8 969.67,-3966.84 989.44,-4019.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="987.42,-4020.09 991.51,-4024.96 991.35,-4018.6 987.42,-4020.09"/>
</g>
<!-- src/client/infrastructure/layouts/RootLayout/RootLayout.tsx -->
<g id="node31" class="node">
<title>src/client/infrastructure/layouts/RootLayout/RootLayout.tsx</title>
<g id="a_node31"><a xlink:href="src/client/infrastructure/layouts/RootLayout/RootLayout.tsx" xlink:title="RootLayout.tsx">
<polygon fill="#6cbaff" stroke="black" points="558.62,-3073.53 481.88,-3073.53 481.88,-3055.03 558.62,-3055.03 558.62,-3073.53"/>
<text text-anchor="start" x="489.88" y="-3060.98" font-family="Helvetica,sans-Serif" font-size="9.00">RootLayout.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;src/client/infrastructure/layouts/RootLayout/RootLayout.tsx -->
<g id="edge23" class="edge">
<title>app/layout.tsx&#45;&gt;src/client/infrastructure/layouts/RootLayout/RootLayout.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M95.14,-7359.79C95.6,-7140.77 104.85,-3204.87 180.25,-3118.28 252.83,-3034.93 398.17,-3044 472.75,-3055.34"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="472.24,-3057.38 478.5,-3056.25 472.9,-3053.23 472.24,-3057.38"/>
</g>
<!-- src/client/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/infrastructure/providers/ToastProvider.tsx -->
<g id="edge227" class="edge">
<title>src/client/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/infrastructure/providers/ToastProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M558.88,-3066.42C586.17,-3070.15 621.42,-3079.96 640,-3105.28 665.26,-3139.69 622.59,-3457.2 650.75,-3489.28 663,-3503.24 682.15,-3508.43 700.46,-3509.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="700.24,-3511.79 706.31,-3509.91 700.39,-3507.59 700.24,-3511.79"/>
</g>
<!-- src/client/infrastructure/providers/ConvexClientProvider.tsx -->
<g id="node140" class="node">
<title>src/client/infrastructure/providers/ConvexClientProvider.tsx</title>
<g id="a_node140"><a xlink:href="src/client/infrastructure/providers/ConvexClientProvider.tsx" xlink:title="ConvexClientProvider.tsx">
<polygon fill="#6cbaff" stroke="black" points="811.5,-3483.53 692.75,-3483.53 692.75,-3465.03 811.5,-3465.03 811.5,-3483.53"/>
<text text-anchor="start" x="700.75" y="-3470.98" font-family="Helvetica,sans-Serif" font-size="9.00">ConvexClientProvider.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/infrastructure/providers/ConvexClientProvider.tsx -->
<g id="edge226" class="edge">
<title>src/client/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/infrastructure/providers/ConvexClientProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M558.99,-3068.29C585.07,-3072.99 618.77,-3083.18 640,-3105.28 739.03,-3208.35 750.17,-3396.38 751.14,-3455.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="749.04,-3455.75 751.19,-3461.73 753.24,-3455.71 749.04,-3455.75"/>
</g>
<!-- app/page.tsx -->
<g id="node32" class="node">
<title>app/page.tsx</title>
<g id="a_node32"><a xlink:href="app/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7409.53C114.96,-7409.53 73.29,-7409.53 73.29,-7409.53 70.21,-7409.53 67.12,-7406.45 67.12,-7403.36 67.12,-7403.36 67.12,-7397.2 67.12,-7397.2 67.12,-7394.11 70.21,-7391.03 73.29,-7391.03 73.29,-7391.03 114.96,-7391.03 114.96,-7391.03 118.04,-7391.03 121.12,-7394.11 121.12,-7397.2 121.12,-7397.2 121.12,-7403.36 121.12,-7403.36 121.12,-7406.45 118.04,-7409.53 114.96,-7409.53"/>
<text text-anchor="start" x="76.88" y="-7396.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/page.tsx&#45;&gt;src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx -->
<g id="edge24" class="edge">
<title>app/page.tsx&#45;&gt;src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.58,-7402.54C138.84,-7402.41 160.32,-7398.91 172.25,-7384.28 190.73,-7361.62 159.55,-3193.94 180.25,-3173.28 235.14,-3118.49 298.54,-3119.85 354.75,-3173.28 367.49,-3185.39 359.48,-3314.01 362.75,-3331.28 396.99,-3512.29 484.44,-3719.38 511.29,-3780.46"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="509.36,-3781.28 513.7,-3785.92 513.2,-3779.58 509.36,-3781.28"/>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts -->
<g id="node33" class="node">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts</title>
<g id="a_node33"><a xlink:href="src/client/application/commands/addCardToDeck/addCardToDeck.ts" xlink:title="addCardToDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1250.25,-960.53 1158.5,-960.53 1158.5,-942.03 1250.25,-942.03 1250.25,-960.53"/>
<text text-anchor="start" x="1166.5" y="-947.98" font-family="Helvetica,sans-Serif" font-size="9.00">addCardToDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge25" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250,-960.87C1267.04,-966.91 1284.8,-976.67 1295,-992.28 1310.83,-1016.52 1285.4,-1490.3 1303,-1513.28 1307.15,-1518.7 1312.54,-1522.84 1318.54,-1526.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1317.32,-1527.77 1323.65,-1528.29 1319.03,-1523.93 1317.32,-1527.77"/>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts -->
<g id="node35" class="node">
<title>src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts</title>
<g id="a_node35"><a xlink:href="src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts" xlink:title="addCardToDeckRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1443.88,-960.53 1317.62,-960.53 1317.62,-942.03 1443.88,-942.03 1443.88,-960.53"/>
<text text-anchor="start" x="1325.62" y="-947.98" font-family="Helvetica,sans-Serif" font-size="9.00">addCardToDeckRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts -->
<g id="edge26" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.73,-951.28C1268.32,-951.28 1288.89,-951.28 1308.37,-951.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1308.2,-953.38 1314.2,-951.28 1308.2,-949.18 1308.2,-953.38"/>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge27" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.56,-960.98C1267.46,-967.03 1284.98,-976.76 1295,-992.28 1307.9,-1012.26 1286.15,-2687.51 1303,-2704.28 1328.75,-2729.91 1431.29,-2713.24 1466.5,-2704.28 1488.86,-2698.59 1511.73,-2685.32 1527.41,-2674.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1528.26,-2676.8 1532.01,-2671.67 1525.87,-2673.35 1528.26,-2676.8"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="node37" class="node">
<title>src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<g id="a_node37"><a xlink:href="src/client/domain/DeckBuilder/DeckBuilder.ts" xlink:title="DeckBuilder.ts">
<polygon fill="#fa9f36" stroke="black" points="1585.62,-220.53 1511.12,-220.53 1511.12,-202.03 1585.62,-202.03 1585.62,-220.53"/>
<text text-anchor="start" x="1519.12" y="-207.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge28" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.68,-951.12C1267.18,-948.58 1284.37,-942.53 1295,-929.28 1307.69,-913.46 1298.05,-583.95 1303,-564.28 1343.98,-401.3 1341.54,-330.07 1474.5,-227.28 1482.51,-221.09 1492.41,-217.19 1502.31,-214.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1502.54,-216.85 1508,-213.59 1501.7,-212.74 1502.54,-216.85"/>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="node38" class="node">
<title>src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<g id="a_node38"><a xlink:href="src/client/domain/DeckBuilder/deckBuilderEvents.ts" xlink:title="deckBuilderEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1882.25,-220.53 1780.75,-220.53 1780.75,-202.03 1882.25,-202.03 1882.25,-220.53"/>
<text text-anchor="start" x="1788.75" y="-207.98" font-family="Helvetica,sans-Serif" font-size="9.00">deckBuilderEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge29" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.3,-951.25C1266.95,-948.77 1284.35,-942.72 1295,-929.28 1308.7,-911.99 1287.51,-150.98 1303,-135.28 1339.19,-98.61 1719.79,-131.26 1766.38,-153.28 1786.95,-163.01 1805.64,-181.55 1817.46,-195.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1815.56,-196.06 1821.04,-199.28 1818.76,-193.34 1815.56,-196.06"/>
</g>
<!-- src/client/application/queries/getCatalogCardById/getCatalogCardById.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge83" class="edge">
<title>src/client/application/queries/getCatalogCardById/getCatalogCardById.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1422.69,-1544.95C1439.21,-1551.07 1456.66,-1560.84 1466.5,-1576.28 1474.67,-1589.1 1473.37,-2108.13 1474.5,-2123.28 1489.84,-2328.06 1532,-2574.01 1544.09,-2642.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1542,-2642.27 1545.12,-2647.81 1546.13,-2641.53 1542,-2642.27"/>
</g>
<!-- src/client/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="node87" class="node">
<title>src/client/application/services/DeckDraftService/DeckDraftService.ts</title>
<g id="a_node87"><a xlink:href="src/client/application/services/DeckDraftService/DeckDraftService.ts" xlink:title="DeckDraftService.ts">
<polygon fill="#fb6969" stroke="black" points="1742.38,-2747.53 1646.12,-2747.53 1646.12,-2729.03 1742.38,-2729.03 1742.38,-2747.53"/>
<text text-anchor="start" x="1654.12" y="-2734.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckDraftService.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="edge99" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1563.61,-2669.88C1578.91,-2680.01 1604.11,-2696.15 1626.88,-2708.28 1638.54,-2714.49 1651.74,-2720.63 1663.28,-2725.72"/>
<polygon fill="none" stroke="#000000" stroke-opacity="0.200000" points="1662.13,-2727.51 1668.47,-2727.99 1663.81,-2723.66 1662.13,-2727.51"/>
</g>
<!-- src/client/application/services/LocationService.ts -->
<g id="node91" class="node">
<title>src/client/application/services/LocationService.ts</title>
<g id="a_node91"><a xlink:href="src/client/application/services/LocationService.ts" xlink:title="LocationService.ts">
<polygon fill="#fb6969" stroke="black" points="1739.75,-2800.53 1648.75,-2800.53 1648.75,-2782.03 1739.75,-2782.03 1739.75,-2800.53"/>
<text text-anchor="start" x="1656.75" y="-2787.98" font-family="Helvetica,sans-Serif" font-size="9.00">LocationService.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/application/services/LocationService.ts -->
<g id="edge100" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/application/services/LocationService.ts</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1552.83,-2670.01C1561.45,-2692.77 1586.17,-2749.62 1626.88,-2777.28 1631.3,-2780.28 1636.23,-2782.66 1641.37,-2784.54"/>
<polygon fill="none" stroke="#000000" stroke-opacity="0.200000" points="1640.66,-2786.52 1647.02,-2786.35 1641.94,-2782.52 1640.66,-2786.52"/>
</g>
<!-- src/client/domain/Catalog/catalogFiltersReducer.ts -->
<g id="node92" class="node">
<title>src/client/domain/Catalog/catalogFiltersReducer.ts</title>
<g id="a_node92"><a xlink:href="src/client/domain/Catalog/catalogFiltersReducer.ts" xlink:title="catalogFiltersReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1888.62,-415.53 1774.38,-415.53 1774.38,-397.03 1888.62,-397.03 1888.62,-415.53"/>
<text text-anchor="start" x="1782.38" y="-402.98" font-family="Helvetica,sans-Serif" font-size="9.00">catalogFiltersReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogFiltersReducer.ts -->
<g id="edge101" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogFiltersReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1550.74,-2650.76C1558.24,-2598.11 1595.04,-2337.23 1618.88,-2123.28 1696.9,-1422.78 1710.53,-1246.9 1766.38,-544.28 1767.45,-530.74 1765.84,-432.85 1774.38,-422.28 1774.5,-422.12 1774.63,-421.97 1774.76,-421.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1776.15,-423.39 1779.21,-417.82 1773.34,-420.26 1776.15,-423.39"/>
</g>
<!-- src/client/domain/Catalog/catalogReducer.ts -->
<g id="node93" class="node">
<title>src/client/domain/Catalog/catalogReducer.ts</title>
<g id="a_node93"><a xlink:href="src/client/domain/Catalog/catalogReducer.ts" xlink:title="catalogReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1739,-459.53 1649.5,-459.53 1649.5,-441.03 1739,-441.03 1739,-459.53"/>
<text text-anchor="start" x="1657.5" y="-446.98" font-family="Helvetica,sans-Serif" font-size="9.00">catalogReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogReducer.ts -->
<g id="edge102" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1551.05,-2650.79C1560.23,-2598.29 1604.51,-2338.08 1618.88,-2123.28 1619.64,-2111.8 1619.73,-475.3 1626.88,-466.28 1630.63,-461.53 1635.53,-458.05 1640.97,-455.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1641.52,-457.54 1646.41,-453.47 1640.05,-453.6 1641.52,-457.54"/>
</g>
<!-- src/client/domain/Catalog/catalogSearchReducer.ts -->
<g id="node94" class="node">
<title>src/client/domain/Catalog/catalogSearchReducer.ts</title>
<g id="a_node94"><a xlink:href="src/client/domain/Catalog/catalogSearchReducer.ts" xlink:title="catalogSearchReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1753.62,-490.53 1634.88,-490.53 1634.88,-472.03 1753.62,-472.03 1753.62,-490.53"/>
<text text-anchor="start" x="1642.88" y="-477.98" font-family="Helvetica,sans-Serif" font-size="9.00">catalogSearchReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogSearchReducer.ts -->
<g id="edge103" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogSearchReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1551.04,-2650.79C1560.18,-2598.29 1604.28,-2338.06 1618.88,-2123.28 1636.23,-1867.86 1608.77,-1226.65 1626.88,-971.28 1640.01,-785.98 1678.37,-563.84 1689.92,-499.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1691.94,-500.16 1690.94,-493.88 1687.81,-499.41 1691.94,-500.16"/>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="node95" class="node">
<title>src/client/domain/DeckBuilder/deckBuilderReducer.ts</title>
<g id="a_node95"><a xlink:href="src/client/domain/DeckBuilder/deckBuilderReducer.ts" xlink:title="deckBuilderReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1748.38,-242.53 1640.12,-242.53 1640.12,-224.03 1748.38,-224.03 1748.38,-242.53"/>
<text text-anchor="start" x="1648.12" y="-229.98" font-family="Helvetica,sans-Serif" font-size="9.00">deckBuilderReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="edge104" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1551.05,-2650.79C1560.24,-2598.29 1604.56,-2338.08 1618.88,-2123.28 1620.57,-2097.78 1615.42,-306.13 1626.88,-283.28 1634.73,-267.61 1650.08,-255.55 1664.04,-247.21"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1664.76,-249.21 1668.96,-244.44 1662.7,-245.55 1664.76,-249.21"/>
</g>
<!-- src/client/domain/GameSettings/gameSettingsReducer.ts -->
<g id="node96" class="node">
<title>src/client/domain/GameSettings/gameSettingsReducer.ts</title>
<g id="a_node96"><a xlink:href="src/client/domain/GameSettings/gameSettingsReducer.ts" xlink:title="gameSettingsReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1606.25,-326.53 1490.5,-326.53 1490.5,-308.03 1606.25,-308.03 1606.25,-326.53"/>
<text text-anchor="start" x="1498.5" y="-313.98" font-family="Helvetica,sans-Serif" font-size="9.00">gameSettingsReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsReducer.ts -->
<g id="edge105" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1546.48,-2650.69C1536.09,-2597.67 1486.02,-2334.82 1470.5,-2117.28 1467.04,-2068.79 1468.46,-1290.85 1470.5,-1242.28 1485.89,-876.05 1535.41,-430.41 1546.26,-335.56"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1548.32,-336.04 1546.92,-329.84 1544.15,-335.56 1548.32,-336.04"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge121" class="edge">
<title>src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1585.84,-211.28C1632.94,-211.28 1715.52,-211.28 1771.58,-211.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1771.31,-213.38 1777.31,-211.28 1771.31,-209.18 1771.31,-213.38"/>
</g>
<!-- src/client/domain/Catalog/CatalogCard.ts -->
<g id="node49" class="node">
<title>src/client/domain/Catalog/CatalogCard.ts</title>
<g id="a_node49"><a xlink:href="src/client/domain/Catalog/CatalogCard.ts" xlink:title="CatalogCard.ts">
<polygon fill="#fa9f36" stroke="black" points="1991.88,-468.53 1914.38,-468.53 1914.38,-450.03 1991.88,-450.03 1991.88,-468.53"/>
<text text-anchor="start" x="1922.38" y="-455.98" font-family="Helvetica,sans-Serif" font-size="9.00">CatalogCard.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts -->
<g id="edge120" class="edge">
<title>src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1586.02,-213C1597.65,-215.19 1609.82,-219.39 1618.88,-227.28 1626.41,-233.84 1619.05,-242.08 1626.88,-248.28 1718.88,-321.15 1807.06,-198.89 1888.62,-283.28 1908.96,-304.32 1888.35,-388.21 1901.62,-414.28 1907.8,-426.41 1918.73,-436.78 1928.85,-444.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1927.44,-446.06 1933.53,-447.86 1929.89,-442.65 1927.44,-446.06"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="edge122" class="edge">
<title>src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1586.05,-216.89C1599.69,-218.98 1615.58,-221.4 1630.88,-223.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1630.56,-225.82 1636.81,-224.65 1631.19,-221.67 1630.56,-225.82"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="node98" class="node">
<title>src/client/domain/DeckBuilder/DeckBuilderCard.ts</title>
<g id="a_node98"><a xlink:href="src/client/domain/DeckBuilder/DeckBuilderCard.ts" xlink:title="DeckBuilderCard.ts">
<polygon fill="#fa9f36" stroke="black" points="2000.88,-248.53 1905.38,-248.53 1905.38,-230.03 2000.88,-230.03 2000.88,-248.53"/>
<text text-anchor="start" x="1913.38" y="-235.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderCard.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderEvents.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="edge124" class="edge">
<title>src/client/domain/DeckBuilder/deckBuilderEvents.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1874,-221C1883,-223.1 1892.62,-225.35 1901.91,-227.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1901.43,-229.57 1907.75,-228.89 1902.38,-225.48 1901.43,-229.57"/>
</g>
<!-- src/client/application/commands/clearDeckDraft/clearDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge30" class="edge">
<title>src/client/application/commands/clearDeckDraft/clearDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1247.74,-1326.01C1265.48,-1331.97 1284.41,-1341.9 1295,-1358.28 1305.78,-1374.96 1288.92,-2774.28 1303,-2788.28 1315.88,-2801.09 1450.59,-2797.05 1466.5,-2788.28 1509.5,-2764.58 1532.81,-2707.33 1542.27,-2678.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1544.21,-2678.91 1543.97,-2672.56 1540.2,-2677.68 1544.21,-2678.91"/>
</g>
<!-- src/client/application/commands/filterCatalog/filterCatalog.ts -->
<g id="node40" class="node">
<title>src/client/application/commands/filterCatalog/filterCatalog.ts</title>
<g id="a_node40"><a xlink:href="src/client/application/commands/filterCatalog/filterCatalog.ts" xlink:title="filterCatalog.ts">
<polygon fill="#fb6969" stroke="black" points="1418,-1265.53 1343.5,-1265.53 1343.5,-1247.03 1418,-1247.03 1418,-1265.53"/>
<text text-anchor="start" x="1351.5" y="-1252.98" font-family="Helvetica,sans-Serif" font-size="9.00">filterCatalog.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge32" class="edge">
<title>src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1401.13,-1265.96C1421.67,-1277.43 1452.96,-1298.65 1466.5,-1327.28 1475.95,-1347.27 1472.9,-2101.23 1474.5,-2123.28 1489.37,-2328.09 1531.86,-2574.02 1544.06,-2642.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1541.97,-2642.28 1545.1,-2647.81 1546.1,-2641.54 1541.97,-2642.28"/>
</g>
<!-- src/client/application/commands/filterCatalog/filterCatalogRequest.ts -->
<g id="node41" class="node">
<title>src/client/application/commands/filterCatalog/filterCatalogRequest.ts</title>
<g id="a_node41"><a xlink:href="src/client/application/commands/filterCatalog/filterCatalogRequest.ts" xlink:title="filterCatalogRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1602.88,-1265.53 1493.88,-1265.53 1493.88,-1247.03 1602.88,-1247.03 1602.88,-1265.53"/>
<text text-anchor="start" x="1501.88" y="-1252.98" font-family="Helvetica,sans-Serif" font-size="9.00">filterCatalogRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalogRequest.ts -->
<g id="edge31" class="edge">
<title>src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalogRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1418.1,-1256.28C1437.6,-1256.28 1462.19,-1256.28 1484.67,-1256.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1484.47,-1258.38 1490.47,-1256.28 1484.47,-1254.18 1484.47,-1258.38"/>
</g>
<!-- src/client/domain/Catalog/CatalogFilters.ts -->
<g id="node42" class="node">
<title>src/client/domain/Catalog/CatalogFilters.ts</title>
<g id="a_node42"><a xlink:href="src/client/domain/Catalog/CatalogFilters.ts" xlink:title="CatalogFilters.ts">
<polygon fill="#fa9f36" stroke="black" points="1735.25,-404.53 1653.25,-404.53 1653.25,-386.03 1735.25,-386.03 1735.25,-404.53"/>
<text text-anchor="start" x="1661.25" y="-391.98" font-family="Helvetica,sans-Serif" font-size="9.00">CatalogFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/domain/Catalog/CatalogFilters.ts -->
<g id="edge33" class="edge">
<title>src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/domain/Catalog/CatalogFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1397.58,-1246.56C1417.92,-1233.03 1452.32,-1206.41 1466.5,-1173.28 1480.12,-1141.46 1458.87,-581.16 1474.5,-550.28 1512.43,-475.34 1605.58,-429.03 1657.25,-408.23"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1657.85,-410.25 1662.66,-406.1 1656.31,-406.34 1657.85,-410.25"/>
</g>
<!-- src/client/domain/Catalog/catalogFilterEvents.ts -->
<g id="node67" class="node">
<title>src/client/domain/Catalog/catalogFilterEvents.ts</title>
<g id="a_node67"><a xlink:href="src/client/domain/Catalog/catalogFilterEvents.ts" xlink:title="catalogFilterEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="2004.62,-408.53 1901.62,-408.53 1901.62,-390.03 2004.62,-390.03 2004.62,-408.53"/>
<text text-anchor="start" x="1909.62" y="-395.98" font-family="Helvetica,sans-Serif" font-size="9.00">catalogFilterEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts -->
<g id="edge110" class="edge">
<title>src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1735.66,-392.49C1774.81,-390.25 1835.84,-387.97 1888.62,-391.28 1889.91,-391.36 1891.2,-391.45 1892.51,-391.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1892.06,-393.62 1898.21,-392.04 1892.42,-389.43 1892.06,-393.62"/>
</g>
<!-- src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge112" class="edge">
<title>src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1735.62,-388.45C1809.81,-376.77 1964.33,-356.75 2008.88,-384.28 2036.62,-401.43 2049.03,-439.79 2054.15,-462.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2052.08,-463.18 2055.32,-468.65 2056.2,-462.35 2052.08,-463.18"/>
</g>
<!-- src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFiltersReducer.ts -->
<g id="edge111" class="edge">
<title>src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFiltersReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1735.46,-398.55C1744.8,-399.31 1754.97,-400.14 1765.08,-400.96"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1764.76,-403.04 1770.91,-401.43 1765.1,-398.85 1764.76,-403.04"/>
</g>
<!-- src/client/application/commands/hideCardDetails/hideCardDetails.ts -->
<g id="node43" class="node">
<title>src/client/application/commands/hideCardDetails/hideCardDetails.ts</title>
<g id="a_node43"><a xlink:href="src/client/application/commands/hideCardDetails/hideCardDetails.ts" xlink:title="hideCardDetails.ts">
<polygon fill="#fb6969" stroke="black" points="1250.25,-655.53 1158.5,-655.53 1158.5,-637.03 1250.25,-637.03 1250.25,-655.53"/>
<text text-anchor="start" x="1166.5" y="-642.98" font-family="Helvetica,sans-Serif" font-size="9.00">hideCardDetails.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge34" class="edge">
<title>src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.56,-655.98C1267.47,-662.03 1284.98,-671.75 1295,-687.28 1309.49,-709.74 1286.02,-2590.65 1303,-2611.28 1315.97,-2627.04 1442.5,-2646.18 1508.2,-2655.16"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1507.82,-2657.23 1514.05,-2655.95 1508.39,-2653.07 1507.82,-2657.23"/>
</g>
<!-- src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge35" class="edge">
<title>src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1205.95,-636.54C1209.67,-577.1 1232.5,-263.83 1303,-207.28 1363.7,-158.59 1462.84,-181.72 1514.2,-198.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1513.4,-200.69 1519.75,-200.65 1514.76,-196.72 1513.4,-200.69"/>
</g>
<!-- src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge36" class="edge">
<title>src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1210.34,-636.59C1222.53,-614.03 1256.33,-558.24 1303,-536.28 1349.62,-514.35 1729.96,-556.72 1766.38,-520.28 1775.69,-510.96 1769.88,-295.67 1774.38,-283.28 1782.46,-261 1800.24,-240.18 1813.64,-226.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1814.8,-228.53 1817.64,-222.84 1811.87,-225.52 1814.8,-228.53"/>
</g>
<!-- src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts -->
<g id="node44" class="node">
<title>src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts</title>
<g id="a_node44"><a xlink:href="src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts" xlink:title="initializeDeckBuilderFromLocation.ts">
<polygon fill="#fb6969" stroke="black" points="1287,-1204.53 1121.75,-1204.53 1121.75,-1186.03 1287,-1186.03 1287,-1204.53"/>
<text text-anchor="start" x="1129.75" y="-1191.98" font-family="Helvetica,sans-Serif" font-size="9.00">initializeDeckBuilderFromLocation.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge39" class="edge">
<title>src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.55,-1204.98C1267.46,-1211.04 1284.97,-1220.76 1295,-1236.28 1306.48,-1254.06 1287.99,-2745.35 1303,-2760.28 1315.88,-2773.09 1450.11,-2768.11 1466.5,-2760.28 1502.39,-2743.15 1527.49,-2701.44 1539.44,-2677.66"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1541.21,-2678.82 1541.93,-2672.51 1537.43,-2677 1541.21,-2678.82"/>
</g>
<!-- src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalog.ts -->
<g id="edge37" class="edge">
<title>src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalog.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1233.01,-1204.95C1262.89,-1215.4 1310.66,-1232.11 1343.53,-1243.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1342.75,-1245.56 1349.1,-1245.56 1344.13,-1241.6 1342.75,-1245.56"/>
</g>
<!-- src/client/application/commands/search/search.ts -->
<g id="node45" class="node">
<title>src/client/application/commands/search/search.ts</title>
<g id="a_node45"><a xlink:href="src/client/application/commands/search/search.ts" xlink:title="search.ts">
<polygon fill="#fb6969" stroke="black" points="1407.75,-1356.53 1353.75,-1356.53 1353.75,-1338.03 1407.75,-1338.03 1407.75,-1356.53"/>
<text text-anchor="start" x="1362.38" y="-1343.98" font-family="Helvetica,sans-Serif" font-size="9.00">search.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/search/search.ts -->
<g id="edge38" class="edge">
<title>src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/search/search.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1247.71,-1204.99C1265,-1211.12 1283.58,-1220.89 1295,-1236.28 1310.77,-1257.53 1289.05,-1272.79 1303,-1295.28 1313.57,-1312.33 1332.03,-1325.1 1348.2,-1333.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1346.91,-1335.42 1353.21,-1336.25 1348.8,-1331.67 1346.91,-1335.42"/>
</g>
<!-- src/client/application/commands/search/search.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge61" class="edge">
<title>src/client/application/commands/search/search.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1393.05,-1356.76C1412.47,-1373.86 1451.52,-1412.07 1466.5,-1454.28 1478.93,-1489.31 1471.77,-2086.21 1474.5,-2123.28 1489.56,-2328.08 1531.92,-2574.02 1544.07,-2642.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1541.98,-2642.28 1545.11,-2647.81 1546.11,-2641.53 1541.98,-2642.28"/>
</g>
<!-- src/client/application/commands/search/searchRequest.ts -->
<g id="node59" class="node">
<title>src/client/application/commands/search/searchRequest.ts</title>
<g id="a_node59"><a xlink:href="src/client/application/commands/search/searchRequest.ts" xlink:title="searchRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1592,-1356.53 1504.75,-1356.53 1504.75,-1338.03 1592,-1338.03 1592,-1356.53"/>
<text text-anchor="start" x="1512.75" y="-1343.98" font-family="Helvetica,sans-Serif" font-size="9.00">searchRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/search/search.ts&#45;&gt;src/client/application/commands/search/searchRequest.ts -->
<g id="edge60" class="edge">
<title>src/client/application/commands/search/search.ts&#45;&gt;src/client/application/commands/search/searchRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1407.99,-1347.28C1431.47,-1347.28 1466.56,-1347.28 1495.66,-1347.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1495.46,-1349.38 1501.46,-1347.28 1495.46,-1345.18 1495.46,-1349.38"/>
</g>
<!-- src/client/domain/Catalog/catalogSearchEvents.ts -->
<g id="node60" class="node">
<title>src/client/domain/Catalog/catalogSearchEvents.ts</title>
<g id="a_node60"><a xlink:href="src/client/domain/Catalog/catalogSearchEvents.ts" xlink:title="catalogSearchEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1887.5,-490.53 1775.5,-490.53 1775.5,-472.03 1887.5,-472.03 1887.5,-490.53"/>
<text text-anchor="start" x="1783.5" y="-477.98" font-family="Helvetica,sans-Serif" font-size="9.00">catalogSearchEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/search/search.ts&#45;&gt;src/client/domain/Catalog/catalogSearchEvents.ts -->
<g id="edge62" class="edge">
<title>src/client/application/commands/search/search.ts&#45;&gt;src/client/domain/Catalog/catalogSearchEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1407.95,-1339.03C1427.89,-1331.22 1453.98,-1317.35 1466.5,-1295.28 1476.03,-1278.49 1462.12,-615.09 1474.5,-600.28 1559.23,-498.95 1649.4,-605.63 1766.38,-544.28 1787.8,-533.05 1806.78,-512.32 1818.43,-497.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1819.98,-499.15 1821.98,-493.12 1816.65,-496.59 1819.98,-499.15"/>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCards.ts -->
<g id="node46" class="node">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCards.ts</title>
<g id="a_node46"><a xlink:href="src/client/application/commands/loadCatalogCards/loadCatalogCards.ts" xlink:title="loadCatalogCards.ts">
<polygon fill="#fb6969" stroke="black" points="1254.38,-1021.53 1154.38,-1021.53 1154.38,-1003.03 1254.38,-1003.03 1254.38,-1021.53"/>
<text text-anchor="start" x="1162.38" y="-1008.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadCatalogCards.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge41" class="edge">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.55,-1021.98C1267.46,-1028.03 1284.98,-1037.76 1295,-1053.28 1307.54,-1072.71 1286.61,-2701.97 1303,-2718.28 1328.75,-2743.91 1431.91,-2729.4 1466.5,-2718.28 1492.46,-2709.93 1517.15,-2689.83 1532.27,-2675.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1533.49,-2677.39 1536.34,-2671.71 1530.57,-2674.37 1533.49,-2677.39"/>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts -->
<g id="node47" class="node">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts</title>
<g id="a_node47"><a xlink:href="src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts" xlink:title="loadCatalogCardsRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1448,-1021.53 1313.5,-1021.53 1313.5,-1003.03 1448,-1003.03 1448,-1021.53"/>
<text text-anchor="start" x="1321.5" y="-1008.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadCatalogCardsRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts -->
<g id="edge40" class="edge">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1254.41,-1012.28C1269.94,-1012.28 1287.48,-1012.28 1304.41,-1012.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1304.16,-1014.38 1310.16,-1012.28 1304.16,-1010.18 1304.16,-1014.38"/>
</g>
<!-- src/client/domain/Catalog/catalogEvents.ts -->
<g id="node48" class="node">
<title>src/client/domain/Catalog/catalogEvents.ts</title>
<g id="a_node48"><a xlink:href="src/client/domain/Catalog/catalogEvents.ts" xlink:title="catalogEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1872.88,-446.53 1790.12,-446.53 1790.12,-428.03 1872.88,-428.03 1872.88,-446.53"/>
<text text-anchor="start" x="1798.12" y="-433.98" font-family="Helvetica,sans-Serif" font-size="9.00">catalogEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/domain/Catalog/catalogEvents.ts -->
<g id="edge42" class="edge">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/domain/Catalog/catalogEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1254.51,-1011.55C1269.83,-1008.7 1285.25,-1002.59 1295,-990.28 1308.7,-972.99 1289.06,-213.37 1303,-196.28 1368.09,-116.48 1700.35,-112.25 1766.38,-191.28 1782.77,-210.9 1758.62,-401.14 1774.38,-421.28 1776.56,-424.07 1779.2,-426.4 1782.12,-428.34"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1780.91,-430.08 1787.2,-431 1782.86,-426.36 1780.91,-430.08"/>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts -->
<g id="edge43" class="edge">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1437.86,-1002.6C1447.81,-999.55 1457.77,-995.53 1466.5,-990.28 1705.06,-846.63 1896.37,-550.55 1941.84,-476.4"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1943.54,-477.64 1944.87,-471.43 1939.95,-475.46 1943.54,-477.64"/>
</g>
<!-- src/client/domain/Catalog/catalogEvents.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts -->
<g id="edge113" class="edge">
<title>src/client/domain/Catalog/catalogEvents.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1873.33,-444.79C1883.59,-446.68 1894.68,-448.72 1905.21,-450.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1904.64,-452.68 1910.92,-451.7 1905.39,-448.55 1904.64,-452.68"/>
</g>
<!-- src/client/domain/CardData/CardData..ts -->
<g id="node70" class="node">
<title>src/client/domain/CardData/CardData..ts</title>
<g id="a_node70"><a xlink:href="src/client/domain/CardData/CardData..ts" xlink:title="CardData..ts">
<polygon fill="#fa9f36" stroke="black" points="2091.88,-551.53 2024.88,-551.53 2024.88,-533.03 2091.88,-533.03 2091.88,-551.53"/>
<text text-anchor="start" x="2032.88" y="-538.98" font-family="Helvetica,sans-Serif" font-size="9.00">CardData..ts</text>
</a>
</g>
</g>
<!-- src/client/domain/Catalog/CatalogCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge109" class="edge">
<title>src/client/domain/Catalog/CatalogCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1962.37,-468.79C1973.76,-481.54 1995.34,-504.43 2016.88,-520.28 2020.74,-523.13 2025.02,-525.85 2029.31,-528.34"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2028.03,-530.04 2034.29,-531.11 2030.06,-526.36 2028.03,-530.04"/>
</g>
<!-- src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge44" class="edge">
<title>src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1246.57,-1264.85C1264.53,-1270.82 1283.97,-1280.78 1295,-1297.28 1308.35,-1317.25 1288.19,-1494.37 1303,-1513.28 1307.05,-1518.46 1312.24,-1522.47 1318,-1525.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1317.07,-1527.46 1323.4,-1528.02 1318.8,-1523.63 1317.07,-1527.46"/>
</g>
<!-- src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge45" class="edge">
<title>src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1246.34,-1264.54C1264.48,-1270.46 1284.14,-1280.47 1295,-1297.28 1306.13,-1314.51 1288.46,-2759.81 1303,-2774.28 1315.88,-2787.09 1450.36,-2782.62 1466.5,-2774.28 1505.82,-2753.97 1530.16,-2704.74 1540.91,-2678.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1542.74,-2679.19 1542.95,-2672.83 1538.83,-2677.67 1542.74,-2679.19"/>
</g>
<!-- src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge46" class="edge">
<title>src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1246.55,-1256.73C1264.27,-1254.6 1283.52,-1248.7 1295,-1234.28 1304.46,-1222.39 1299.31,-702.02 1303,-687.28 1341.51,-533.51 1425.58,-523.43 1466.5,-370.28 1471.5,-351.58 1460.5,-209.65 1474.5,-196.28 1516.33,-156.33 1687.04,-182.76 1776,-199.9"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1775.35,-201.91 1781.64,-201 1776.15,-197.79 1775.35,-201.91"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge48" class="edge">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.5,-778.01C1267.4,-784.07 1284.93,-793.79 1295,-809.28 1305.66,-825.68 1291.14,-1497.73 1303,-1513.28 1307.14,-1518.71 1312.52,-1522.86 1318.52,-1526.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1317.29,-1527.78 1323.63,-1528.3 1319,-1523.94 1317.29,-1527.78"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge49" class="edge">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.56,-777.98C1267.46,-784.03 1284.98,-793.75 1295,-809.28 1308.95,-830.91 1284.76,-2644.12 1303,-2662.28 1317.14,-2676.36 1442.5,-2668.66 1507.96,-2663.6"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1507.94,-2665.7 1513.76,-2663.14 1507.61,-2661.52 1507.94,-2665.7"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge50" class="edge">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1258.33,-765.84C1272.04,-762.64 1285.57,-756.76 1295,-746.28 1312.69,-726.6 1289.91,-710.28 1303,-687.28 1351.13,-602.74 1383.72,-586.25 1474.5,-551.28 1489.66,-545.44 1755.08,-543.96 1766.38,-532.28 1776,-522.33 1769.67,-296.3 1774.38,-283.28 1782.44,-261 1800.23,-240.18 1813.63,-226.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1814.79,-228.52 1817.63,-222.84 1811.86,-225.51 1814.79,-228.52"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts -->
<g id="node52" class="node">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts</title>
<g id="a_node52"><a xlink:href="src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts" xlink:title="loadDeckIntoBuilderRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1452.12,-777.53 1309.38,-777.53 1309.38,-759.03 1452.12,-759.03 1452.12,-777.53"/>
<text text-anchor="start" x="1317.38" y="-764.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadDeckIntoBuilderRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts -->
<g id="edge47" class="edge">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1258.17,-768.28C1271.45,-768.28 1286,-768.28 1300.27,-768.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1300.08,-770.38 1306.08,-768.28 1300.08,-766.18 1300.08,-770.38"/>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettings.ts -->
<g id="node53" class="node">
<title>src/client/application/commands/loadGameSettings/loadGameSettings.ts</title>
<g id="a_node53"><a xlink:href="src/client/application/commands/loadGameSettings/loadGameSettings.ts" xlink:title="loadGameSettings.ts">
<polygon fill="#fb6969" stroke="black" points="1254.75,-1082.53 1154,-1082.53 1154,-1064.03 1254.75,-1064.03 1254.75,-1082.53"/>
<text text-anchor="start" x="1162" y="-1069.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadGameSettings.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge52" class="edge">
<title>src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.55,-1082.98C1267.46,-1089.03 1284.98,-1098.76 1295,-1114.28 1307.19,-1133.16 1287.07,-2716.43 1303,-2732.28 1328.75,-2757.91 1432.54,-2745.21 1466.5,-2732.28 1495.55,-2721.22 1520.82,-2694.4 1535.07,-2676.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1536.57,-2678.3 1538.61,-2672.28 1533.26,-2675.71 1536.57,-2678.3"/>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts -->
<g id="node54" class="node">
<title>src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts</title>
<g id="a_node54"><a xlink:href="src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts" xlink:title="loadGameSettingsRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1448.38,-1082.53 1313.12,-1082.53 1313.12,-1064.03 1448.38,-1064.03 1448.38,-1082.53"/>
<text text-anchor="start" x="1321.12" y="-1069.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadGameSettingsRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts -->
<g id="edge51" class="edge">
<title>src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1254.88,-1073.28C1270.23,-1073.28 1287.5,-1073.28 1304.2,-1073.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1303.84,-1075.38 1309.84,-1073.28 1303.84,-1071.18 1303.84,-1075.38"/>
</g>
<!-- src/client/domain/GameSettings/gameSettingsEvents.ts -->
<g id="node55" class="node">
<title>src/client/domain/GameSettings/gameSettingsEvents.ts</title>
<g id="a_node55"><a xlink:href="src/client/domain/GameSettings/gameSettingsEvents.ts" xlink:title="gameSettingsEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1748.75,-312.53 1639.75,-312.53 1639.75,-294.03 1748.75,-294.03 1748.75,-312.53"/>
<text text-anchor="start" x="1647.75" y="-299.98" font-family="Helvetica,sans-Serif" font-size="9.00">gameSettingsEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsEvents.ts -->
<g id="edge53" class="edge">
<title>src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1254.94,-1072.43C1270.1,-1069.53 1285.31,-1063.43 1295,-1051.28 1303.59,-1040.51 1299.88,-568.7 1303,-555.28 1343.46,-381.08 1323.48,-287.07 1474.5,-191.28 1528.69,-156.91 1571.54,-147.96 1618.88,-191.28 1633.69,-204.84 1614.41,-265.54 1626.88,-281.28 1629.21,-284.23 1631.94,-286.78 1634.95,-288.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1633.62,-290.64 1639.83,-291.99 1635.82,-287.06 1633.62,-290.64"/>
</g>
<!-- src/client/domain/GameSettings/GameSettings.ts -->
<g id="node99" class="node">
<title>src/client/domain/GameSettings/GameSettings.ts</title>
<g id="a_node99"><a xlink:href="src/client/domain/GameSettings/GameSettings.ts" xlink:title="GameSettings.ts">
<polygon fill="#fa9f36" stroke="black" points="1872.88,-319.53 1790.12,-319.53 1790.12,-301.03 1872.88,-301.03 1872.88,-319.53"/>
<text text-anchor="start" x="1798.12" y="-306.98" font-family="Helvetica,sans-Serif" font-size="9.00">GameSettings.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/GameSettings/gameSettingsEvents.ts&#45;&gt;src/client/domain/GameSettings/GameSettings.ts -->
<g id="edge127" class="edge">
<title>src/client/domain/GameSettings/gameSettingsEvents.ts&#45;&gt;src/client/domain/GameSettings/GameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1749.09,-306.07C1759.58,-306.61 1770.52,-307.18 1780.86,-307.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1780.64,-309.8 1786.74,-308.02 1780.86,-305.61 1780.64,-309.8"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts -->
<g id="node56" class="node">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts</title>
<g id="a_node56"><a xlink:href="src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts" xlink:title="removeCardFromDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1263.38,-1143.53 1145.38,-1143.53 1145.38,-1125.03 1263.38,-1125.03 1263.38,-1143.53"/>
<text text-anchor="start" x="1153.38" y="-1130.98" font-family="Helvetica,sans-Serif" font-size="9.00">removeCardFromDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge54" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1249.94,-1143.91C1266.97,-1149.96 1284.74,-1159.71 1295,-1175.28 1305.34,-1190.96 1291.52,-1498.42 1303,-1513.28 1307.17,-1518.68 1312.57,-1522.82 1318.58,-1525.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1317.37,-1527.73 1323.7,-1528.25 1319.07,-1523.9 1317.37,-1527.73"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge56" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.55,-1143.98C1267.46,-1150.03 1284.97,-1159.76 1295,-1175.28 1306.84,-1193.61 1287.53,-2730.89 1303,-2746.28 1315.88,-2759.09 1449.82,-2753.49 1466.5,-2746.28 1498.89,-2732.28 1524.29,-2698.22 1537.45,-2677.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1539.13,-2678.7 1540.46,-2672.48 1535.54,-2676.51 1539.13,-2678.7"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge57" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1263.79,-1131.32C1275.83,-1127.9 1287.2,-1122.04 1295,-1112.28 1309.75,-1093.84 1292.5,-708.43 1303,-687.28 1344.16,-604.37 1425.12,-641.08 1466.5,-558.28 1474.72,-541.83 1462.8,-241.47 1474.5,-227.28 1481.32,-219 1491.36,-214.41 1501.81,-211.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1502.11,-214.06 1507.65,-210.94 1501.38,-209.92 1502.11,-214.06"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge58" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1263.82,-1131.34C1275.86,-1127.92 1287.22,-1122.06 1295,-1112.28 1304.57,-1100.26 1299.75,-574.29 1303,-559.28 1343.28,-373.43 1315.35,-271.37 1474.5,-167.28 1528.78,-131.78 1704.42,-148.07 1766.38,-167.28 1784.49,-172.9 1802.02,-185.59 1814.17,-195.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1812.5,-197.24 1818.39,-199.64 1815.28,-194.09 1812.5,-197.24"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts -->
<g id="node57" class="node">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts</title>
<g id="a_node57"><a xlink:href="src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts" xlink:title="removeCardFromDeckRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1457,-1143.53 1304.5,-1143.53 1304.5,-1125.03 1457,-1125.03 1457,-1143.53"/>
<text text-anchor="start" x="1312.5" y="-1130.98" font-family="Helvetica,sans-Serif" font-size="9.00">removeCardFromDeckRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts -->
<g id="edge55" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1263.44,-1134.28C1273.66,-1134.28 1284.48,-1134.28 1295.24,-1134.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1295.23,-1136.38 1301.23,-1134.28 1295.23,-1132.18 1295.23,-1136.38"/>
</g>
<!-- src/client/application/commands/saveDeckDraft/saveDeckDraft.ts -->
<g id="node58" class="node">
<title>src/client/application/commands/saveDeckDraft/saveDeckDraft.ts</title>
<g id="a_node58"><a xlink:href="src/client/application/commands/saveDeckDraft/saveDeckDraft.ts" xlink:title="saveDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1246.88,-1387.53 1161.88,-1387.53 1161.88,-1369.03 1246.88,-1369.03 1246.88,-1387.53"/>
<text text-anchor="start" x="1169.88" y="-1374.98" font-family="Helvetica,sans-Serif" font-size="9.00">saveDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/saveDeckDraft/saveDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge59" class="edge">
<title>src/client/application/commands/saveDeckDraft/saveDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1206.18,-1387.96C1213.22,-1473.31 1265.08,-2106.67 1295,-2621.28 1295.58,-2631.33 1295.73,-2795.32 1303,-2802.28 1329.25,-2827.4 1435.09,-2820.54 1466.5,-2802.28 1512.96,-2775.28 1534.94,-2710.5 1543.24,-2678.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1545.27,-2679.14 1544.66,-2672.81 1541.19,-2678.14 1545.27,-2679.14"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts -->
<g id="node61" class="node">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts</title>
<g id="a_node61"><a xlink:href="src/client/application/commands/showCardDetails/showCardDetails.ts" xlink:title="showCardDetails.ts">
<polygon fill="#fb6969" stroke="black" points="1252.12,-716.53 1156.62,-716.53 1156.62,-698.03 1252.12,-698.03 1252.12,-716.53"/>
<text text-anchor="start" x="1164.62" y="-703.98" font-family="Helvetica,sans-Serif" font-size="9.00">showCardDetails.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge64" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.51,-717.01C1267.41,-723.07 1284.93,-732.78 1295,-748.28 1306.58,-766.1 1290.12,-1496.38 1303,-1513.28 1307.13,-1518.71 1312.52,-1522.86 1318.51,-1526.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1317.29,-1527.78 1323.62,-1528.31 1319,-1523.95 1317.29,-1527.78"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge65" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.56,-716.98C1267.46,-723.03 1284.98,-732.75 1295,-748.28 1309.31,-770.46 1284.76,-2629.21 1303,-2648.28 1316.81,-2662.72 1442.32,-2662.42 1507.89,-2661.24"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1507.74,-2663.35 1513.69,-2661.13 1507.66,-2659.15 1507.74,-2663.35"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge66" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1252.59,-706.84C1268.51,-704.14 1284.8,-698.05 1295,-685.28 1311.08,-665.13 1284.99,-239.73 1303,-221.28 1329.58,-194.06 1438.68,-200.05 1501.75,-206.09"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1501.52,-208.18 1507.7,-206.68 1501.94,-204 1501.52,-208.18"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge67" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1252.61,-706.86C1268.54,-704.16 1284.83,-698.07 1295,-685.28 1305.41,-672.19 1291.1,-95.03 1303,-83.28 1346.69,-40.17 1673.77,-40.18 1766.38,-97.28 1801.32,-118.83 1818.89,-166.8 1826.2,-193.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1824.14,-193.55 1827.68,-198.83 1828.2,-192.49 1824.14,-193.55"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetailsRequest.ts -->
<g id="node62" class="node">
<title>src/client/application/commands/showCardDetails/showCardDetailsRequest.ts</title>
<g id="a_node62"><a xlink:href="src/client/application/commands/showCardDetails/showCardDetailsRequest.ts" xlink:title="showCardDetailsRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1445.75,-716.53 1315.75,-716.53 1315.75,-698.03 1445.75,-698.03 1445.75,-716.53"/>
<text text-anchor="start" x="1323.75" y="-703.98" font-family="Helvetica,sans-Serif" font-size="9.00">showCardDetailsRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/commands/showCardDetails/showCardDetailsRequest.ts -->
<g id="edge63" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/commands/showCardDetails/showCardDetailsRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1252.56,-707.28C1269.21,-707.28 1288.33,-707.28 1306.61,-707.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1306.47,-709.38 1312.47,-707.28 1306.47,-705.18 1306.47,-709.38"/>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts -->
<g id="node63" class="node">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts</title>
<g id="a_node63"><a xlink:href="src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts" xlink:title="switchDeckBuilderView.ts">
<polygon fill="#fb6969" stroke="black" points="1264.5,-838.53 1144.25,-838.53 1144.25,-820.03 1264.5,-820.03 1264.5,-838.53"/>
<text text-anchor="start" x="1152.25" y="-825.98" font-family="Helvetica,sans-Serif" font-size="9.00">switchDeckBuilderView.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge69" class="edge">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.56,-838.98C1267.46,-845.03 1284.98,-854.75 1295,-870.28 1308.6,-891.36 1285.22,-2658.58 1303,-2676.28 1330.93,-2704.09 1446.56,-2683.23 1508.22,-2669.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1508.57,-2671.68 1513.96,-2668.32 1507.65,-2667.59 1508.57,-2671.68"/>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge70" class="edge">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1264.75,-826.08C1276.44,-822.62 1287.42,-816.81 1295,-807.28 1314.79,-782.41 1281.49,-258.68 1303,-235.28 1309.64,-228.06 1432.96,-218.82 1501.82,-214.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1501.88,-216.3 1507.73,-213.8 1501.6,-212.11 1501.88,-216.3"/>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge71" class="edge">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1264.77,-826.09C1276.46,-822.63 1287.43,-816.82 1295,-807.28 1307.02,-792.14 1289.25,-124.87 1303,-111.28 1339.61,-75.08 1721.2,-86.58 1766.38,-111.28 1798.17,-128.66 1816.52,-169.28 1824.91,-193.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1822.89,-193.64 1826.78,-198.67 1826.88,-192.32 1822.89,-193.64"/>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts -->
<g id="node64" class="node">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts</title>
<g id="a_node64"><a xlink:href="src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts" xlink:title="switchDeckBuilderViewRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1458.5,-838.53 1303,-838.53 1303,-820.03 1458.5,-820.03 1458.5,-838.53"/>
<text text-anchor="start" x="1311" y="-825.98" font-family="Helvetica,sans-Serif" font-size="9.00">switchDeckBuilderViewRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts -->
<g id="edge68" class="edge">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1264.9,-829.28C1274.19,-829.28 1283.95,-829.28 1293.69,-829.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1293.59,-831.38 1299.59,-829.28 1293.59,-827.18 1293.59,-831.38"/>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts -->
<g id="node65" class="node">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts</title>
<g id="a_node65"><a xlink:href="src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts" xlink:title="updateAvailableFilters.ts">
<polygon fill="#fb6969" stroke="black" points="1262.62,-899.53 1146.12,-899.53 1146.12,-881.03 1262.62,-881.03 1262.62,-899.53"/>
<text text-anchor="start" x="1154.12" y="-886.98" font-family="Helvetica,sans-Serif" font-size="9.00">updateAvailableFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge73" class="edge">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.56,-899.98C1267.46,-906.03 1284.98,-915.75 1295,-931.28 1308.25,-951.81 1285.69,-2673.05 1303,-2690.28 1328.75,-2715.91 1430.74,-2696.7 1466.5,-2690.28 1484.66,-2687.02 1504.07,-2679.95 1519.32,-2673.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1519.9,-2675.53 1524.56,-2671.21 1518.22,-2671.68 1519.9,-2675.53"/>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts -->
<g id="node66" class="node">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts</title>
<g id="a_node66"><a xlink:href="src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts" xlink:title="updateAvailableFiltersRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1456.25,-899.53 1305.25,-899.53 1305.25,-881.03 1456.25,-881.03 1456.25,-899.53"/>
<text text-anchor="start" x="1313.25" y="-886.98" font-family="Helvetica,sans-Serif" font-size="9.00">updateAvailableFiltersRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts -->
<g id="edge72" class="edge">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1262.96,-890.28C1273.52,-890.28 1284.75,-890.28 1295.9,-890.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1295.79,-892.38 1301.79,-890.28 1295.79,-888.18 1295.79,-892.38"/>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts -->
<g id="edge74" class="edge">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1262.92,-887.63C1275.3,-884.24 1287.04,-878.32 1295,-868.28 1308,-851.89 1288.13,-129.99 1303,-115.28 1489.57,69.32 1736.99,22.94 1888.62,-191.28 1911.98,-224.27 1890.69,-242.37 1901.62,-281.28 1912.05,-318.4 1931.46,-359.25 1942.94,-381.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1941.05,-382.74 1945.66,-387.11 1944.78,-380.82 1941.05,-382.74"/>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge75" class="edge">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1440.49,-880.54C1449.65,-877.5 1458.66,-873.51 1466.5,-868.28 1605.49,-775.46 1526.12,-568.28 1693.25,-568.28 1693.25,-568.28 1693.25,-568.28 1832.5,-568.28 1911.83,-568.28 1996.95,-520.6 2035.81,-495.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2036.95,-497.6 2040.84,-492.57 2034.66,-494.07 2036.95,-497.6"/>
</g>
<!-- src/client/domain/Catalog/catalogFilterEvents.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge114" class="edge">
<title>src/client/domain/Catalog/catalogFilterEvents.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1980.17,-409.01C1989.7,-413.23 2000.25,-418.72 2008.88,-425.28 2023.49,-436.39 2036.88,-452.52 2045.88,-464.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2044.14,-465.8 2049.35,-469.44 2047.54,-463.34 2044.14,-465.8"/>
</g>
<!-- src/client/application/queries/applyFilterToCard/applyFilterToCard.ts -->
<g id="node69" class="node">
<title>src/client/application/queries/applyFilterToCard/applyFilterToCard.ts</title>
<g id="a_node69"><a xlink:href="src/client/application/queries/applyFilterToCard/applyFilterToCard.ts" xlink:title="applyFilterToCard.ts">
<polygon fill="#fb6969" stroke="black" points="1597.25,-2093.53 1499.5,-2093.53 1499.5,-2075.03 1597.25,-2075.03 1597.25,-2093.53"/>
<text text-anchor="start" x="1507.5" y="-2080.98" font-family="Helvetica,sans-Serif" font-size="9.00">applyFilterToCard.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/applyFilterToCard/applyFilterToCard.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge77" class="edge">
<title>src/client/application/queries/applyFilterToCard/applyFilterToCard.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1552.8,-2074.82C1586.51,-1981.38 1860.79,-1213.86 2008.88,-572.28 2013.94,-550.35 2006.7,-542.36 2016.88,-522.28 2021.79,-512.58 2029.84,-503.72 2037.47,-496.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2038.79,-498.38 2041.96,-492.87 2036.05,-495.19 2038.79,-498.38"/>
</g>
<!-- src/client/application/queries/applyFilterToCard/applyFilterToCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge76" class="edge">
<title>src/client/application/queries/applyFilterToCard/applyFilterToCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1572.76,-2074.65C1651.75,-2040.67 1909.29,-1917.9 2008.88,-1723.28 2064.05,-1615.46 2058.71,-702.11 2057.54,-560.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2059.64,-560.83 2057.49,-554.85 2055.44,-560.87 2059.64,-560.83"/>
</g>
<!-- src/client/application/queries/findDeckCardById/findDeckCardById.ts -->
<g id="node71" class="node">
<title>src/client/application/queries/findDeckCardById/findDeckCardById.ts</title>
<g id="a_node71"><a xlink:href="src/client/application/queries/findDeckCardById/findDeckCardById.ts" xlink:title="findDeckCardById.ts">
<polygon fill="#fb6969" stroke="black" points="1430.38,-1483.53 1331.12,-1483.53 1331.12,-1465.03 1430.38,-1465.03 1430.38,-1483.53"/>
<text text-anchor="start" x="1339.12" y="-1470.98" font-family="Helvetica,sans-Serif" font-size="9.00">findDeckCardById.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/findDeckCardById/findDeckCardById.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge78" class="edge">
<title>src/client/application/queries/findDeckCardById/findDeckCardById.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1422.7,-1483.94C1439.22,-1490.06 1456.67,-1499.84 1466.5,-1515.28 1475.57,-1529.53 1473.25,-2106.44 1474.5,-2123.28 1489.69,-2328.07 1531.95,-2574.01 1544.08,-2642.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1541.99,-2642.28 1545.11,-2647.81 1546.12,-2641.53 1541.99,-2642.28"/>
</g>
<!-- src/client/application/queries/getActiveFilters/getActiveFilters.ts -->
<g id="node72" class="node">
<title>src/client/application/queries/getActiveFilters/getActiveFilters.ts</title>
<g id="a_node72"><a xlink:href="src/client/application/queries/getActiveFilters/getActiveFilters.ts" xlink:title="getActiveFilters.ts">
<polygon fill="#fb6969" stroke="black" points="1424.38,-1666.53 1337.12,-1666.53 1337.12,-1648.03 1424.38,-1648.03 1424.38,-1666.53"/>
<text text-anchor="start" x="1345.12" y="-1653.98" font-family="Helvetica,sans-Serif" font-size="9.00">getActiveFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getActiveFilters/getActiveFilters.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge79" class="edge">
<title>src/client/application/queries/getActiveFilters/getActiveFilters.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1422.66,-1666.97C1439.17,-1673.09 1456.63,-1682.86 1466.5,-1698.28 1472.87,-1708.22 1473.59,-2111.51 1474.5,-2123.28 1490.27,-2328.03 1532.12,-2574 1544.12,-2642.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1542.02,-2642.27 1545.14,-2647.81 1546.16,-2641.53 1542.02,-2642.27"/>
</g>
<!-- src/client/application/queries/getAvailableFilters/getAvailableFilters.ts -->
<g id="node73" class="node">
<title>src/client/application/queries/getAvailableFilters/getAvailableFilters.ts</title>
<g id="a_node73"><a xlink:href="src/client/application/queries/getAvailableFilters/getAvailableFilters.ts" xlink:title="getAvailableFilters.ts">
<polygon fill="#fb6969" stroke="black" points="1431.12,-1605.53 1330.38,-1605.53 1330.38,-1587.03 1431.12,-1587.03 1431.12,-1605.53"/>
<text text-anchor="start" x="1338.38" y="-1592.98" font-family="Helvetica,sans-Serif" font-size="9.00">getAvailableFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getAvailableFilters/getAvailableFilters.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge80" class="edge">
<title>src/client/application/queries/getAvailableFilters/getAvailableFilters.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1422.68,-1605.96C1439.19,-1612.08 1456.65,-1621.85 1466.5,-1637.28 1473.77,-1648.66 1473.48,-2109.82 1474.5,-2123.28 1490.02,-2328.05 1532.05,-2574.01 1544.1,-2642.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1542.01,-2642.27 1545.13,-2647.81 1546.14,-2641.53 1542.01,-2642.27"/>
</g>
<!-- src/client/application/queries/getCardDetails/getCardDetails.ts -->
<g id="node74" class="node">
<title>src/client/application/queries/getCardDetails/getCardDetails.ts</title>
<g id="a_node74"><a xlink:href="src/client/application/queries/getCardDetails/getCardDetails.ts" xlink:title="getCardDetails.ts">
<polygon fill="#fb6969" stroke="black" points="1424,-1727.53 1337.5,-1727.53 1337.5,-1709.03 1424,-1709.03 1424,-1727.53"/>
<text text-anchor="start" x="1345.5" y="-1714.98" font-family="Helvetica,sans-Serif" font-size="9.00">getCardDetails.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getCardDetails/getCardDetails.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge81" class="edge">
<title>src/client/application/queries/getCardDetails/getCardDetails.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1422.64,-1727.99C1439.15,-1734.11 1456.6,-1743.88 1466.5,-1759.28 1477.43,-1776.3 1472.92,-2103.12 1474.5,-2123.28 1490.59,-2328 1532.22,-2573.99 1544.14,-2642.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1542.04,-2642.26 1545.15,-2647.81 1546.18,-2641.53 1542.04,-2642.26"/>
</g>
<!-- src/client/application/queries/getCardsInDeck/getCardsInDeck.ts -->
<g id="node75" class="node">
<title>src/client/application/queries/getCardsInDeck/getCardsInDeck.ts</title>
<g id="a_node75"><a xlink:href="src/client/application/queries/getCardsInDeck/getCardsInDeck.ts" xlink:title="getCardsInDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1426.25,-1788.53 1335.25,-1788.53 1335.25,-1770.03 1426.25,-1770.03 1426.25,-1788.53"/>
<text text-anchor="start" x="1343.25" y="-1775.98" font-family="Helvetica,sans-Serif" font-size="9.00">getCardsInDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getCardsInDeck/getCardsInDeck.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge82" class="edge">
<title>src/client/application/queries/getCardsInDeck/getCardsInDeck.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1422.6,-1789.01C1439.11,-1795.13 1456.57,-1804.9 1466.5,-1820.28 1475.63,-1834.43 1473.14,-2106.5 1474.5,-2123.28 1491.04,-2327.97 1532.35,-2573.98 1544.17,-2642.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1542.07,-2642.26 1545.17,-2647.81 1546.21,-2641.53 1542.07,-2642.26"/>
</g>
<!-- src/client/application/queries/getCatalogCards/getCatalogCards.ts -->
<g id="node76" class="node">
<title>src/client/application/queries/getCatalogCards/getCatalogCards.ts</title>
<g id="a_node76"><a xlink:href="src/client/application/queries/getCatalogCards/getCatalogCards.ts" xlink:title="getCatalogCards.ts">
<polygon fill="#fb6969" stroke="black" points="1428.12,-1849.53 1333.38,-1849.53 1333.38,-1831.03 1428.12,-1831.03 1428.12,-1849.53"/>
<text text-anchor="start" x="1341.38" y="-1836.98" font-family="Helvetica,sans-Serif" font-size="9.00">getCatalogCards.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getCatalogCards/getCatalogCards.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge84" class="edge">
<title>src/client/application/queries/getCatalogCards/getCatalogCards.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1422.11,-1849.87C1438.74,-1855.99 1456.43,-1865.8 1466.5,-1881.28 1473.83,-1892.56 1473.37,-2109.88 1474.5,-2123.28 1491.72,-2327.91 1532.55,-2573.97 1544.21,-2642.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1542.12,-2642.25 1545.2,-2647.8 1546.25,-2641.53 1542.12,-2642.25"/>
</g>
<!-- src/client/application/queries/getCatalogError/getCatalogError.ts -->
<g id="node77" class="node">
<title>src/client/application/queries/getCatalogError/getCatalogError.ts</title>
<g id="a_node77"><a xlink:href="src/client/application/queries/getCatalogError/getCatalogError.ts" xlink:title="getCatalogError.ts">
<polygon fill="#fb6969" stroke="black" points="1425.5,-2032.53 1336,-2032.53 1336,-2014.03 1425.5,-2014.03 1425.5,-2032.53"/>
<text text-anchor="start" x="1344" y="-2019.98" font-family="Helvetica,sans-Serif" font-size="9.00">getCatalogError.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getCatalogError/getCatalogError.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge85" class="edge">
<title>src/client/application/queries/getCatalogError/getCatalogError.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1420.08,-2032.88C1436.92,-2039.08 1455.37,-2048.93 1466.5,-2064.28 1474.27,-2074.99 1472.72,-2110.17 1474.5,-2123.28 1502.1,-2326.77 1535.61,-2573.63 1544.89,-2641.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1542.79,-2642.08 1545.67,-2647.75 1546.95,-2641.52 1542.79,-2642.08"/>
</g>
<!-- src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts -->
<g id="node78" class="node">
<title>src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts</title>
<g id="a_node78"><a xlink:href="src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts" xlink:title="getDeckBuilderView.ts">
<polygon fill="#fb6969" stroke="black" points="1434.5,-1910.53 1327,-1910.53 1327,-1892.03 1434.5,-1892.03 1434.5,-1910.53"/>
<text text-anchor="start" x="1335" y="-1897.98" font-family="Helvetica,sans-Serif" font-size="9.00">getDeckBuilderView.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge86" class="edge">
<title>src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1422.03,-1910.93C1438.64,-1917.06 1456.34,-1926.85 1466.5,-1942.28 1477.57,-1959.1 1472.7,-2103.23 1474.5,-2123.28 1492.86,-2327.81 1532.89,-2573.94 1544.29,-2642.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1542.19,-2642.23 1545.25,-2647.8 1546.33,-2641.53 1542.19,-2642.23"/>
</g>
<!-- src/client/application/queries/getDeckName/getDeckName.ts -->
<g id="node79" class="node">
<title>src/client/application/queries/getDeckName/getDeckName.ts</title>
<g id="a_node79"><a xlink:href="src/client/application/queries/getDeckName/getDeckName.ts" xlink:title="getDeckName.ts">
<polygon fill="#fb6969" stroke="black" points="1422.5,-1971.53 1339,-1971.53 1339,-1953.03 1422.5,-1953.03 1422.5,-1971.53"/>
<text text-anchor="start" x="1347" y="-1958.98" font-family="Helvetica,sans-Serif" font-size="9.00">getDeckName.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getDeckName/getDeckName.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge87" class="edge">
<title>src/client/application/queries/getDeckName/getDeckName.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1421.43,-1971.87C1438.14,-1978.01 1456.08,-1987.83 1466.5,-2003.28 1473.97,-2014.36 1473.16,-2109.99 1474.5,-2123.28 1495.15,-2327.59 1533.56,-2573.87 1544.44,-2642.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1542.34,-2642.19 1545.36,-2647.79 1546.48,-2641.53 1542.34,-2642.19"/>
</g>
<!-- src/client/application/queries/getFilteredCards/getFilteredCards.ts -->
<g id="node80" class="node">
<title>src/client/application/queries/getFilteredCards/getFilteredCards.ts</title>
<g id="a_node80"><a xlink:href="src/client/application/queries/getFilteredCards/getFilteredCards.ts" xlink:title="getFilteredCards.ts">
<polygon fill="#fb6969" stroke="black" points="1427.38,-2093.53 1334.12,-2093.53 1334.12,-2075.03 1427.38,-2075.03 1427.38,-2093.53"/>
<text text-anchor="start" x="1342.12" y="-2080.98" font-family="Helvetica,sans-Serif" font-size="9.00">getFilteredCards.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getFilteredCards/getFilteredCards.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge89" class="edge">
<title>src/client/application/queries/getFilteredCards/getFilteredCards.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1419.97,-2093.96C1436.8,-2100.17 1455.25,-2110.02 1466.5,-2125.28 1529.02,-2210.12 1544.08,-2557.24 1546.84,-2641.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1544.74,-2641.7 1547.02,-2647.63 1548.94,-2641.57 1544.74,-2641.7"/>
</g>
<!-- src/client/application/queries/getFilteredCards/getFilteredCards.ts&#45;&gt;src/client/application/queries/applyFilterToCard/applyFilterToCard.ts -->
<g id="edge88" class="edge">
<title>src/client/application/queries/getFilteredCards/getFilteredCards.ts&#45;&gt;src/client/application/queries/applyFilterToCard/applyFilterToCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1427.45,-2084.28C1446.88,-2084.28 1469.75,-2084.28 1490.28,-2084.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1490.2,-2086.38 1496.2,-2084.28 1490.2,-2082.18 1490.2,-2086.38"/>
</g>
<!-- src/client/application/queries/getGameSettings/getGameSettings.ts -->
<g id="node81" class="node">
<title>src/client/application/queries/getGameSettings/getGameSettings.ts</title>
<g id="a_node81"><a xlink:href="src/client/application/queries/getGameSettings/getGameSettings.ts" xlink:title="getGameSettings.ts">
<polygon fill="#fb6969" stroke="black" points="1428.5,-2215.53 1333,-2215.53 1333,-2197.03 1428.5,-2197.03 1428.5,-2215.53"/>
<text text-anchor="start" x="1341" y="-2202.98" font-family="Helvetica,sans-Serif" font-size="9.00">getGameSettings.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getGameSettings/getGameSettings.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge90" class="edge">
<title>src/client/application/queries/getGameSettings/getGameSettings.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1421.96,-2215.98C1438.55,-2222.12 1456.26,-2231.91 1466.5,-2247.28 1475.5,-2260.8 1471.9,-2377.24 1474.5,-2393.28 1490,-2488.97 1526.33,-2599.66 1541.03,-2642.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1539.03,-2642.91 1542.98,-2647.89 1542.99,-2641.53 1539.03,-2642.91"/>
</g>
<!-- src/client/application/queries/getGameSettingsError/getGameSettingsError.ts -->
<g id="node82" class="node">
<title>src/client/application/queries/getGameSettingsError/getGameSettingsError.ts</title>
<g id="a_node82"><a xlink:href="src/client/application/queries/getGameSettingsError/getGameSettingsError.ts" xlink:title="getGameSettingsError.ts">
<polygon fill="#fb6969" stroke="black" points="1438.25,-2276.53 1323.25,-2276.53 1323.25,-2258.03 1438.25,-2258.03 1438.25,-2276.53"/>
<text text-anchor="start" x="1331.25" y="-2263.98" font-family="Helvetica,sans-Serif" font-size="9.00">getGameSettingsError.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getGameSettingsError/getGameSettingsError.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge91" class="edge">
<title>src/client/application/queries/getGameSettingsError/getGameSettingsError.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1421.24,-2277C1437.9,-2283.17 1455.87,-2292.98 1466.5,-2308.28 1477.32,-2323.86 1471.1,-2374.62 1474.5,-2393.28 1491.87,-2488.65 1527.12,-2599.52 1541.28,-2642.24"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1539.27,-2642.82 1543.16,-2647.85 1543.25,-2641.49 1539.27,-2642.82"/>
</g>
<!-- src/client/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts -->
<g id="node83" class="node">
<title>src/client/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts</title>
<g id="a_node83"><a xlink:href="src/client/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts" xlink:title="getMaxCardsInDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1434.88,-2337.53 1326.62,-2337.53 1326.62,-2319.03 1434.88,-2319.03 1434.88,-2337.53"/>
<text text-anchor="start" x="1334.62" y="-2324.98" font-family="Helvetica,sans-Serif" font-size="9.00">getMaxCardsInDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge92" class="edge">
<title>src/client/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1417.45,-2337.95C1434.52,-2344.27 1453.93,-2354.22 1466.5,-2369.28 1471.05,-2374.74 1525.93,-2579.78 1542.6,-2642.34"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1540.5,-2642.62 1544.08,-2647.88 1544.56,-2641.54 1540.5,-2642.62"/>
</g>
<!-- src/client/application/queries/getSearchTerm/getSearchTerm.ts -->
<g id="node84" class="node">
<title>src/client/application/queries/getSearchTerm/getSearchTerm.ts</title>
<g id="a_node84"><a xlink:href="src/client/application/queries/getSearchTerm/getSearchTerm.ts" xlink:title="getSearchTerm.ts">
<polygon fill="#fb6969" stroke="black" points="1424.38,-2459.53 1337.12,-2459.53 1337.12,-2441.03 1424.38,-2441.03 1424.38,-2459.53"/>
<text text-anchor="start" x="1345.12" y="-2446.98" font-family="Helvetica,sans-Serif" font-size="9.00">getSearchTerm.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getSearchTerm/getSearchTerm.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge93" class="edge">
<title>src/client/application/queries/getSearchTerm/getSearchTerm.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1413.88,-2459.97C1431.21,-2466.53 1451.97,-2476.69 1466.5,-2491.28 1510.56,-2535.52 1534.01,-2608.43 1542.99,-2642.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1540.87,-2642.34 1544.4,-2647.63 1544.94,-2641.29 1540.87,-2642.34"/>
</g>
<!-- src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts -->
<g id="node85" class="node">
<title>src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts</title>
<g id="a_node85"><a xlink:href="src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts" xlink:title="getTotalCardsInDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1436,-2520.53 1325.5,-2520.53 1325.5,-2502.03 1436,-2502.03 1436,-2520.53"/>
<text text-anchor="start" x="1333.5" y="-2507.98" font-family="Helvetica,sans-Serif" font-size="9.00">getTotalCardsInDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge94" class="edge">
<title>src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1410.03,-2521.01C1427.5,-2527.93 1449.72,-2538.45 1466.5,-2552.28 1498.38,-2578.55 1524.71,-2619.88 1538.02,-2643.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1536.06,-2643.86 1540.83,-2648.06 1539.73,-2641.8 1536.06,-2643.86"/>
</g>
<!-- src/client/application/queries/hasDeckDraft/hasDeckDraft.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="edge95" class="edge">
<title>src/client/application/queries/hasDeckDraft/hasDeckDraft.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1416.27,-2154.92C1433.46,-2161.3 1453.32,-2171.32 1466.5,-2186.28 1607.16,-2346.01 1572.33,-2432.59 1618.88,-2640.28 1622.2,-2655.13 1617.89,-2696 1626.88,-2708.28 1632.29,-2715.68 1639.96,-2721.3 1648.18,-2725.54"/>
<polygon fill="none" stroke="#000000" stroke-opacity="0.200000" points="1646.85,-2727.24 1653.17,-2727.89 1648.63,-2723.44 1646.85,-2727.24"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckDraft.ts -->
<g id="node90" class="node">
<title>src/client/domain/DeckBuilder/DeckDraft.ts</title>
<g id="a_node90"><a xlink:href="src/client/domain/DeckBuilder/DeckDraft.ts" xlink:title="DeckDraft.ts">
<polygon fill="#fa9f36" stroke="black" points="1581.12,-251.53 1515.62,-251.53 1515.62,-233.03 1581.12,-233.03 1581.12,-251.53"/>
<text text-anchor="start" x="1523.62" y="-238.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/application/services/DeckDraftService/DeckDraftService.ts&#45;&gt;src/client/domain/DeckBuilder/DeckDraft.ts -->
<g id="edge98" class="edge">
<title>src/client/application/services/DeckDraftService/DeckDraftService.ts&#45;&gt;src/client/domain/DeckBuilder/DeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1692.97,-2728.66C1688.06,-2562.02 1620.97,-286.82 1618.88,-283.28 1611.68,-271.11 1599.36,-262.17 1587.07,-255.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1588.11,-253.96 1581.79,-253.28 1586.3,-257.75 1588.11,-253.96"/>
</g>
<!-- src/client/application/queries/isCatalogLoading/isCatalogLoading.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge96" class="edge">
<title>src/client/application/queries/isCatalogLoading/isCatalogLoading.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1416.18,-2399C1433.34,-2405.4 1453.21,-2415.41 1466.5,-2430.28 1523.15,-2493.69 1540.69,-2599.22 1545.62,-2641.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1543.53,-2641.91 1546.26,-2647.65 1547.71,-2641.46 1543.53,-2641.91"/>
</g>
<!-- src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts -->
<g id="node89" class="node">
<title>src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts</title>
<g id="a_node89"><a xlink:href="src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts" xlink:title="isGameSettingsLoading.ts">
<polygon fill="#fb6969" stroke="black" points="1442.38,-2581.53 1319.12,-2581.53 1319.12,-2563.03 1442.38,-2563.03 1442.38,-2581.53"/>
<text text-anchor="start" x="1327.12" y="-2568.98" font-family="Helvetica,sans-Serif" font-size="9.00">isGameSettingsLoading.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge97" class="edge">
<title>src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1399.79,-2581.87C1429.21,-2597.5 1487.62,-2628.53 1521.47,-2646.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1520.15,-2648.19 1526.43,-2649.15 1522.12,-2644.48 1520.15,-2648.19"/>
</g>
<!-- src/client/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts -->
<g id="edge115" class="edge">
<title>src/client/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1888.81,-402.99C1889.97,-402.92 1891.13,-402.85 1892.29,-402.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1892.27,-404.89 1898.14,-402.44 1892.03,-400.69 1892.27,-404.89"/>
</g>
<!-- src/client/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge116" class="edge">
<title>src/client/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1888.95,-415.68C1932.16,-423.39 1987.82,-434.58 2008.88,-444.28 2020.56,-449.67 2032.08,-458.28 2040.96,-465.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2039.31,-467.18 2045.2,-469.59 2042.1,-464.04 2039.31,-467.18"/>
</g>
<!-- src/client/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/domain/Catalog/catalogEvents.ts -->
<g id="edge117" class="edge">
<title>src/client/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/domain/Catalog/catalogEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1739.16,-446.06C1752.58,-444.77 1767.41,-443.34 1781.16,-442.02"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1781.23,-444.13 1787,-441.46 1780.83,-439.95 1781.23,-444.13"/>
</g>
<!-- src/client/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts -->
<g id="edge118" class="edge">
<title>src/client/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1739.41,-451.83C1785.63,-453.45 1857.93,-455.98 1905.17,-457.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1904.88,-459.73 1910.95,-457.84 1905.03,-455.53 1904.88,-459.73"/>
</g>
<!-- src/client/domain/Catalog/catalogSearchReducer.ts&#45;&gt;src/client/domain/Catalog/catalogSearchEvents.ts -->
<g id="edge119" class="edge">
<title>src/client/domain/Catalog/catalogSearchReducer.ts&#45;&gt;src/client/domain/Catalog/catalogSearchEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1753.79,-481.28C1758.02,-481.28 1762.31,-481.28 1766.58,-481.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1766.26,-483.38 1772.26,-481.28 1766.26,-479.18 1766.26,-483.38"/>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge126" class="edge">
<title>src/client/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1748.7,-224.59C1756.24,-223.36 1764.02,-222.09 1771.65,-220.85"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1771.94,-222.93 1777.53,-219.9 1771.27,-218.79 1771.94,-222.93"/>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="edge125" class="edge">
<title>src/client/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1748.79,-234.53C1791.98,-235.54 1852.55,-236.95 1896.33,-237.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1896.06,-240.07 1902.1,-238.11 1896.15,-235.87 1896.06,-240.07"/>
</g>
<!-- src/client/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsEvents.ts -->
<g id="edge129" class="edge">
<title>src/client/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1606.65,-311.71C1614.51,-310.94 1622.61,-310.15 1630.56,-309.38"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1630.66,-311.48 1636.43,-308.81 1630.26,-307.3 1630.66,-311.48"/>
</g>
<!-- src/client/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/domain/GameSettings/GameSettings.ts -->
<g id="edge128" class="edge">
<title>src/client/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/domain/GameSettings/GameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1606.55,-319.62C1650.34,-320.91 1712.23,-321.65 1766.38,-318.28 1771.13,-317.99 1776.08,-317.57 1781.02,-317.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1781.11,-319.18 1786.85,-316.45 1780.66,-315 1781.11,-319.18"/>
</g>
<!-- src/client/application/subscribers/subscribeToDeckDraft.ts -->
<g id="node97" class="node">
<title>src/client/application/subscribers/subscribeToDeckDraft.ts</title>
<g id="a_node97"><a xlink:href="src/client/application/subscribers/subscribeToDeckDraft.ts" xlink:title="subscribeToDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1054.75,-2010.53 939.75,-2010.53 939.75,-1992.03 1054.75,-1992.03 1054.75,-2010.53"/>
<text text-anchor="start" x="947.75" y="-1997.98" font-family="Helvetica,sans-Serif" font-size="9.00">subscribeToDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge107" class="edge">
<title>src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M998.18,-2010.69C998.22,-2087.27 1009.39,-2610.78 1303,-2814.28 1362.72,-2855.68 1404.31,-2851.87 1466.5,-2814.28 1516.35,-2784.16 1536.99,-2712.01 1544.14,-2678.26"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1546.12,-2679.09 1545.22,-2672.8 1542,-2678.27 1546.12,-2679.09"/>
</g>
<!-- src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/application/commands/saveDeckDraft/saveDeckDraft.ts -->
<g id="edge106" class="edge">
<title>src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/application/commands/saveDeckDraft/saveDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1001.31,-1991.99C1024.05,-1922.92 1166.77,-1489.47 1197.53,-1396.02"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1199.47,-1396.86 1199.35,-1390.5 1195.48,-1395.54 1199.47,-1396.86"/>
</g>
<!-- src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="edge108" class="edge">
<title>src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M998.56,-1991.58C1002.06,-1883.72 1035.5,-907.05 1105.75,-626.28 1158.85,-414.05 1125.68,-303.42 1303,-175.28 1416.93,-92.95 1488.94,-105.65 1618.88,-159.28 1646.87,-170.83 1669.87,-198.59 1682.56,-216.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1680.54,-217.4 1685.65,-221.19 1684.02,-215.04 1680.54,-217.4"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilderCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge123" class="edge">
<title>src/client/domain/DeckBuilder/DeckBuilderCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1960.17,-248.99C1972.08,-268.71 1998.32,-315.21 2008.88,-358.28 2013.16,-375.79 2007.28,-505.02 2016.88,-520.28 2018.59,-523.02 2020.78,-525.44 2023.24,-527.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2021.7,-529.05 2027.8,-530.82 2024.14,-525.63 2021.7,-529.05"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx -->
<g id="node101" class="node">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx</title>
<g id="a_node101"><a xlink:href="src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx" xlink:title="SignInButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="561.25,-4137.53 479.25,-4137.53 479.25,-4119.03 561.25,-4119.03 561.25,-4137.53"/>
<text text-anchor="start" x="487.25" y="-4124.98" font-family="Helvetica,sans-Serif" font-size="9.00">SignInButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx -->
<g id="node102" class="node">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx</title>
<g id="a_node102"><a xlink:href="src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx" xlink:title="useSignInForm.tsx">
<polygon fill="#6cbaff" stroke="black" points="798,-4137.53 706.25,-4137.53 706.25,-4119.03 798,-4119.03 798,-4137.53"/>
<text text-anchor="start" x="714.25" y="-4124.98" font-family="Helvetica,sans-Serif" font-size="9.00">useSignInForm.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx -->
<g id="edge130" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M561.55,-4128.28C599.22,-4128.28 655.73,-4128.28 697.26,-4128.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="697.11,-4130.38 703.11,-4128.28 697.11,-4126.18 697.11,-4130.38"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx -->
<g id="node103" class="node">
<title>src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx</title>
<g id="a_node103"><a xlink:href="src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx" xlink:title="ShiningButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="1040.88,-4107.53 953.62,-4107.53 953.62,-4089.03 1040.88,-4089.03 1040.88,-4107.53"/>
<text text-anchor="start" x="961.62" y="-4094.98" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx -->
<g id="edge131" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M541.3,-4118.73C566.27,-4107.41 610.54,-4089.21 650.75,-4082.28 753.82,-4064.52 876.58,-4078.33 944.52,-4088.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="944.08,-4091.04 950.34,-4089.91 944.75,-4086.89 944.08,-4091.04"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge134" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M798.46,-4120.27C827.68,-4117.95 864.09,-4120.89 885.5,-4144.28 892.11,-4151.5 890.4,-4310.52 891.12,-4320.28 917.63,-4679.1 979.9,-5114.7 993.57,-5208.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="991.47,-5208.31 994.42,-5213.94 995.63,-5207.7 991.47,-5208.31"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge135" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M760.55,-4118.93C784.47,-4088.11 859.5,-3986.07 885.5,-3887.28 888.73,-3875 883.9,-3440.72 891.12,-3430.28 905.35,-3409.74 931.5,-3399.49 954.16,-3394.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="954.53,-3396.44 960,-3393.19 953.7,-3392.32 954.53,-3396.44"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css -->
<g id="node133" class="node">
<title>src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css</title>
<g id="a_node133"><a xlink:href="src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css" xlink:title="ShiningButton.css">
<polygon fill="#6cbaff" stroke="black" points="1249.12,-4107.53 1159.62,-4107.53 1159.62,-4089.03 1249.12,-4089.03 1249.12,-4107.53"/>
<text text-anchor="start" x="1167.62" y="-4094.98" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningButton.css</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css -->
<g id="edge189" class="edge">
<title>src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1041.25,-4098.28C1073.06,-4098.28 1116.58,-4098.28 1150.52,-4098.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1150.34,-4100.38 1156.34,-4098.28 1150.34,-4096.18 1150.34,-4100.38"/>
</g>
<!-- src/client/infrastructure/lib/utils.ts -->
<g id="node134" class="node">
<title>src/client/infrastructure/lib/utils.ts</title>
<g id="a_node134"><a xlink:href="src/client/infrastructure/lib/utils.ts" xlink:title="utils.ts">
<polygon fill="#6cbaff" stroke="black" points="1231.38,-5235.53 1177.38,-5235.53 1177.38,-5217.03 1231.38,-5217.03 1231.38,-5235.53"/>
<text text-anchor="start" x="1191.62" y="-5222.98" font-family="Helvetica,sans-Serif" font-size="9.00">utils.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts -->
<g id="edge190" class="edge">
<title>src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1041.32,-4106.02C1060.92,-4111.83 1082.56,-4121.9 1095.38,-4139.28 1103.68,-4150.54 1188.52,-5065.48 1201.67,-5207.77"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1199.57,-5207.86 1202.21,-5213.64 1203.75,-5207.48 1199.57,-5207.86"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx -->
<g id="edge132" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M713.54,-4154.21C674.5,-4148.95 613.26,-4140.69 570.4,-4134.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="570.78,-4132.84 564.55,-4134.12 570.22,-4137 570.78,-4132.84"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge133" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M790.85,-4159.28C832.99,-4159.28 901.33,-4159.28 947.5,-4159.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="947.39,-4161.38 953.39,-4159.28 947.39,-4157.18 947.39,-4161.38"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts -->
<g id="edge192" class="edge">
<title>src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1038.31,-4166.2C1058.65,-4171.86 1081.86,-4182.05 1095.38,-4200.28 1157.96,-4284.67 1196.55,-5076.27 1202.55,-5207.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1200.45,-5207.79 1202.82,-5213.69 1204.64,-5207.6 1200.45,-5207.79"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css -->
<g id="node135" class="node">
<title>src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css</title>
<g id="a_node135"><a xlink:href="src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css" xlink:title="ShiningCard.css">
<polygon fill="#6cbaff" stroke="black" points="1246.12,-4168.53 1162.62,-4168.53 1162.62,-4150.03 1246.12,-4150.03 1246.12,-4168.53"/>
<text text-anchor="start" x="1170.62" y="-4155.98" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningCard.css</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css -->
<g id="edge191" class="edge">
<title>src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1038.29,-4159.28C1071.28,-4159.28 1118.24,-4159.28 1153.65,-4159.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1153.36,-4161.38 1159.36,-4159.28 1153.36,-4157.18 1153.36,-4161.38"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge136" class="edge">
<title>src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M558.58,-4814.96C582.6,-4821.95 614.06,-4832.58 640,-4846.28 645.27,-4849.06 645.33,-4851.82 650.75,-4854.28 747.94,-4898.48 805.67,-4834.38 885.5,-4905.28 978.64,-4988.01 993.48,-5152.88 995.82,-5207.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="993.71,-5207.65 996.01,-5213.57 997.91,-5207.5 993.71,-5207.65"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge137" class="edge">
<title>src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M567.95,-4795.6C593.92,-4787.57 624.13,-4773.28 640,-4748.28 659.65,-4717.33 626.12,-3455.45 650.75,-3428.28 685.99,-3389.41 833.39,-3409.35 885.5,-3403.28 908.31,-3400.63 933.72,-3397.43 954.45,-3394.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="954.63,-3396.86 960.32,-3394 954.1,-3392.69 954.63,-3396.86"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge138" class="edge">
<title>src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M557.87,-4875.99C628.48,-4896.24 785.33,-4948.87 885.5,-5040.28 940.01,-5090.02 976.06,-5172.89 989.89,-5208.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="987.79,-5209.16 991.86,-5214.03 991.72,-5207.68 987.79,-5209.16"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge139" class="edge">
<title>src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M569.73,-4870.62C594.61,-4869.98 623.16,-4864.23 640,-4844.28 653.19,-4828.66 646.02,-4128.17 650.75,-4108.28 705.98,-3876.08 828.62,-3857.08 885.5,-3625.28 888.08,-3614.76 884.88,-3439.14 891.12,-3430.28 905.52,-3409.85 931.67,-3399.6 954.29,-3394.46"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="954.65,-3396.53 960.11,-3393.28 953.81,-3392.42 954.65,-3396.53"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge166" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M804.55,-4314.09C833.28,-4318.06 866.94,-4328.26 885.5,-4353.28 900.59,-4373.63 877.23,-5244.1 891.12,-5265.28 974.33,-5392.16 1051.65,-5403.28 1203.38,-5403.28 1203.38,-5403.28 1203.38,-5403.28 1832.5,-5403.28 1911.61,-5403.28 1956.54,-5438.61 2008.88,-5379.28 2051.72,-5330.7 2057.03,-879.56 2057.36,-560.32"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2059.46,-560.76 2057.36,-554.76 2055.26,-560.76 2059.46,-560.76"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge167" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M762.71,-4302.69C784.1,-4281.71 838.05,-4231.15 891.12,-4200.28 911.42,-4188.47 935.76,-4178.73 955.91,-4171.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="956.39,-4173.77 961.39,-4169.85 955.04,-4169.79 956.39,-4173.77"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge183" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M819.72,-4378.84C844.05,-4377.09 869.49,-4370.08 885.5,-4351.28 897.09,-4337.67 881.01,-3079.02 891.12,-3064.28 903.1,-3046.84 923.69,-3036.81 943.48,-3031.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="943.99,-3033.09 949.26,-3029.54 942.93,-3029.03 943.99,-3033.09"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="edge184" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M819.7,-4378.82C844.02,-4377.07 869.47,-4370.07 885.5,-4351.28 901.51,-4332.51 874.73,-3481.71 891.12,-3463.28 906,-3446.56 930.5,-3442.8 952.11,-3443.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="951.71,-3445.54 957.82,-3443.78 951.95,-3441.34 951.71,-3445.54"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx -->
<g id="node126" class="node">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx</title>
<g id="a_node126"><a xlink:href="src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx" xlink:title="useGameSettingsByGameId.tsx">
<polygon fill="#6cbaff" stroke="black" points="1069.75,-3546.53 924.75,-3546.53 924.75,-3528.03 1069.75,-3528.03 1069.75,-3546.53"/>
<text text-anchor="start" x="932.75" y="-3533.98" font-family="Helvetica,sans-Serif" font-size="9.00">useGameSettingsByGameId.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx -->
<g id="edge185" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M819.69,-4378.81C844.01,-4377.06 869.46,-4370.06 885.5,-4351.28 898.89,-4335.61 881.44,-3627.48 891.12,-3609.28 906.06,-3581.21 937.37,-3561.96 961.95,-3550.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="962.62,-3552.48 967.24,-3548.12 960.9,-3548.65 962.62,-3552.48"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge198" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1053.63,-3083.57C1069.26,-3080.54 1084.86,-3074.33 1095.38,-3062.28 1112.83,-3042.28 1087.75,-2962.79 1105.75,-2943.28 1216.01,-2823.76 1341.55,-2986.35 1466.5,-2882.28 1530.25,-2829.19 1543.75,-2721.43 1546.61,-2678.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1548.69,-2678.85 1546.93,-2672.74 1544.5,-2678.62 1548.69,-2678.85"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/commands/loadCatalogCards/loadCatalogCards.ts -->
<g id="edge194" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/commands/loadCatalogCards/loadCatalogCards.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1053.74,-3084.18C1069.57,-3081.22 1085.25,-3074.89 1095.38,-3062.28 1112.85,-3040.52 1090.24,-1076.48 1105.75,-1053.28 1115.1,-1039.29 1130.25,-1030.06 1145.85,-1023.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1146.22,-1026.08 1151.17,-1022.09 1144.81,-1022.12 1146.22,-1026.08"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/getCatalogError/getCatalogError.ts -->
<g id="edge195" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/getCatalogError/getCatalogError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1053.51,-3083.99C1069.33,-3081.02 1085.06,-3074.73 1095.38,-3062.28 1116.08,-3037.28 1085.05,-2795.29 1105.75,-2770.28 1160.75,-2703.84 1240.78,-2794.37 1295,-2727.28 1306.58,-2712.96 1293.43,-2080.02 1303,-2064.28 1310.67,-2051.67 1323.66,-2042.71 1336.8,-2036.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.52,-2038.43 1342.18,-2034.11 1335.84,-2034.58 1337.52,-2038.43"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/getFilteredCards/getFilteredCards.ts -->
<g id="edge196" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/getFilteredCards/getFilteredCards.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1053.67,-3083.86C1069.4,-3080.87 1085.04,-3074.6 1095.38,-3062.28 1110.89,-3043.79 1091.76,-2864.95 1105.75,-2845.28 1158.29,-2771.42 1243.77,-2844.05 1295,-2769.28 1315.22,-2739.76 1284.41,-2155.85 1303,-2125.28 1310.67,-2112.67 1323.67,-2103.71 1336.81,-2097.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.52,-2099.43 1342.19,-2095.11 1335.85,-2095.58 1337.52,-2099.43"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/isCatalogLoading/isCatalogLoading.ts -->
<g id="edge197" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/isCatalogLoading/isCatalogLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1053.54,-3083.76C1069.27,-3080.76 1084.94,-3074.51 1095.38,-3062.28 1118.64,-3035 1085.64,-2930.96 1105.75,-2901.28 1158,-2824.18 1244.43,-2889.5 1295,-2811.28 1317.99,-2775.72 1280.85,-2466.37 1303,-2430.28 1310.72,-2417.7 1323.73,-2408.75 1336.87,-2402.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.58,-2404.47 1342.24,-2400.15 1335.9,-2400.62 1337.58,-2404.47"/>
</g>
<!-- src/client/infrastructure/hooks/useDebounce/useDebounce.ts -->
<g id="node138" class="node">
<title>src/client/infrastructure/hooks/useDebounce/useDebounce.ts</title>
<g id="a_node138"><a xlink:href="src/client/infrastructure/hooks/useDebounce/useDebounce.ts" xlink:title="useDebounce.ts">
<polygon fill="#6cbaff" stroke="black" points="1245.75,-3154.53 1163,-3154.53 1163,-3136.03 1245.75,-3136.03 1245.75,-3154.53"/>
<text text-anchor="start" x="1171" y="-3141.98" font-family="Helvetica,sans-Serif" font-size="9.00">useDebounce.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/hooks/useDebounce/index.ts&#45;&gt;src/client/infrastructure/hooks/useDebounce/useDebounce.ts -->
<g id="edge199" class="edge">
<title>src/client/infrastructure/hooks/useDebounce/index.ts&#45;&gt;src/client/infrastructure/hooks/useDebounce/useDebounce.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1024.65,-3145.28C1057.62,-3145.28 1114.65,-3145.28 1155.42,-3145.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1161.58,-3147.38 1155.58,-3145.28 1161.58,-3143.18 1161.58,-3147.38"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/addCardToDeck/addCardToDeck.ts -->
<g id="edge200" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/addCardToDeck/addCardToDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1003.98,-3013.83C1021.89,-2983.65 1076.78,-2886.32 1095.38,-2797.28 1100.5,-2772.74 1091.81,-1013.12 1105.75,-992.28 1115.89,-977.11 1132.85,-967.54 1149.81,-961.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1150.16,-963.6 1155.22,-959.76 1148.87,-959.61 1150.16,-963.6"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge215" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1003.21,-3013.81C1016.27,-2989.98 1054.19,-2927.55 1105.75,-2901.28 1177.5,-2864.73 1402.21,-2915.76 1466.5,-2867.28 1528.25,-2820.71 1542.89,-2720.14 1546.33,-2678.7"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1548.42,-2678.87 1546.75,-2672.74 1544.23,-2678.57 1548.42,-2678.87"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalog.ts -->
<g id="edge201" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalog.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1004.27,-3013.61C1022.09,-2984.43 1074.59,-2894.13 1095.38,-2811.28 1113.47,-2739.15 1097.77,-2718.22 1105.75,-2644.28 1164.97,-2095.69 1228.48,-1965.04 1295,-1417.28 1297.42,-1397.35 1294.15,-1345.3 1303,-1327.28 1314.76,-1303.33 1338.32,-1283.47 1356.25,-1270.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1357.24,-1272.78 1361.04,-1267.67 1354.89,-1269.3 1357.24,-1272.78"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/hideCardDetails/hideCardDetails.ts -->
<g id="edge202" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/hideCardDetails/hideCardDetails.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M999.54,-3013.62C1009.48,-2938.84 1073.9,-2444.43 1095.38,-2040.28 1096.37,-2021.52 1095.29,-702.89 1105.75,-687.28 1115.91,-672.12 1132.87,-662.56 1149.82,-656.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1150.18,-658.62 1155.24,-654.77 1148.88,-654.62 1150.18,-658.62"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/search/search.ts -->
<g id="edge204" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/search/search.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1004.25,-3013.61C1022,-2984.41 1074.35,-2894.08 1095.38,-2811.28 1099.57,-2794.77 1093.77,-2670.4 1105.75,-2658.28 1135.41,-2628.28 1266.27,-2674.17 1295,-2643.28 1306.25,-2631.19 1297.89,-1469.99 1303,-1454.28 1314.96,-1417.53 1344.59,-1382.65 1363.35,-1363.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1364.63,-1364.89 1367.35,-1359.14 1361.64,-1361.94 1364.63,-1364.89"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts -->
<g id="edge203" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1003.97,-3013.83C1021.88,-2983.65 1076.76,-2886.32 1095.38,-2797.28 1099.99,-2775.23 1093.22,-1194 1105.75,-1175.28 1114.55,-1162.13 1128.47,-1153.19 1143.06,-1147.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1143.52,-1149.19 1148.39,-1145.1 1142.03,-1145.26 1143.52,-1149.19"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/showCardDetails/showCardDetails.ts -->
<g id="edge205" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/showCardDetails/showCardDetails.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M999.54,-3013.62C1009.47,-2938.83 1073.83,-2444.43 1095.38,-2040.28 1096.33,-2022.36 1095.76,-763.19 1105.75,-748.28 1115.51,-733.72 1131.55,-724.32 1147.84,-718.25"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1148.46,-720.25 1153.47,-716.34 1147.12,-716.28 1148.46,-720.25"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts -->
<g id="edge206" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1003.98,-3013.83C1021.9,-2983.65 1076.8,-2886.33 1095.38,-2797.28 1100.84,-2771.08 1090.88,-892.53 1105.75,-870.28 1114.54,-857.13 1128.46,-848.19 1143.06,-842.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1143.51,-844.18 1148.38,-840.09 1142.02,-840.25 1143.51,-844.18"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts -->
<g id="edge207" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1003.98,-3013.83C1021.9,-2983.65 1076.79,-2886.33 1095.38,-2797.28 1100.67,-2771.91 1091.34,-952.83 1105.75,-931.28 1114.54,-918.13 1128.46,-909.19 1143.06,-903.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1143.51,-905.18 1148.38,-901.09 1142.02,-901.26 1143.51,-905.18"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge216" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.3,-3030.58C1062.01,-3036.28 1083.45,-3046.39 1095.38,-3064.28 1112.81,-3090.43 1083.47,-5305.11 1105.75,-5327.28 1165.36,-5386.62 1228.49,-5378.76 1295,-5327.28 1916.02,-4846.62 1966.31,-2650.43 2008.88,-1866.28 2009.89,-1847.64 2009.73,-539.53 2016.88,-522.28 2020.98,-512.37 2028.75,-503.57 2036.39,-496.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2037.69,-498.34 2040.94,-492.88 2035,-495.12 2037.69,-498.34"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getActiveFilters/getActiveFilters.ts -->
<g id="edge208" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getActiveFilters/getActiveFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1004.22,-3013.6C1021.9,-2984.38 1074.07,-2894 1095.38,-2811.28 1099.24,-2796.28 1094.82,-2683.26 1105.75,-2672.28 1135.51,-2642.38 1266.24,-2688.15 1295,-2657.28 1313.16,-2637.79 1289.21,-1721.07 1303,-1698.28 1310.64,-1685.65 1323.63,-1676.69 1336.78,-1670.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.5,-1672.41 1342.16,-1668.09 1335.82,-1668.56 1337.5,-1672.41"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getCardDetails/getCardDetails.ts -->
<g id="edge209" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getCardDetails/getCardDetails.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1004.19,-3013.59C1021.78,-2984.35 1073.73,-2893.91 1095.38,-2811.28 1102.44,-2784.32 1086,-2705.95 1105.75,-2686.28 1135.64,-2656.51 1266.24,-2702.14 1295,-2671.28 1312.27,-2652.75 1289.88,-1780.95 1303,-1759.28 1310.65,-1746.65 1323.64,-1737.69 1336.78,-1731.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.5,-1733.42 1342.16,-1729.09 1335.82,-1729.57 1337.5,-1733.42"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getCardsInDeck/getCardsInDeck.ts -->
<g id="edge210" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getCardsInDeck/getCardsInDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1004.15,-3013.58C1021.63,-2984.31 1073.3,-2893.8 1095.38,-2811.28 1101.78,-2787.35 1088.11,-2717.67 1105.75,-2700.28 1135.79,-2670.66 1266.23,-2716.14 1295,-2685.28 1311.39,-2667.71 1290.55,-1840.83 1303,-1820.28 1310.65,-1807.66 1323.64,-1798.69 1336.78,-1792.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.5,-1794.42 1342.16,-1790.1 1335.82,-1790.57 1337.5,-1794.42"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts -->
<g id="edge211" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1004.1,-3013.57C1021.44,-2984.26 1072.75,-2893.65 1095.38,-2811.28 1101.12,-2790.38 1090.21,-2729.4 1105.75,-2714.28 1135.99,-2684.86 1266.21,-2730.12 1295,-2699.28 1309.35,-2683.91 1292.09,-1960.26 1303,-1942.28 1310.66,-1929.66 1323.65,-1920.7 1336.79,-1914.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.51,-1916.42 1342.17,-1912.1 1335.83,-1912.57 1337.51,-1916.42"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getDeckName/getDeckName.ts -->
<g id="edge212" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getDeckName/getDeckName.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1004.03,-3013.55C1021.18,-2984.19 1072.02,-2893.45 1095.38,-2811.28 1100.46,-2793.4 1092.31,-2741.12 1105.75,-2728.28 1136.25,-2699.13 1266.2,-2744.11 1295,-2713.28 1308.47,-2698.87 1292.76,-2020.14 1303,-2003.28 1310.66,-1990.66 1323.66,-1981.7 1336.8,-1975.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.52,-1977.43 1342.18,-1973.1 1335.84,-1973.58 1337.52,-1977.43"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getSearchTerm/getSearchTerm.ts -->
<g id="edge213" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getSearchTerm/getSearchTerm.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1003.23,-3013.59C1023.23,-2974.79 1096.94,-2832.46 1105.75,-2826.28 1175.42,-2777.42 1238.74,-2861.12 1295,-2797.28 1306.24,-2784.52 1294.07,-2505.75 1303,-2491.28 1310.76,-2478.72 1323.77,-2469.77 1336.91,-2463.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.62,-2465.49 1342.28,-2461.17 1335.94,-2461.65 1337.62,-2465.49"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts -->
<g id="edge214" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1001.49,-3013.53C1011.66,-2984.62 1045.83,-2898.9 1105.75,-2859.28 1177.04,-2812.15 1239.22,-2890.02 1295,-2825.28 1314.81,-2802.29 1287.02,-2578.08 1303,-2552.28 1310.78,-2539.73 1323.8,-2530.79 1336.93,-2524.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.64,-2526.51 1342.3,-2522.18 1335.96,-2522.66 1337.64,-2526.51"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="edge217" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1009.02,-3013.58C1028.61,-2995.41 1070.36,-2954.31 1095.38,-2912.28 1102.16,-2900.88 1095.66,-2892.9 1105.75,-2884.28 1170.97,-2828.55 1222.74,-2892.52 1295,-2846.28 1403.91,-2776.59 1426.06,-2734.09 1466.5,-2611.28 1478.45,-2575 1464.49,-1273.14 1474.5,-1236.28 1570.82,-881.51 1779.21,-871.24 1888.62,-520.28 1910.12,-451.32 1884.41,-428.43 1901.62,-358.28 1910.92,-320.4 1930.87,-279.11 1942.72,-256.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1944.44,-257.74 1945.41,-251.46 1940.73,-255.77 1944.44,-257.74"/>
</g>
<!-- src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge225" class="edge">
<title>src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1080.5,-3318.62C1086.12,-3315.4 1091.2,-3311.35 1095.38,-3306.28 1120.04,-3276.33 1079.96,-2986.26 1105.75,-2957.28 1159.62,-2896.76 1404.44,-2956.37 1466.5,-2904.28 1536.4,-2845.61 1546.23,-2724.98 1547.36,-2678.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1549.46,-2678.96 1547.44,-2672.93 1545.26,-2678.9 1549.46,-2678.96"/>
</g>
<!-- src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts -->
<g id="edge223" class="edge">
<title>src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1080.64,-3318.74C1086.23,-3315.49 1091.26,-3311.4 1095.38,-3306.28 1113.38,-3283.86 1089.78,-1260.19 1105.75,-1236.28 1114.54,-1223.13 1128.45,-1214.18 1143.05,-1208.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1143.51,-1210.18 1148.37,-1206.09 1142.02,-1206.25 1143.51,-1210.18"/>
</g>
<!-- src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/queries/getAvailableFilters/getAvailableFilters.ts -->
<g id="edge224" class="edge">
<title>src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/queries/getAvailableFilters/getAvailableFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1080.55,-3318.66C1086.16,-3315.43 1091.22,-3311.37 1095.38,-3306.28 1112.87,-3284.84 1093.57,-2833.13 1105.75,-2808.28 1153.37,-2711.17 1248.01,-2752.7 1295,-2655.28 1307.28,-2629.81 1288.36,-1661.48 1303,-1637.28 1310.64,-1624.65 1323.63,-1615.69 1336.77,-1609.43"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.49,-1611.41 1342.15,-1607.09 1335.82,-1607.56 1337.49,-1611.41"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge164" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M796.7,-4527.15C826.9,-4530.31 865.2,-4539.95 885.5,-4567.28 898.24,-4584.43 878.48,-5319.06 891.12,-5336.28 950.44,-5417.03 1006.84,-5389.26 1105.75,-5405.28 1204.81,-5421.33 1939.65,-5472.93 2008.88,-5400.28 2053.76,-5353.19 2057.18,-881.14 2057.37,-560.4"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2059.47,-560.81 2057.37,-554.8 2055.27,-560.8 2059.47,-560.81"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge165" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M772.47,-4516.54C803.02,-4499.93 861.44,-4462.97 885.5,-4412.28 895.6,-4390.99 877.57,-4219.56 891.12,-4200.28 904.15,-4181.76 926.84,-4171.6 947.81,-4166.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="948.07,-4168.14 953.41,-4164.69 947.09,-4164.05 948.07,-4168.14"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge173" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M815.61,-4593.09C841.14,-4591.85 868.59,-4585.15 885.5,-4565.28 899.01,-4549.4 879.33,-3081.47 891.12,-3064.28 903.1,-3046.83 923.68,-3036.81 943.48,-3031.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="943.99,-3033.09 949.26,-3029.53 942.92,-3029.03 943.99,-3033.09"/>
</g>
<!-- src/client/infrastructure/store/store.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge247" class="edge">
<title>src/client/infrastructure/store/store.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1024.47,-3622.93C1047.6,-3615.81 1079.95,-3601.75 1095.38,-3576.28 1112.38,-3548.19 1083.7,-3009.61 1105.75,-2985.28 1134.18,-2953.91 1253.02,-2968.78 1295,-2963.28 1371.38,-2953.28 1408.4,-2988.85 1466.5,-2938.28 1506.74,-2903.26 1535.85,-2735.2 1544.62,-2678.79"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1546.69,-2679.13 1545.52,-2672.88 1542.54,-2678.5 1546.69,-2679.13"/>
</g>
<!-- src/client/infrastructure/store/store.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="edge246" class="edge">
<title>src/client/infrastructure/store/store.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1024.44,-3622.92C1047.55,-3615.77 1079.89,-3601.71 1095.38,-3576.28 1121.45,-3533.47 1081.55,-3169.18 1105.75,-3125.28 1156.55,-3033.12 1206.37,-3035.97 1303,-2994.28 1371.5,-2964.73 1403.6,-2996.39 1466.5,-2956.28 1564.04,-2894.09 1528.27,-2811.76 1626.88,-2751.28 1630.23,-2749.22 1633.85,-2747.48 1637.6,-2746.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1638.08,-2748.06 1643.1,-2744.16 1636.75,-2744.08 1638.08,-2748.06"/>
</g>
<!-- src/client/infrastructure/store/store.ts&#45;&gt;src/client/application/subscribers/subscribeToDeckDraft.ts -->
<g id="edge248" class="edge">
<title>src/client/infrastructure/store/store.ts&#45;&gt;src/client/application/subscribers/subscribeToDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M970,-3625.61C940.36,-3619.85 896.15,-3605.45 888.31,-3570.28 883.55,-3548.89 886.79,-2803.14 888.31,-2781.28 909.6,-2474.91 977.39,-2105.71 993.7,-2019.77"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="995.76,-2020.18 994.82,-2013.89 991.63,-2019.39 995.76,-2020.18"/>
</g>
<!-- src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts -->
<g id="node146" class="node">
<title>src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts</title>
<g id="a_node146"><a xlink:href="src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts" xlink:title="browserDeckDraftService.ts">
<polygon fill="#6cbaff" stroke="black" points="1269,-3642.53 1139.75,-3642.53 1139.75,-3624.03 1269,-3624.03 1269,-3642.53"/>
<text text-anchor="start" x="1147.75" y="-3629.98" font-family="Helvetica,sans-Serif" font-size="9.00">browserDeckDraftService.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/store/store.ts&#45;&gt;src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts -->
<g id="edge249" class="edge">
<title>src/client/infrastructure/store/store.ts&#45;&gt;src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1024.65,-3629.8C1051.39,-3630.32 1093.97,-3631.15 1130.86,-3631.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1130.55,-3633.96 1136.59,-3631.98 1130.63,-3629.76 1130.55,-3633.96"/>
</g>
<!-- src/client/infrastructure/services/location/browserLocationService.ts -->
<g id="node147" class="node">
<title>src/client/infrastructure/services/location/browserLocationService.ts</title>
<g id="a_node147"><a xlink:href="src/client/infrastructure/services/location/browserLocationService.ts" xlink:title="browserLocationService.ts">
<polygon fill="#6cbaff" stroke="black" points="1266.38,-3703.53 1142.38,-3703.53 1142.38,-3685.03 1266.38,-3685.03 1266.38,-3703.53"/>
<text text-anchor="start" x="1150.38" y="-3690.98" font-family="Helvetica,sans-Serif" font-size="9.00">browserLocationService.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/store/store.ts&#45;&gt;src/client/infrastructure/services/location/browserLocationService.ts -->
<g id="edge250" class="edge">
<title>src/client/infrastructure/store/store.ts&#45;&gt;src/client/infrastructure/services/location/browserLocationService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1019.31,-3638.86C1040.72,-3648.33 1075.04,-3662.74 1105.75,-3672.28 1118.09,-3676.11 1131.49,-3679.57 1144.3,-3682.56"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1143.79,-3684.59 1150.1,-3683.87 1144.72,-3680.5 1143.79,-3684.59"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge171" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M754.32,-4718.95C762.72,-4785.63 814.88,-5177.38 891.12,-5265.28 987.53,-5376.43 1056.24,-5375.28 1203.38,-5375.28 1203.38,-5375.28 1203.38,-5375.28 1832.5,-5375.28 1983.82,-5375.28 2003.69,-2808.51 2008.88,-2657.28 2009.38,-2642.46 2011.22,-535.99 2016.88,-522.28 2020.97,-512.37 2028.73,-503.57 2036.38,-496.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2037.68,-498.34 2040.94,-492.87 2034.99,-495.11 2037.68,-498.34"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx -->
<g id="node125" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx</title>
<g id="a_node125"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx" xlink:title="DeckBuildingSkeletonCard.tsx">
<polygon fill="#6cbaff" stroke="black" points="590.5,-4474.53 450,-4474.53 450,-4456.03 590.5,-4456.03 590.5,-4474.53"/>
<text text-anchor="start" x="458" y="-4461.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuildingSkeletonCard.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge172" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M590.78,-4467.58C609.37,-4464.56 627.7,-4457.59 640,-4443.28 664.27,-4415.03 624,-4134.19 650.75,-4108.28 688.22,-4071.99 845.25,-4075.09 885.5,-4108.28 895.63,-4116.63 881.91,-4127.93 891.12,-4137.28 905.65,-4152.02 927.35,-4158.12 947.26,-4160.29"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="947.08,-4162.38 953.23,-4160.78 947.43,-4158.2 947.08,-4162.38"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge222" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1069.92,-3539.49C1079.64,-3536.66 1088.58,-3531.9 1095.38,-3524.28 1115.83,-3501.35 1085.28,-2994.2 1105.75,-2971.28 1134.01,-2939.64 1253,-2952.24 1295,-2946.28 1371.33,-2935.45 1408.03,-2970.52 1466.5,-2920.28 1540.31,-2856.86 1547.71,-2727.31 1547.78,-2679"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1549.88,-2678.98 1547.73,-2673 1545.68,-2679.02 1549.88,-2678.98"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/commands/loadGameSettings/loadGameSettings.ts -->
<g id="edge218" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/commands/loadGameSettings/loadGameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1070.02,-3539.57C1079.72,-3536.74 1088.64,-3531.95 1095.38,-3524.28 1117.47,-3499.14 1087.16,-1142.12 1105.75,-1114.28 1115.01,-1100.42 1129.96,-1091.23 1145.41,-1085.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1145.72,-1087.27 1150.65,-1083.26 1144.3,-1083.31 1145.72,-1087.27"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/getGameSettings/getGameSettings.ts -->
<g id="edge219" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/getGameSettings/getGameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1069.95,-3539.5C1079.66,-3536.68 1088.59,-3531.91 1095.38,-3524.28 1120.11,-3496.46 1082.3,-2883.19 1105.75,-2854.28 1160.08,-2787.29 1240.71,-2878.31 1295,-2811.28 1314.72,-2786.93 1286.7,-2274.04 1303,-2247.28 1310.68,-2234.67 1323.68,-2225.72 1336.82,-2219.46"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.54,-2221.44 1342.2,-2217.12 1335.86,-2217.59 1337.54,-2221.44"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/getGameSettingsError/getGameSettingsError.ts -->
<g id="edge220" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/getGameSettingsError/getGameSettingsError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1069.93,-3539.49C1079.65,-3536.67 1088.59,-3531.9 1095.38,-3524.28 1117.55,-3499.38 1086.53,-2951.52 1105.75,-2924.28 1157.55,-2850.88 1243.29,-2926.75 1295,-2853.28 1312.43,-2828.52 1287.24,-2334.14 1303,-2308.28 1310.68,-2295.68 1323.68,-2286.72 1336.82,-2280.46"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1337.54,-2282.44 1342.2,-2278.12 1335.86,-2278.59 1337.54,-2282.44"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts -->
<g id="edge221" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1069.93,-3539.49C1079.64,-3536.66 1088.58,-3531.9 1095.38,-3524.28 1116.34,-3500.76 1085.32,-2981.26 1105.75,-2957.28 1161.17,-2892.23 1232.81,-2981.9 1295,-2923.28 1344.57,-2876.56 1371.06,-2656.17 1377.93,-2590.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1380,-2591.06 1378.52,-2584.87 1375.82,-2590.63 1380,-2591.06"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/Match/Match.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge186" class="edge">
<title>src/client/infrastructure/components/app/Gaming/Match/Match.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M547.81,-5092.22C627.1,-5115.92 862.44,-5186.28 956.63,-5214.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="955.78,-5216.37 962.13,-5216.08 956.98,-5212.35 955.78,-5216.37"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge187" class="edge">
<title>src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M570.1,-5028.67C642.83,-5038.34 782.1,-5063.34 885.5,-5121.28 927,-5144.54 964.77,-5186.83 983.63,-5210.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="981.79,-5211.07 987.17,-5214.45 985.07,-5208.45 981.79,-5211.07"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge188" class="edge">
<title>src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M570.09,-5027.66C594.92,-5027 623.3,-5021.21 640,-5001.28 667.59,-4968.34 622.51,-3486.67 650.75,-3454.28 685.39,-3414.55 833.93,-3431.19 885.5,-3420.28 909.71,-3415.16 936.43,-3407.73 957.56,-3401.43"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="957.92,-3403.52 963.06,-3399.78 956.71,-3399.5 957.92,-3403.52"/>
</g>
<!-- src/client/infrastructure/components/debug/JsonObjectViewer/JsonObjectViewer.tsx -->
<g id="node130" class="node">
<title>src/client/infrastructure/components/debug/JsonObjectViewer/JsonObjectViewer.tsx</title>
<g id="a_node130"><a xlink:href="src/client/infrastructure/components/debug/JsonObjectViewer/JsonObjectViewer.tsx" xlink:title="JsonObjectViewer.tsx">
<polygon fill="#6cbaff" stroke="black" points="571.75,-3903.53 468.75,-3903.53 468.75,-3885.03 571.75,-3885.03 571.75,-3903.53"/>
<text text-anchor="start" x="476.75" y="-3890.98" font-family="Helvetica,sans-Serif" font-size="9.00">JsonObjectViewer.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/debug/MatchMakingConsoleEvents/MatchMakingConsoleEvents.tsx -->
<g id="node132" class="node">
<title>src/client/infrastructure/components/debug/MatchMakingConsoleEvents/MatchMakingConsoleEvents.tsx</title>
<g id="a_node132"><a xlink:href="src/client/infrastructure/components/debug/MatchMakingConsoleEvents/MatchMakingConsoleEvents.tsx" xlink:title="MatchMakingConsoleEvents.tsx">
<polygon fill="#6cbaff" stroke="black" points="593.5,-4025.53 447,-4025.53 447,-4007.03 593.5,-4007.03 593.5,-4025.53"/>
<text text-anchor="start" x="455" y="-4012.98" font-family="Helvetica,sans-Serif" font-size="9.00">MatchMakingConsoleEvents.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/SkeletonHelper/SkeletonHelper.tsx -->
<g id="node136" class="node">
<title>src/client/infrastructure/components/ui/SkeletonHelper/SkeletonHelper.tsx</title>
<g id="a_node136"><a xlink:href="src/client/infrastructure/components/ui/SkeletonHelper/SkeletonHelper.tsx" xlink:title="SkeletonHelper.tsx">
<polygon fill="#6cbaff" stroke="black" points="1043.5,-4229.53 951,-4229.53 951,-4211.03 1043.5,-4211.03 1043.5,-4229.53"/>
<text text-anchor="start" x="959" y="-4216.98" font-family="Helvetica,sans-Serif" font-size="9.00">SkeletonHelper.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts -->
<g id="edge193" class="edge">
<title>src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1000.35,-4290.94C1020.06,-4381.74 1172.63,-5084.63 1199.38,-5207.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1197.3,-5208.2 1200.63,-5213.62 1201.41,-5207.31 1197.3,-5208.2"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckById/useDeckById.tsx -->
<g id="node139" class="node">
<title>src/client/infrastructure/hooks/useDeckById/useDeckById.tsx</title>
<g id="a_node139"><a xlink:href="src/client/infrastructure/hooks/useDeckById/useDeckById.tsx" xlink:title="useDeckById.tsx">
<polygon fill="#6cbaff" stroke="black" points="1039,-3215.53 955.5,-3215.53 955.5,-3197.03 1039,-3197.03 1039,-3215.53"/>
<text text-anchor="start" x="963.5" y="-3202.98" font-family="Helvetica,sans-Serif" font-size="9.00">useDeckById.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/lib/groupByDataProperty.ts -->
<g id="node141" class="node">
<title>src/client/infrastructure/lib/groupByDataProperty.ts</title>
<g id="a_node141"><a xlink:href="src/client/infrastructure/lib/groupByDataProperty.ts" xlink:title="groupByDataProperty.ts">
<polygon fill="#6cbaff" stroke="black" points="1260.75,-5297.53 1148,-5297.53 1148,-5279.03 1260.75,-5279.03 1260.75,-5297.53"/>
<text text-anchor="start" x="1156" y="-5284.98" font-family="Helvetica,sans-Serif" font-size="9.00">groupByDataProperty.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/lib/groupByDataProperty.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge228" class="edge">
<title>src/client/infrastructure/lib/groupByDataProperty.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1261.02,-5289.26C1273.75,-5286.78 1286.14,-5281.74 1295,-5272.28 1637.75,-4906.67 1046.23,-3031.28 1547.38,-3031.28 1547.38,-3031.28 1547.38,-3031.28 1832.5,-3031.28 2084.04,-3031.28 1995.08,-1025.44 2008.88,-774.28 2010.41,-746.31 2005.82,-548.02 2016.88,-522.28 2021.11,-512.43 2028.89,-503.64 2036.52,-496.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2037.82,-498.4 2041.05,-492.93 2035.11,-495.19 2037.82,-498.4"/>
</g>
<!-- src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="edge243" class="edge">
<title>src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1207.88,-3623.57C1220.03,-3576 1273.16,-3362.59 1295,-3184.28 1296.16,-3174.84 1296.33,-3020.06 1303,-3013.28 1352.32,-2963.14 1569.9,-3043.76 1618.88,-2993.28 1637.61,-2973.97 1609.44,-2771.77 1626.88,-2751.28 1629.89,-2747.74 1633.56,-2744.98 1637.61,-2742.85"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1638.25,-2744.86 1643,-2740.64 1636.65,-2740.98 1638.25,-2744.86"/>
</g>
<!-- src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/domain/DeckBuilder/DeckDraft.ts -->
<g id="edge244" class="edge">
<title>src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/domain/DeckBuilder/DeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1207.89,-3623.58C1220.1,-3576.01 1273.47,-3362.63 1295,-3184.28 1304.86,-3102.57 1284.96,-3079.58 1303,-2999.28 1344.02,-2816.7 1429.12,-2794.64 1466.5,-2611.28 1469.73,-2595.44 1466.31,-297.22 1474.5,-283.28 1481.86,-270.74 1494.63,-261.73 1507.44,-255.42"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1508.01,-257.46 1512.61,-253.07 1506.28,-253.63 1508.01,-257.46"/>
</g>
<!-- src/client/infrastructure/services/location/browserLocationService.ts&#45;&gt;src/client/application/services/LocationService.ts -->
<g id="edge245" class="edge">
<title>src/client/infrastructure/services/location/browserLocationService.ts&#45;&gt;src/client/application/services/LocationService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1266.6,-3690.52C1277.6,-3687.01 1287.83,-3681.31 1295,-3672.28 1306.14,-3658.25 1290.68,-3040.29 1303,-3027.28 1327.19,-3001.75 1590.92,-3027.63 1618.88,-3006.28 1682.23,-2957.91 1691.92,-2852.18 1693.18,-2809.6"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1695.28,-2809.7 1693.29,-2803.66 1691.08,-2809.62 1695.28,-2809.7"/>
</g>
<!-- src/server/DependencyInjection.ts -->
<g id="node148" class="node">
<title>src/server/DependencyInjection.ts</title>
<g id="a_node148"><a xlink:href="src/server/DependencyInjection.ts" xlink:title="DependencyInjection.ts">
<path fill="#ccffcc" stroke="black" d="M570.08,-7290.53C570.08,-7290.53 470.42,-7290.53 470.42,-7290.53 467.33,-7290.53 464.25,-7287.45 464.25,-7284.36 464.25,-7284.36 464.25,-7278.2 464.25,-7278.2 464.25,-7275.11 467.33,-7272.03 470.42,-7272.03 470.42,-7272.03 570.08,-7272.03 570.08,-7272.03 573.17,-7272.03 576.25,-7275.11 576.25,-7278.2 576.25,-7278.2 576.25,-7284.36 576.25,-7284.36 576.25,-7287.45 573.17,-7290.53 570.08,-7290.53"/>
<text text-anchor="start" x="472.25" y="-7277.98" font-family="Helvetica,sans-Serif" font-size="9.00">DependencyInjection.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts -->
<g id="node149" class="node">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts</title>
<g id="a_node149"><a xlink:href="src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts" xlink:title="SaveDeckCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="807,-5603.53 697.25,-5603.53 697.25,-5585.03 807,-5585.03 807,-5603.53"/>
<text text-anchor="start" x="705.25" y="-5590.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">SaveDeckCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts -->
<g id="node150" class="node">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts</title>
<g id="a_node150"><a xlink:href="src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts" xlink:title="SaveDeckCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="591.25,-5603.53 449.25,-5603.53 449.25,-5585.03 591.25,-5585.03 591.25,-5603.53"/>
<text text-anchor="start" x="457.25" y="-5590.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">SaveDeckCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts -->
<g id="edge251" class="edge">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M591.47,-5594.28C622.35,-5594.28 658.34,-5594.28 688.32,-5594.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="687.99,-5596.38 693.99,-5594.28 687.99,-5592.18 687.99,-5596.38"/>
</g>
<!-- src/server/application/ports/DeckRepository.ts -->
<g id="node151" class="node">
<title>src/server/application/ports/DeckRepository.ts</title>
<g id="a_node151"><a xlink:href="src/server/application/ports/DeckRepository.ts" xlink:title="DeckRepository.ts">
<polygon fill="#dd1c1c" stroke="black" points="796.88,-6463.53 707.38,-6463.53 707.38,-6445.03 796.88,-6445.03 796.88,-6463.53"/>
<text text-anchor="start" x="715.38" y="-6450.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">DeckRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/application/ports/DeckRepository.ts -->
<g id="edge252" class="edge">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/application/ports/DeckRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M551.06,-5603.99C579.59,-5615.06 621.02,-5636.17 640,-5670.28 660.75,-5707.57 622.83,-6406.01 650.75,-6438.28 662.46,-6451.81 680.64,-6457.13 698.33,-6458.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="697.82,-6460.7 703.92,-6458.92 698.04,-6456.51 697.82,-6460.7"/>
</g>
<!-- src/server/domain/Deck/Deck.ts -->
<g id="node152" class="node">
<title>src/server/domain/Deck/Deck.ts</title>
<g id="a_node152"><a xlink:href="src/server/domain/Deck/Deck.ts" xlink:title="Deck.ts">
<polygon fill="#dc6b0d" stroke="black" points="1024.25,-6537.53 970.25,-6537.53 970.25,-6519.03 1024.25,-6519.03 1024.25,-6537.53"/>
<text text-anchor="start" x="982.25" y="-6524.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">Deck.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/domain/Deck/Deck.ts -->
<g id="edge253" class="edge">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M541.4,-5584.72C602.99,-5557.05 790.34,-5484.69 885.5,-5579.28 894.71,-5588.44 882.54,-6504.54 891.12,-6514.28 907.99,-6533.43 937.41,-6535.95 960.85,-6534.23"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.04,-6536.32 966.81,-6533.65 960.63,-6532.14 961.04,-6536.32"/>
</g>
<!-- src/server/application/ports/DeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts -->
<g id="edge282" class="edge">
<title>src/server/application/ports/DeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M770.84,-6463.9C796.55,-6477.41 846.07,-6501.75 891.12,-6514.28 913.99,-6520.64 940.49,-6524.15 961.16,-6526.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="960.75,-6528.13 966.91,-6526.55 961.11,-6523.95 960.75,-6528.13"/>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts -->
<g id="node153" class="node">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts</title>
<g id="a_node153"><a xlink:href="src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts" xlink:title="LeaveMatchCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="811.12,-5699.53 693.12,-5699.53 693.12,-5681.03 811.12,-5681.03 811.12,-5699.53"/>
<text text-anchor="start" x="701.12" y="-5686.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">LeaveMatchCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts -->
<g id="node154" class="node">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts</title>
<g id="a_node154"><a xlink:href="src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts" xlink:title="LeaveMatchCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="595.38,-5699.53 445.12,-5699.53 445.12,-5681.03 595.38,-5681.03 595.38,-5699.53"/>
<text text-anchor="start" x="453.12" y="-5686.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">LeaveMatchCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts -->
<g id="edge254" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M595.57,-5690.28C623.97,-5690.28 656.18,-5690.28 683.89,-5690.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="683.69,-5692.38 689.69,-5690.28 683.69,-5688.18 683.69,-5692.38"/>
</g>
<!-- src/server/application/ports/Context.ts -->
<g id="node155" class="node">
<title>src/server/application/ports/Context.ts</title>
<g id="a_node155"><a xlink:href="src/server/application/ports/Context.ts" xlink:title="Context.ts">
<polygon fill="#dd1c1c" stroke="black" points="780.38,-6215.53 723.88,-6215.53 723.88,-6197.03 780.38,-6197.03 780.38,-6215.53"/>
<text text-anchor="start" x="731.88" y="-6202.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">Context.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge255" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M550.68,-5699.95C579.13,-5711.04 620.69,-5732.21 640,-5766.28 659.95,-5801.48 632.2,-6094.32 650.75,-6130.28 665.63,-6159.13 697.02,-6180.08 720.76,-6192.59"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="719.79,-6194.46 726.1,-6195.3 721.69,-6190.71 719.79,-6194.46"/>
</g>
<!-- src/server/application/ports/MatchRepository.ts -->
<g id="node156" class="node">
<title>src/server/application/ports/MatchRepository.ts</title>
<g id="a_node156"><a xlink:href="src/server/application/ports/MatchRepository.ts" xlink:title="MatchRepository.ts">
<polygon fill="#dd1c1c" stroke="black" points="798.75,-6277.53 705.5,-6277.53 705.5,-6259.03 798.75,-6259.03 798.75,-6277.53"/>
<text text-anchor="start" x="713.5" y="-6264.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge256" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M551,-5700.03C579.49,-5711.12 620.88,-5732.24 640,-5766.28 653.23,-5789.83 632.99,-6231.93 650.75,-6252.28 662.06,-6265.25 679.32,-6270.66 696.35,-6272.38"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="696.06,-6274.47 702.18,-6272.77 696.33,-6270.28 696.06,-6274.47"/>
</g>
<!-- src/server/domain/Match/errors/MatchAlreadyFinishedError.ts -->
<g id="node157" class="node">
<title>src/server/domain/Match/errors/MatchAlreadyFinishedError.ts</title>
<g id="a_node157"><a xlink:href="src/server/domain/Match/errors/MatchAlreadyFinishedError.ts" xlink:title="MatchAlreadyFinishedError.ts">
<polygon fill="#dc6b0d" stroke="black" points="1065.25,-6331.53 929.25,-6331.53 929.25,-6313.03 1065.25,-6313.03 1065.25,-6331.53"/>
<text text-anchor="start" x="937.25" y="-6318.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchAlreadyFinishedError.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/errors/MatchAlreadyFinishedError.ts -->
<g id="edge257" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/errors/MatchAlreadyFinishedError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M564.9,-5680.59C590.96,-5672.45 622.32,-5658.03 640,-5633.28 651.84,-5616.71 635.53,-5555.81 650.75,-5542.28 728.73,-5472.97 806.71,-5473.89 885.5,-5542.28 945.28,-5594.17 987.51,-6190.13 995.04,-6303.66"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="992.94,-6303.72 995.43,-6309.57 997.14,-6303.45 992.94,-6303.72"/>
</g>
<!-- src/server/domain/Match/errors/MatchNotFoundError.ts -->
<g id="node158" class="node">
<title>src/server/domain/Match/errors/MatchNotFoundError.ts</title>
<g id="a_node158"><a xlink:href="src/server/domain/Match/errors/MatchNotFoundError.ts" xlink:title="MatchNotFoundError.ts">
<polygon fill="#dc6b0d" stroke="black" points="1052.12,-6362.53 942.38,-6362.53 942.38,-6344.03 1052.12,-6344.03 1052.12,-6362.53"/>
<text text-anchor="start" x="950.38" y="-6349.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchNotFoundError.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/errors/MatchNotFoundError.ts -->
<g id="edge258" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/errors/MatchNotFoundError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M564.41,-5680.54C590.45,-5672.36 621.97,-5657.92 640,-5633.28 660.15,-5605.75 624.99,-5579.65 650.75,-5557.28 690.14,-5523.07 848.48,-5520.53 885.5,-5557.28 900.88,-5572.55 876.94,-6320.9 891.12,-6337.28 901.68,-6349.47 917.26,-6355.21 933.22,-6357.47"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="932.9,-6359.55 939.08,-6358.08 933.33,-6355.37 932.9,-6359.55"/>
</g>
<!-- src/server/domain/Match/Match.ts -->
<g id="node170" class="node">
<title>src/server/domain/Match/Match.ts</title>
<g id="a_node170"><a xlink:href="src/server/domain/Match/Match.ts" xlink:title="Match.ts">
<polygon fill="#dc6b0d" stroke="black" points="1024.25,-6415.53 970.25,-6415.53 970.25,-6397.03 1024.25,-6397.03 1024.25,-6415.53"/>
<text text-anchor="start" x="980.38" y="-6402.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">Match.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/MatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts -->
<g id="edge283" class="edge">
<title>src/server/application/ports/MatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M798.95,-6260.37C827.99,-6258.2 864.01,-6261.27 885.5,-6284.28 901.9,-6301.84 874.83,-6374.62 891.12,-6392.28 908.43,-6411.04 937.81,-6413.59 961.12,-6411.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.25,-6414.08 967.03,-6411.43 960.86,-6409.9 961.25,-6414.08"/>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts -->
<g id="node159" class="node">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts</title>
<g id="a_node159"><a xlink:href="src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts" xlink:title="AddPlayerToMatchMakingQueueCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="853.5,-5795.53 650.75,-5795.53 650.75,-5777.03 853.5,-5777.03 853.5,-5795.53"/>
<text text-anchor="start" x="658.75" y="-5782.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AddPlayerToMatchMakingQueueCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts -->
<g id="node160" class="node">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts</title>
<g id="a_node160"><a xlink:href="src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts" xlink:title="AddPlayerToMatchMakingQueueCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="637.75,-5795.53 402.75,-5795.53 402.75,-5777.03 637.75,-5777.03 637.75,-5795.53"/>
<text text-anchor="start" x="410.75" y="-5782.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AddPlayerToMatchMakingQueueCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge260" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.18,-5795.94C610.59,-5801.97 628.46,-5811.68 640,-5827.28 660.03,-5854.36 635.22,-6100.39 650.75,-6130.28 665.71,-6159.08 697.09,-6180.04 720.8,-6192.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="719.83,-6194.43 726.13,-6195.27 721.73,-6190.68 719.83,-6194.43"/>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts -->
<g id="edge259" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M638.21,-5786.28C639.36,-5786.28 640.52,-5786.28 641.67,-5786.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="641.46,-5788.38 647.46,-5786.28 641.46,-5784.18 641.46,-5788.38"/>
</g>
<!-- src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="node161" class="node">
<title>src/server/application/ports/MatchmakingQueueRepository.ts</title>
<g id="a_node161"><a xlink:href="src/server/application/ports/MatchmakingQueueRepository.ts" xlink:title="MatchmakingQueueRepository.ts">
<polygon fill="#dd1c1c" stroke="black" points="827.62,-6308.53 676.62,-6308.53 676.62,-6290.03 827.62,-6290.03 827.62,-6308.53"/>
<text text-anchor="start" x="684.62" y="-6295.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchmakingQueueRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge261" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.61,-5795.95C610.91,-5801.99 628.61,-5811.69 640,-5827.28 654.95,-5847.74 634.08,-6264.2 650.75,-6283.28 655.67,-6288.92 661.72,-6293.12 668.38,-6296.22"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="667.28,-6298.05 673.64,-6298.27 668.81,-6294.13 667.28,-6298.05"/>
</g>
<!-- src/server/application/ports/TimeService.ts -->
<g id="node162" class="node">
<title>src/server/application/ports/TimeService.ts</title>
<g id="a_node162"><a xlink:href="src/server/application/ports/TimeService.ts" xlink:title="TimeService.ts">
<polygon fill="#dd1c1c" stroke="black" points="790.12,-6246.53 714.12,-6246.53 714.12,-6228.03 790.12,-6228.03 790.12,-6246.53"/>
<text text-anchor="start" x="722.12" y="-6233.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">TimeService.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/TimeService.ts -->
<g id="edge262" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/TimeService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.58,-5795.98C610.87,-5802.01 628.58,-5811.72 640,-5827.28 665.9,-5862.59 621.87,-6188.36 650.75,-6221.28 664.07,-6236.47 685.55,-6241.27 705.22,-6241.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="705.06,-6244.04 711.07,-6241.97 705.08,-6239.84 705.06,-6244.04"/>
</g>
<!-- src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="node163" class="node">
<title>src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<g id="a_node163"><a xlink:href="src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts" xlink:title="MatchmakingQueueItem.ts">
<polygon fill="#dc6b0d" stroke="black" points="1266.75,-6598.53 1142,-6598.53 1142,-6580.03 1266.75,-6580.03 1266.75,-6598.53"/>
<text text-anchor="start" x="1150" y="-6585.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchmakingQueueItem.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge263" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M566.57,-5776.55C592.56,-5768.47 623.26,-5754.13 640,-5729.28 661.4,-5697.52 622.84,-5583.5 650.75,-5557.28 726.79,-5485.84 801.57,-5495.31 885.5,-5557.28 1062.07,-5687.65 1181.57,-6442.89 1200.68,-6570.86"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1198.57,-6570.94 1201.53,-6576.57 1202.73,-6570.32 1198.57,-6570.94"/>
</g>
<!-- src/server/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge285" class="edge">
<title>src/server/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M827.87,-6290.39C915.42,-6281.6 1053.79,-6273.23 1095.38,-6302.28 1186.8,-6366.16 1200.92,-6517.69 1203.03,-6570.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1200.93,-6570.63 1203.21,-6576.57 1205.13,-6570.51 1200.93,-6570.63"/>
</g>
<!-- src/server/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="node180" class="node">
<title>src/server/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<g id="a_node180"><a xlink:href="src/server/domain/MatchmakingQueue/MatchmakingQueue.ts" xlink:title="MatchmakingQueue.ts">
<polygon fill="#dc6b0d" stroke="black" points="1051,-6598.53 943.5,-6598.53 943.5,-6580.03 1051,-6580.03 1051,-6598.53"/>
<text text-anchor="start" x="951.5" y="-6585.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchmakingQueue.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="edge284" class="edge">
<title>src/server/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M827.85,-6291.05C849.39,-6292.79 870.96,-6299.24 885.5,-6315.28 894.91,-6325.66 882.17,-6556.51 891.12,-6567.28 901.83,-6580.16 917.97,-6586.71 934.41,-6589.79"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="933.77,-6591.82 940.02,-6590.64 934.4,-6587.66 933.77,-6591.82"/>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts -->
<g id="node164" class="node">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts</title>
<g id="a_node164"><a xlink:href="src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts" xlink:title="CancelMatchRegistrationCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="837.75,-5856.53 666.5,-5856.53 666.5,-5838.03 837.75,-5838.03 837.75,-5856.53"/>
<text text-anchor="start" x="674.5" y="-5843.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CancelMatchRegistrationCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts -->
<g id="node165" class="node">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts</title>
<g id="a_node165"><a xlink:href="src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts" xlink:title="CancelMatchRegistrationCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="622,-5856.53 418.5,-5856.53 418.5,-5838.03 622,-5838.03 622,-5856.53"/>
<text text-anchor="start" x="426.5" y="-5843.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CancelMatchRegistrationCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge265" class="edge">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.09,-5857C610.5,-5863.03 628.39,-5872.73 640,-5888.28 656.1,-5909.85 638.24,-6106.45 650.75,-6130.28 665.84,-6159.02 697.2,-6179.99 720.87,-6192.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="719.89,-6194.39 726.19,-6195.24 721.8,-6190.65 719.89,-6194.39"/>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge266" class="edge">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.58,-5856.98C610.87,-5863.01 628.58,-5872.72 640,-5888.28 665.96,-5923.69 621.8,-6250.27 650.75,-6283.28 655.68,-6288.91 661.74,-6293.11 668.4,-6296.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="667.31,-6298.03 673.66,-6298.25 668.83,-6294.11 667.31,-6298.03"/>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts -->
<g id="edge264" class="edge">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M622.41,-5847.28C634.03,-5847.28 645.83,-5847.28 657.34,-5847.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="657.05,-5849.38 663.05,-5847.28 657.05,-5845.18 657.05,-5849.38"/>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts -->
<g id="node166" class="node">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts</title>
<g id="a_node166"><a xlink:href="src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts" xlink:title="CleanUpMatchMakingQueueCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="845.62,-5917.53 658.62,-5917.53 658.62,-5899.03 845.62,-5899.03 845.62,-5917.53"/>
<text text-anchor="start" x="666.62" y="-5904.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CleanUpMatchMakingQueueCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts -->
<g id="node167" class="node">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts</title>
<g id="a_node167"><a xlink:href="src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts" xlink:title="CleanUpMatchMakingQueueCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="629.88,-5917.53 410.62,-5917.53 410.62,-5899.03 629.88,-5899.03 629.88,-5917.53"/>
<text text-anchor="start" x="418.62" y="-5904.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CleanUpMatchMakingQueueCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge268" class="edge">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M591.64,-5918.01C610.14,-5924.04 628.2,-5933.73 640,-5949.28 664.35,-5981.39 631.75,-6094.75 650.75,-6130.28 666.05,-6158.9 697.38,-6179.89 720.98,-6192.47"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="719.98,-6194.32 726.28,-6195.19 721.9,-6190.58 719.98,-6194.32"/>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge270" class="edge">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.18,-5917.94C610.59,-5923.97 628.46,-5933.68 640,-5949.28 660.03,-5976.36 628.43,-6227.05 650.75,-6252.28 662.1,-6265.11 679.27,-6270.51 696.23,-6272.27"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="695.89,-6274.36 702.02,-6272.67 696.18,-6270.17 695.89,-6274.36"/>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge269" class="edge">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.53,-5918.01C610.82,-5924.05 628.55,-5933.74 640,-5949.28 662.03,-5979.17 626.2,-6255.43 650.75,-6283.28 655.7,-6288.89 661.76,-6293.09 668.43,-6296.18"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="667.34,-6298.01 673.7,-6298.22 668.86,-6294.09 667.34,-6298.01"/>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts -->
<g id="edge267" class="edge">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M630.13,-5908.28C636.59,-5908.28 643.08,-5908.28 649.51,-5908.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="649.21,-5910.38 655.21,-5908.28 649.21,-5906.18 649.21,-5910.38"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts -->
<g id="node168" class="node">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts</title>
<g id="a_node168"><a xlink:href="src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts" xlink:title="MakeMatchCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="809.62,-5978.53 694.62,-5978.53 694.62,-5960.03 809.62,-5960.03 809.62,-5978.53"/>
<text text-anchor="start" x="702.62" y="-5965.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MakeMatchCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts -->
<g id="node169" class="node">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts</title>
<g id="a_node169"><a xlink:href="src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts" xlink:title="MakeMatchCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="593.88,-5978.53 446.62,-5978.53 446.62,-5960.03 593.88,-5960.03 593.88,-5978.53"/>
<text text-anchor="start" x="454.62" y="-5965.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MakeMatchCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge272" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M590.41,-5978.94C609.23,-5984.95 627.77,-5994.65 640,-6010.28 656.5,-6031.37 637.77,-6106.86 650.75,-6130.28 666.58,-6158.84 698.11,-6179.95 721.63,-6192.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="720.6,-6194.4 726.89,-6195.3 722.53,-6190.67 720.6,-6194.4"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge274" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.09,-5979C610.5,-5985.03 628.39,-5994.73 640,-6010.28 656.1,-6031.85 632.83,-6232.2 650.75,-6252.28 662.16,-6265.06 679.34,-6270.45 696.29,-6272.22"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="695.95,-6274.3 702.08,-6272.62 696.24,-6270.11 695.95,-6274.3"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge273" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.14,-5978.97C610.55,-5984.99 628.43,-5994.7 640,-6010.28 658.1,-6034.65 630.6,-6260.58 650.75,-6283.28 655.68,-6288.83 661.69,-6292.99 668.31,-6296.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="667.14,-6297.86 673.5,-6298.09 668.67,-6293.95 667.14,-6297.86"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts -->
<g id="edge271" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M594.3,-5969.28C623.61,-5969.28 657.13,-5969.28 685.63,-5969.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="685.28,-5971.38 691.28,-5969.28 685.28,-5967.18 685.28,-5971.38"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/Match.ts -->
<g id="edge275" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M594.21,-5971.01C611.62,-5967.71 628.48,-5960.74 640,-5947.28 666.97,-5915.79 621.01,-5603.17 650.75,-5574.28 669.46,-5556.1 866.99,-5555.9 885.5,-5574.28 901.62,-5590.29 876.1,-6375.24 891.12,-6392.28 908,-6411.42 937.42,-6413.94 960.85,-6412.22"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.04,-6414.32 966.81,-6411.64 960.63,-6410.14 961.04,-6414.32"/>
</g>
<!-- src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts -->
<g id="node171" class="node">
<title>src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts</title>
<g id="a_node171"><a xlink:href="src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts" xlink:title="UpdatePlayersStatusCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="829.12,-6039.53 675.12,-6039.53 675.12,-6021.03 829.12,-6021.03 829.12,-6039.53"/>
<text text-anchor="start" x="683.12" y="-6026.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UpdatePlayersStatusCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts -->
<g id="node172" class="node">
<title>src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts</title>
<g id="a_node172"><a xlink:href="src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts" xlink:title="UpdatePlayersStatusCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="613.38,-6039.53 427.12,-6039.53 427.12,-6021.03 613.38,-6021.03 613.38,-6039.53"/>
<text text-anchor="start" x="435.12" y="-6026.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UpdatePlayersStatusCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts -->
<g id="edge276" class="edge">
<title>src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M613.76,-6030.28C630.97,-6030.28 648.88,-6030.28 665.86,-6030.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="665.62,-6032.38 671.62,-6030.28 665.62,-6028.18 665.62,-6032.38"/>
</g>
<!-- src/server/application/ports/AppUserRepository.ts -->
<g id="node173" class="node">
<title>src/server/application/ports/AppUserRepository.ts</title>
<g id="a_node173"><a xlink:href="src/server/application/ports/AppUserRepository.ts" xlink:title="AppUserRepository.ts">
<polygon fill="#dd1c1c" stroke="black" points="804.38,-6339.53 699.88,-6339.53 699.88,-6321.03 804.38,-6321.03 804.38,-6339.53"/>
<text text-anchor="start" x="707.88" y="-6326.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AppUserRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts -->
<g id="edge277" class="edge">
<title>src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.1,-6040C610.5,-6046.03 628.39,-6055.73 640,-6071.28 656.17,-6092.94 632.76,-6294.11 650.75,-6314.28 660.86,-6325.62 675.53,-6331.14 690.54,-6333.47"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="690.18,-6335.55 696.38,-6334.15 690.66,-6331.37 690.18,-6335.55"/>
</g>
<!-- src/server/domain/AppUser/AppUser.ts -->
<g id="node176" class="node">
<title>src/server/domain/AppUser/AppUser.ts</title>
<g id="a_node176"><a xlink:href="src/server/domain/AppUser/AppUser.ts" xlink:title="AppUser.ts">
<polygon fill="#dc6b0d" stroke="black" points="1027.38,-6668.53 967.12,-6668.53 967.12,-6650.03 1027.38,-6650.03 1027.38,-6668.53"/>
<text text-anchor="start" x="975.12" y="-6655.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AppUser.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/AppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge280" class="edge">
<title>src/server/application/ports/AppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M804.74,-6321.78C832.79,-6320.34 865.7,-6324.39 885.5,-6346.28 896.01,-6357.9 881.62,-6615.83 891.12,-6628.28 906.62,-6648.58 934.75,-6656.11 958.02,-6658.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="957.81,-6660.78 963.97,-6659.21 958.18,-6656.6 957.81,-6660.78"/>
</g>
<!-- src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts -->
<g id="node174" class="node">
<title>src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts</title>
<g id="a_node174"><a xlink:href="src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts" xlink:title="UpdateSinglePlayerStatusCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="840,-6100.53 664.25,-6100.53 664.25,-6082.03 840,-6082.03 840,-6100.53"/>
<text text-anchor="start" x="672.25" y="-6087.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UpdateSinglePlayerStatusCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts -->
<g id="node175" class="node">
<title>src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts</title>
<g id="a_node175"><a xlink:href="src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts" xlink:title="UpdateSinglePlayerStatusCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="624.25,-6100.53 416.25,-6100.53 416.25,-6082.03 624.25,-6082.03 624.25,-6100.53"/>
<text text-anchor="start" x="424.25" y="-6087.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UpdateSinglePlayerStatusCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts -->
<g id="edge279" class="edge">
<title>src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M541.45,-6100.82C569.11,-6115.08 617.89,-6144.81 640,-6186.28 653.43,-6211.47 631.33,-6293.36 650.75,-6314.28 661.04,-6325.36 675.68,-6330.82 690.62,-6333.17"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="690.2,-6335.24 696.41,-6333.86 690.7,-6331.07 690.2,-6335.24"/>
</g>
<!-- src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts -->
<g id="edge278" class="edge">
<title>src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M624.42,-6091.28C634.67,-6091.28 645.04,-6091.28 655.21,-6091.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="655.06,-6093.38 661.06,-6091.28 655.06,-6089.18 655.06,-6093.38"/>
</g>
<!-- src/server/domain/AppUser/valueObjects/AppUserId.ts -->
<g id="node184" class="node">
<title>src/server/domain/AppUser/valueObjects/AppUserId.ts</title>
<g id="a_node184"><a xlink:href="src/server/domain/AppUser/valueObjects/AppUserId.ts" xlink:title="AppUserId.ts">
<polygon fill="#dc6b0d" stroke="black" points="1238.62,-6667.53 1170.12,-6667.53 1170.12,-6649.03 1238.62,-6649.03 1238.62,-6667.53"/>
<text text-anchor="start" x="1178.12" y="-6654.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AppUserId.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/AppUser/AppUser.ts&#45;&gt;src/server/domain/AppUser/valueObjects/AppUserId.ts -->
<g id="edge286" class="edge">
<title>src/server/domain/AppUser/AppUser.ts&#45;&gt;src/server/domain/AppUser/valueObjects/AppUserId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1027.64,-6659.14C1062.58,-6658.97 1121.17,-6658.68 1161.01,-6658.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1160.94,-6660.59 1166.93,-6658.46 1160.92,-6656.39 1160.94,-6660.59"/>
</g>
<!-- src/server/application/ports/AuthenticationGateway.ts -->
<g id="node177" class="node">
<title>src/server/application/ports/AuthenticationGateway.ts</title>
<g id="a_node177"><a xlink:href="src/server/application/ports/AuthenticationGateway.ts" xlink:title="AuthenticationGateway.ts">
<polygon fill="#dd1c1c" stroke="black" points="811.5,-6432.53 692.75,-6432.53 692.75,-6414.03 811.5,-6414.03 811.5,-6432.53"/>
<text text-anchor="start" x="700.75" y="-6419.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AuthenticationGateway.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/AuthenticationGateway.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge281" class="edge">
<title>src/server/application/ports/AuthenticationGateway.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M811.67,-6414.62C838.01,-6414.22 867.23,-6419.28 885.5,-6439.28 899.67,-6454.79 878.3,-6611.64 891.12,-6628.28 906.72,-6648.5 934.85,-6656.04 958.09,-6658.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="957.87,-6660.73 964.03,-6659.16 958.23,-6656.54 957.87,-6660.73"/>
</g>
<!-- src/server/application/ports/CryptoPort.ts -->
<g id="node178" class="node">
<title>src/server/application/ports/CryptoPort.ts</title>
<g id="a_node178"><a xlink:href="src/server/application/ports/CryptoPort.ts" xlink:title="CryptoPort.ts">
<polygon fill="#dd1c1c" stroke="black" points="786.38,-6401.53 717.88,-6401.53 717.88,-6383.03 786.38,-6383.03 786.38,-6401.53"/>
<text text-anchor="start" x="725.88" y="-6388.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CryptoPort.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/IdentityProvider.ts -->
<g id="node179" class="node">
<title>src/server/application/ports/IdentityProvider.ts</title>
<g id="a_node179"><a xlink:href="src/server/application/ports/IdentityProvider.ts" xlink:title="IdentityProvider.ts">
<polygon fill="#dd1c1c" stroke="black" points="796.12,-6370.53 708.12,-6370.53 708.12,-6352.03 796.12,-6352.03 796.12,-6370.53"/>
<text text-anchor="start" x="716.12" y="-6357.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">IdentityProvider.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/MatchmakingQueue/MatchmakingQueue.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge287" class="edge">
<title>src/server/domain/MatchmakingQueue/MatchmakingQueue.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1051.34,-6589.28C1076.19,-6589.28 1106.14,-6589.28 1132.85,-6589.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1132.75,-6591.38 1138.75,-6589.28 1132.75,-6587.18 1132.75,-6591.38"/>
</g>
<!-- src/server/application/queries/loadDeckBuilderSettingsByGameId.ts -->
<g id="node181" class="node">
<title>src/server/application/queries/loadDeckBuilderSettingsByGameId.ts</title>
<g id="a_node181"><a xlink:href="src/server/application/queries/loadDeckBuilderSettingsByGameId.ts" xlink:title="loadDeckBuilderSettingsByGameId.ts">
<polygon fill="#dd1c1c" stroke="black" points="604.75,-6277.53 435.75,-6277.53 435.75,-6259.03 604.75,-6259.03 604.75,-6277.53"/>
<text text-anchor="start" x="443.75" y="-6264.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadDeckBuilderSettingsByGameId.ts</text>
</a>
</g>
</g>
<!-- src/server/application/queries/loadGameById.ts -->
<g id="node182" class="node">
<title>src/server/application/queries/loadGameById.ts</title>
<g id="a_node182"><a xlink:href="src/server/application/queries/loadGameById.ts" xlink:title="loadGameById.ts">
<polygon fill="#dd1c1c" stroke="black" points="563.12,-6308.53 477.38,-6308.53 477.38,-6290.03 563.12,-6290.03 563.12,-6308.53"/>
<text text-anchor="start" x="485.38" y="-6295.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadGameById.ts</text>
</a>
</g>
</g>
<!-- src/server/application/queries/loadGameSettingsByGameId.ts -->
<g id="node183" class="node">
<title>src/server/application/queries/loadGameSettingsByGameId.ts</title>
<g id="a_node183"><a xlink:href="src/server/application/queries/loadGameSettingsByGameId.ts" xlink:title="loadGameSettingsByGameId.ts">
<polygon fill="#dd1c1c" stroke="black" points="592,-6339.53 448.5,-6339.53 448.5,-6321.03 592,-6321.03 592,-6339.53"/>
<text text-anchor="start" x="456.5" y="-6326.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadGameSettingsByGameId.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/Deck/errors/DeckNotOwnedError.ts -->
<g id="node185" class="node">
<title>src/server/domain/Deck/errors/DeckNotOwnedError.ts</title>
<g id="a_node185"><a xlink:href="src/server/domain/Deck/errors/DeckNotOwnedError.ts" xlink:title="DeckNotOwnedError.ts">
<polygon fill="#dc6b0d" stroke="black" points="1051.75,-6484.53 942.75,-6484.53 942.75,-6466.03 1051.75,-6466.03 1051.75,-6484.53"/>
<text text-anchor="start" x="950.75" y="-6471.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">DeckNotOwnedError.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/User/errors/UserNotAuthenticatedError.ts -->
<g id="node186" class="node">
<title>src/server/domain/User/errors/UserNotAuthenticatedError.ts</title>
<g id="a_node186"><a xlink:href="src/server/domain/User/errors/UserNotAuthenticatedError.ts" xlink:title="UserNotAuthenticatedError.ts">
<polygon fill="#dc6b0d" stroke="black" points="1064.5,-6794.53 930,-6794.53 930,-6776.03 1064.5,-6776.03 1064.5,-6794.53"/>
<text text-anchor="start" x="938" y="-6781.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UserNotAuthenticatedError.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/User/errors/UserNotRegisteredError.ts -->
<g id="node187" class="node">
<title>src/server/domain/User/errors/UserNotRegisteredError.ts</title>
<g id="a_node187"><a xlink:href="src/server/domain/User/errors/UserNotRegisteredError.ts" xlink:title="UserNotRegisteredError.ts">
<polygon fill="#dc6b0d" stroke="black" points="1058.88,-6763.53 935.62,-6763.53 935.62,-6745.03 1058.88,-6745.03 1058.88,-6763.53"/>
<text text-anchor="start" x="943.62" y="-6750.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UserNotRegisteredError.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts -->
<g id="node188" class="node">
<title>src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts</title>
<g id="a_node188"><a xlink:href="src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts" xlink:title="UuidIdentityProvider.ts">
<polygon fill="#248cea" stroke="black" points="574,-6559.53 466.5,-6559.53 466.5,-6541.03 574,-6541.03 574,-6559.53"/>
<text text-anchor="start" x="474.5" y="-6546.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UuidIdentityProvider.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/application/ports/CryptoPort.ts -->
<g id="edge288" class="edge">
<title>src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/application/ports/CryptoPort.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M526.76,-6540.65C542.36,-6514.38 589.67,-6441.23 650.75,-6408.28 668.37,-6398.78 690.08,-6394.55 708.8,-6392.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="708.88,-6394.9 714.7,-6392.35 708.56,-6390.71 708.88,-6394.9"/>
</g>
<!-- src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/application/ports/IdentityProvider.ts -->
<g id="edge289" class="edge">
<title>src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/application/ports/IdentityProvider.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M525.19,-6540.77C538.12,-6510.92 582.34,-6418.58 650.75,-6377.28 665.03,-6368.66 682.44,-6364.23 698.73,-6362.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="698.89,-6364.15 704.63,-6361.41 698.43,-6359.98 698.89,-6364.15"/>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts -->
<g id="node189" class="node">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts</title>
<g id="a_node189"><a xlink:href="src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts" xlink:title="ConvexAuthenticationGateway.ts">
<polygon fill="#248cea" stroke="black" points="595.38,-6750.53 445.12,-6750.53 445.12,-6732.03 595.38,-6732.03 595.38,-6750.53"/>
<text text-anchor="start" x="453.12" y="-6737.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexAuthenticationGateway.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge291" class="edge">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M577.6,-6731.55C675.53,-6714.65 874.61,-6680.28 958.08,-6665.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="958.19,-6667.98 963.75,-6664.89 957.48,-6663.84 958.19,-6667.98"/>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/application/ports/AuthenticationGateway.ts -->
<g id="edge290" class="edge">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/application/ports/AuthenticationGateway.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M595.67,-6742.63C612.53,-6739.22 628.75,-6732.29 640,-6719.28 660.36,-6695.73 630.09,-6462.57 650.75,-6439.28 659.26,-6429.68 671.04,-6424.24 683.54,-6421.38"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="683.77,-6423.47 689.3,-6420.33 683.02,-6419.34 683.77,-6423.47"/>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/User/errors/UserNotAuthenticatedError.ts -->
<g id="edge292" class="edge">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/User/errors/UserNotAuthenticatedError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M595.73,-6748.18C684.33,-6756.39 831.43,-6770.01 920.56,-6778.27"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="920.35,-6780.36 926.52,-6778.82 920.74,-6776.18 920.35,-6780.36"/>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/User/errors/UserNotRegisteredError.ts -->
<g id="edge293" class="edge">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/User/errors/UserNotRegisteredError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M595.73,-6743.32C686.35,-6745.8 838.16,-6749.95 926.56,-6752.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="926.36,-6754.47 932.41,-6752.53 926.47,-6750.27 926.36,-6754.47"/>
</g>
<!-- src/server/infrastructure/gateways/Context/ConvexContext.ts -->
<g id="node190" class="node">
<title>src/server/infrastructure/gateways/Context/ConvexContext.ts</title>
<g id="a_node190"><a xlink:href="src/server/infrastructure/gateways/Context/ConvexContext.ts" xlink:title="ConvexContext.ts">
<polygon fill="#248cea" stroke="black" points="564.25,-6628.53 476.25,-6628.53 476.25,-6610.03 564.25,-6610.03 564.25,-6628.53"/>
<text text-anchor="start" x="484.25" y="-6615.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexContext.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/gateways/Context/ConvexContext.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge294" class="edge">
<title>src/server/infrastructure/gateways/Context/ConvexContext.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M564.59,-6620.46C590.7,-6618.63 622.23,-6611.42 640,-6589.28 665.53,-6557.47 623.82,-6252.92 650.75,-6222.28 666.31,-6204.57 692.97,-6200.99 714.9,-6201.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="714.53,-6203.69 720.63,-6201.92 714.75,-6199.5 714.53,-6203.69"/>
</g>
<!-- src/server/infrastructure/gateways/Time/RealTimeService.ts -->
<g id="node191" class="node">
<title>src/server/infrastructure/gateways/Time/RealTimeService.ts</title>
<g id="a_node191"><a xlink:href="src/server/infrastructure/gateways/Time/RealTimeService.ts" xlink:title="RealTimeService.ts">
<polygon fill="#248cea" stroke="black" points="568,-6689.53 472.5,-6689.53 472.5,-6671.03 568,-6671.03 568,-6689.53"/>
<text text-anchor="start" x="480.5" y="-6676.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">RealTimeService.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/gateways/Time/RealTimeService.ts&#45;&gt;src/server/application/ports/TimeService.ts -->
<g id="edge295" class="edge">
<title>src/server/infrastructure/gateways/Time/RealTimeService.ts&#45;&gt;src/server/application/ports/TimeService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M568.44,-6684.57C593.57,-6684.05 622.77,-6678.45 640,-6658.28 669.24,-6624.06 621.08,-6287.14 650.75,-6253.28 664.06,-6238.09 685.54,-6233.29 705.22,-6232.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="705.08,-6234.72 711.07,-6232.59 705.06,-6230.52 705.08,-6234.72"/>
</g>
<!-- src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts -->
<g id="node192" class="node">
<title>src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts</title>
<g id="a_node192"><a xlink:href="src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts" xlink:title="ConvexAppUserRepository.ts">
<polygon fill="#248cea" stroke="black" points="588.25,-7061.53 452.25,-7061.53 452.25,-7043.03 588.25,-7043.03 588.25,-7061.53"/>
<text text-anchor="start" x="460.25" y="-7048.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexAppUserRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts -->
<g id="edge296" class="edge">
<title>src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M588.44,-7055.09C607.89,-7052.3 627.28,-7045.33 640,-7030.28 664.53,-7001.25 625.86,-6375 650.75,-6346.28 660.75,-6334.74 675.46,-6329.18 690.54,-6326.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="690.71,-6328.96 696.43,-6326.2 690.23,-6324.79 690.71,-6328.96"/>
</g>
<!-- src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge297" class="edge">
<title>src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M588.74,-7046.57C606.08,-7043.3 624.27,-7038.21 640,-7030.28 773.24,-6963.12 820.34,-6935.51 885.5,-6801.28 898.55,-6774.4 876.34,-6760.25 891.12,-6734.28 907.47,-6705.57 939.82,-6685.06 964.34,-6672.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="965.1,-6674.8 969.6,-6670.31 963.28,-6671.01 965.1,-6674.8"/>
</g>
<!-- src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts -->
<g id="node193" class="node">
<title>src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts</title>
<g id="a_node193"><a xlink:href="src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts" xlink:title="ConvexDeckRepository.ts">
<polygon fill="#248cea" stroke="black" points="580.75,-7122.53 459.75,-7122.53 459.75,-7104.03 580.75,-7104.03 580.75,-7122.53"/>
<text text-anchor="start" x="467.75" y="-7109.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexDeckRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/application/ports/DeckRepository.ts -->
<g id="edge298" class="edge">
<title>src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/application/ports/DeckRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M580.96,-7116.62C602.64,-7114.54 625.38,-7107.88 640,-7091.28 653.98,-7075.41 647.83,-6922.22 650.75,-6901.28 674.34,-6732.29 728.88,-6532.63 745.93,-6472.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="747.89,-6473.18 747.51,-6466.83 743.85,-6472.03 747.89,-6473.18"/>
</g>
<!-- src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts -->
<g id="edge299" class="edge">
<title>src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M580.95,-7114.59C601.85,-7112.1 624.07,-7105.7 640,-7091.28 652.7,-7079.79 643.34,-7069.72 650.75,-7054.28 729.29,-6890.72 827.58,-6892.23 885.5,-6720.28 890.86,-6704.37 881.4,-6582.96 891.12,-6569.28 907.13,-6546.77 937.39,-6536.62 961.35,-6532.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.63,-6534.12 967.2,-6531.05 960.93,-6529.98 961.63,-6534.12"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts -->
<g id="node194" class="node">
<title>src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts</title>
<g id="a_node194"><a xlink:href="src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts" xlink:title="InMemoryAppUserRepository.ts">
<polygon fill="#248cea" stroke="black" points="592.75,-6969.53 447.75,-6969.53 447.75,-6951.03 592.75,-6951.03 592.75,-6969.53"/>
<text text-anchor="start" x="455.75" y="-6956.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">InMemoryAppUserRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts -->
<g id="edge300" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M593.06,-6966.16C610.8,-6963.83 628.1,-6957.64 640,-6944.28 662.09,-6919.46 628.96,-6371.36 650.75,-6346.28 660.76,-6334.76 675.47,-6329.19 690.56,-6326.88"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="690.72,-6328.98 696.45,-6326.21 690.25,-6324.8 690.72,-6328.98"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge301" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.98,-6965.08C610.4,-6962.67 627.56,-6956.7 640,-6944.28 665.07,-6919.24 631.21,-6894.84 650.75,-6865.28 655.92,-6857.47 895.27,-6717.96 972.5,-6673.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="973.12,-6675.14 977.25,-6670.31 971.01,-6671.51 973.12,-6675.14"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts -->
<g id="node195" class="node">
<title>src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts</title>
<g id="a_node195"><a xlink:href="src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts" xlink:title="InMemoryDeckRepository.ts">
<polygon fill="#248cea" stroke="black" points="585.25,-6938.53 455.25,-6938.53 455.25,-6920.03 585.25,-6920.03 585.25,-6938.53"/>
<text text-anchor="start" x="463.25" y="-6925.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">InMemoryDeckRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts&#45;&gt;src/server/application/ports/DeckRepository.ts -->
<g id="edge302" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts&#45;&gt;src/server/application/ports/DeckRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M585.62,-6935.61C605.74,-6934.06 626.24,-6928.18 640,-6913.28 655.02,-6897.01 646.11,-6735.93 650.75,-6714.28 671.16,-6619.08 721.51,-6512.89 742.11,-6471.82"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="743.94,-6472.86 744.78,-6466.56 740.19,-6470.96 743.94,-6472.86"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts -->
<g id="edge303" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M585.58,-6935.57C605.69,-6934.01 626.2,-6928.15 640,-6913.28 667.26,-6883.91 623.69,-6762.84 650.75,-6733.28 721.97,-6655.47 814.9,-6776.66 885.5,-6698.28 895.1,-6687.62 882.77,-6580.95 891.12,-6569.28 907.2,-6546.82 937.46,-6536.67 961.39,-6532.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.67,-6534.16 967.23,-6531.08 960.97,-6530.02 961.67,-6534.16"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts -->
<g id="node196" class="node">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts</title>
<g id="a_node196"><a xlink:href="src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts" xlink:title="InMemoryMatchRepository.ts">
<polygon fill="#248cea" stroke="black" points="587.12,-6907.53 453.38,-6907.53 453.38,-6889.03 587.12,-6889.03 587.12,-6907.53"/>
<text text-anchor="start" x="461.38" y="-6894.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">InMemoryMatchRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge304" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M587.49,-6901.2C607.24,-6898.5 627.06,-6891.56 640,-6876.28 661.26,-6851.18 629.17,-6309.11 650.75,-6284.28 662.04,-6271.29 679.29,-6265.88 696.32,-6264.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="696.31,-6266.26 702.15,-6263.76 696.03,-6262.07 696.31,-6266.26"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts -->
<g id="edge305" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M587.42,-6900.83C607.05,-6898.08 626.8,-6891.2 640,-6876.28 666.11,-6846.76 628.21,-6731.6 650.75,-6699.28 715.93,-6605.83 821.3,-6690.41 885.5,-6596.28 894.33,-6583.33 882.44,-6468.33 891.12,-6455.28 906.92,-6431.54 937.41,-6419.07 961.5,-6412.67"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.78,-6414.76 967.11,-6411.3 960.78,-6410.68 961.78,-6414.76"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts -->
<g id="node197" class="node">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts</title>
<g id="a_node197"><a xlink:href="src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts" xlink:title="InMemoryMatchmakingQueueRepository.ts">
<polygon fill="#248cea" stroke="black" points="616,-7000.53 424.5,-7000.53 424.5,-6982.03 616,-6982.03 616,-7000.53"/>
<text text-anchor="start" x="432.5" y="-6987.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">InMemoryMatchmakingQueueRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge306" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M616.47,-6991.31C625.39,-6987.75 633.5,-6982.59 640,-6975.28 664.36,-6947.87 626.72,-6342.98 650.75,-6315.28 655.65,-6309.63 661.69,-6305.41 668.34,-6302.3"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="668.76,-6304.39 673.59,-6300.25 667.23,-6300.48 668.76,-6304.39"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge308" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M616.28,-6995.09C748.07,-6994.57 982.77,-6971.43 1095.38,-6824.28 1120.53,-6791.4 1083.04,-6672.89 1105.75,-6638.28 1117.12,-6620.96 1136.57,-6609.49 1155.13,-6602.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1155.84,-6604.02 1160.72,-6599.96 1154.37,-6600.09 1155.84,-6604.02"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="edge307" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M616.44,-6990.91C625.3,-6987.41 633.42,-6982.37 640,-6975.28 666.82,-6946.41 626.74,-6829.52 650.75,-6798.28 717.75,-6711.11 818.42,-6807.39 885.5,-6720.28 897.73,-6704.4 879.31,-6646.46 891.12,-6630.28 901.7,-6615.8 918.15,-6606.43 934.92,-6600.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="935.24,-6602.48 940.28,-6598.61 933.93,-6598.49 935.24,-6602.48"/>
</g>
<!-- src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts -->
<g id="node198" class="node">
<title>src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts</title>
<g id="a_node198"><a xlink:href="src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts" xlink:title="ConvexMatchRepository.ts">
<polygon fill="#248cea" stroke="black" points="582.62,-6846.53 457.88,-6846.53 457.88,-6828.03 582.62,-6828.03 582.62,-6846.53"/>
<text text-anchor="start" x="465.88" y="-6833.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexMatchRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge309" class="edge">
<title>src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M567.53,-6827.61C593.52,-6819.58 623.9,-6805.27 640,-6780.28 654.93,-6757.11 632.63,-6305.05 650.75,-6284.28 662.06,-6271.31 679.31,-6265.9 696.35,-6264.17"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="696.33,-6266.28 702.18,-6263.79 696.05,-6262.09 696.33,-6266.28"/>
</g>
<!-- src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts -->
<g id="edge310" class="edge">
<title>src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M564.89,-6827.58C590.94,-6819.43 622.3,-6805.02 640,-6780.28 663.44,-6747.52 631.59,-6725.72 650.75,-6690.28 715.54,-6570.47 819.03,-6612.17 885.5,-6493.28 893.83,-6478.38 880.92,-6468.97 891.12,-6455.28 907.99,-6432.67 937.88,-6420.13 961.49,-6413.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="962,-6415.45 967.26,-6411.89 960.93,-6411.39 962,-6415.45"/>
</g>
<!-- src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts -->
<g id="node199" class="node">
<title>src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts</title>
<g id="a_node199"><a xlink:href="src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts" xlink:title="ConvexMatchmakingQueueRepository.ts">
<polygon fill="#248cea" stroke="black" points="611.5,-7183.53 429,-7183.53 429,-7165.03 611.5,-7165.03 611.5,-7183.53"/>
<text text-anchor="start" x="437" y="-7170.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexMatchmakingQueueRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge311" class="edge">
<title>src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M611.88,-7171.31C622.67,-7167.25 632.5,-7161.18 640,-7152.28 669.97,-7116.72 620.34,-6350.46 650.75,-6315.28 655.64,-6309.62 661.67,-6305.39 668.32,-6302.29"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="668.74,-6304.38 673.56,-6300.23 667.21,-6300.47 668.74,-6304.38"/>
</g>
<!-- src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge313" class="edge">
<title>src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M589.68,-7164.58C716.31,-7142.5 984.38,-7075.48 1095.38,-6893.28 1110.13,-6869.06 1090.37,-6662.11 1105.75,-6638.28 1116.9,-6621.01 1136.11,-6609.58 1154.55,-6602.14"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1155.23,-6604.13 1160.11,-6600.06 1153.75,-6600.2 1155.23,-6604.13"/>
</g>
<!-- src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="edge312" class="edge">
<title>src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M611.84,-7167.61C622.05,-7164.02 631.74,-7159.07 640,-7152.28 649.98,-7144.08 643.64,-7136.06 650.75,-7125.28 731.85,-7002.3 827.55,-7026.72 885.5,-6891.28 891.2,-6877.95 882.81,-6642.17 891.12,-6630.28 901.45,-6615.52 917.92,-6606.07 934.79,-6600.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="935.14,-6602.12 940.2,-6598.26 933.84,-6598.13 935.14,-6602.12"/>
</g>
</g>
</svg>
