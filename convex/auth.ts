import {convexAuth} from "@convex-dev/auth/server";
import GitHub from "@auth/core/providers/github";
import {MutationCtx} from "@/convex/_generated/server";
import {UuidIdentityProvider} from "@/src/Shared/server/infrastructure/IdentityProvider/UuidIdentityProvider";
import {getOneFrom} from "convex-helpers/server/relationships";

export const {auth, signIn, signOut, store, isAuthenticated} = convexAuth({
  providers: [GitHub],
  callbacks: {
    afterUserCreatedOrUpdated: async (ctx: MutationCtx, {userId}) => {
      if (userId === null) {
        return;
      }

      const appUser = await getOneFrom(ctx.db, 'appUsers', 'by_convexUserId', userId);
      const user = await ctx.db.get(userId);

      if (appUser === null) {
        const identityProvider = new UuidIdentityProvider(crypto);
        const appUserId = identityProvider.generateId();

        await ctx.db.insert('appUsers', {
          convexUserId: userId,
          appUserId,
          name: user?.name || `Anonymous-${appUserId}`,
          createdAt: Date.now(),
          avatar: user?.image || `https://api.dicebear.com/9.x/fun-emoji/svg?seed=${appUserId}`,
          email: user?.email || '',
          status: 'idle',
          active: true,
        });
      } else {
        await ctx.db.patch(appUser._id, {
          name: user?.name || appUser.name,
          avatar: user?.image || appUser.avatar,
          email: user?.email || appUser.email || '',
        });
      }
    },
  },
});
