import {MutationCtx} from "../../_generated/server";
import {Id} from "../../_generated/dataModel";
import {UpdatePlayersStatusCommandHandler} from "@/src/MatchMaking/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler";
import {ConvexAppUserRepository} from "@/src/Authentication/server/infrastructure/repositories/AppUser/ConvexAppUserRepository";

export async function matchEndedSaga(
  ctx: MutationCtx,
  payload: {
    winner: string;
    loser: string;
    matchId: string;
  }
) {
  const updateStatus = new UpdatePlayersStatusCommandHandler(
    new ConvexAppUserRepository(ctx)
  );
  await updateStatus.handle({players: [payload.winner, payload.loser], status: "idle"});
}
