import {MutationCtx} from "../../_generated/server";
import {Id} from "../../_generated/dataModel";
import {MakeMatchCommandHandler} from "@/src/MatchMaking/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler";
import {UpdatePlayersStatusCommandHandler} from "@/src/MatchMaking/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler";
import {ConvexMatchmakingQueueRepository} from "@/src/MatchMaking/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository";
import {ConvexMatchRepository} from "@/src/Gaming/server/infrastructure/repositories/Match/ConvexMatchRepository";
import {ConvexAppUserRepository} from "@/src/Authentication/server/infrastructure/repositories/AppUser/ConvexAppUserRepository";
import {ConvexContext} from "@/src/Shared/server/infrastructure/gateways/Context/ConvexContext";

export async function playerAddedToMatchMakingQueueSaga(
  ctx: MutationCtx,
  payload: {
    gameId: string;
    playerId: string;
    queueId: string;
  }
) {
  const context = new ConvexContext(ctx);
  const makeMatchCommandHandler = new MakeMatchCommandHandler(
    context,
    new ConvexMatchmakingQueueRepository(ctx),
    new ConvexMatchRepository(ctx)
  );
  const updatePlayersStatusCommandHandler = new UpdatePlayersStatusCommandHandler(
    new ConvexAppUserRepository(ctx)
  );

  await Promise.all([
    makeMatchCommandHandler.handle({ gameId: payload.gameId as string,  playerId: payload.playerId,  queueId: payload.queueId as string}),
    updatePlayersStatusCommandHandler.handle({players: [payload.playerId], status: "waiting-for-opponent"}),
  ]);
}
