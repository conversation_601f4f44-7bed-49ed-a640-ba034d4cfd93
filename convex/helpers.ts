import {customMutation, customQuery} from "convex-helpers/server/customFunctions";
import {mutation, query} from "./_generated/server";
import {
  ConvexAuthenticationGateway
} from "@/src/Authentication/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway";

export const protectedQuery = customQuery(query, {
  args: {},
  input: async (ctx, args) => {
    const authenticationGateway = new ConvexAuthenticationGateway(ctx);
    const user = await authenticationGateway.getAuthenticatedUser();
    return {ctx: {...ctx, userId: user.id()}, args};
  },
});

export const protectedMutation = customMutation(mutation, {
  args: {},
  input: async (ctx, args) => {
    const authenticationGateway = new ConvexAuthenticationGateway(ctx);
    const user = await authenticationGateway.getAuthenticatedUser();
    return {ctx: {...ctx, userId: user.id()}, args};
  },
});
