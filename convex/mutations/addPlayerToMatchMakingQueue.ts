import {v} from "convex/values";
import {protectedMutation} from "@/convex/helpers";
import {AddPlayerToMatchMakingQueueCommandHandler} from "@/src/MatchMaking/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler";
import {ConvexMatchmakingQueueRepository} from "@/src/MatchMaking/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository";
import {ConvexContext} from "@/src/Shared/server/infrastructure/gateways/Context/ConvexContext";
import {RealTimeService} from "@/src/Shared/server/infrastructure/gateways/Time/RealTimeService";

export const endpoint = protectedMutation({
  args: {
    gameId: v.string(),
    deckId: v.string(),
  },
  handler: async (ctx, {gameId, deckId}) => {
    const repository = new ConvexMatchmakingQueueRepository(ctx);
    const commandHandler = new AddPlayerToMatchMakingQueueCommandHandler(
      new ConvexContext(ctx),
      repository,
      new RealTimeService()
    );
    return commandHandler.handle({gameId: gameId, deckId, userId: ctx.userId});
  },
});
