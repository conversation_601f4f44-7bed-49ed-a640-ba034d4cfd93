import {v} from "convex/values";
import {protectedMutation} from "@/convex/helpers";
import {CancelMatchRegistrationCommandHandler} from "@/src/MatchMaking/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler";
import {ConvexMatchmakingQueueRepository} from "@/src/MatchMaking/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository";
import {ConvexContext} from "@/src/Shared/server/infrastructure/gateways/Context/ConvexContext";

export const endpoint = protectedMutation({
  args: {
    gameId: v.string(),
  },
  handler: async (ctx, {gameId}) => {
    const repository = new ConvexMatchmakingQueueRepository(ctx);
    const handler = new CancelMatchRegistrationCommandHandler(new ConvexContext(ctx), repository);
    await handler.handle({gameId, userId: ctx.userId});
  },
});
