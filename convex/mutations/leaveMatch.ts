import {v} from "convex/values";
import {protectedMutation} from "@/convex/helpers";
import {LeaveMatchCommandHandler} from "@/src/Gaming/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler";
import {ConvexMatchRepository} from "@/src/Gaming/server/infrastructure/repositories/Match/ConvexMatchRepository";
import {ConvexContext} from "@/src/Shared/server/infrastructure/gateways/Context/ConvexContext";

export const endpoint = protectedMutation({
  args: {
    matchId: v.id("matches"),
  },
  handler: async (ctx, {matchId}) => {
    const repository = new ConvexMatchRepository(ctx);
    const handler = new LeaveMatchCommandHandler(new ConvexContext(ctx), repository);
    await handler.handle({matchId: matchId as string, userId: ctx.userId});
  },
});
