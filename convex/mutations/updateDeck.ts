import {v} from "convex/values";
import {protectedMutation} from "@/convex/helpers";
import {DeckNotOwnedError} from "@/src/DeckBuilding/server/domain/Deck/errors/DeckNotOwnedError";

export const endpoint = protectedMutation({
  args: {
    deckId: v.id("decks"),
    name: v.string(),
    cards: v.array(v.object({cardId: v.string(), quantity: v.number()})),
  },
  handler: async (ctx, {deckId, name, cards}) => {
    const deck = await ctx.db.get(deckId);
    if (!deck) return;
    if (deck.playerId !== ctx.userId) {
      throw new DeckNotOwnedError();
    }
    await ctx.db.patch(deckId, {name, cards});
  },
});
