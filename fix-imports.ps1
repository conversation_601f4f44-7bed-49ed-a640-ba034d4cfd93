# Script PowerShell pour corriger automatiquement les imports
# Mapping des anciens chemins vers les nouveaux bounded contexts

$replacements = @{
    # DeckBuilding
    '@/src/client/domain/DeckBuilder/' = '@/src/DeckBuilding/client/domain/DeckBuilder/'
    '@/src/client/domain/Catalog/' = '@/src/DeckBuilding/client/domain/Catalog/'
    '@/src/client/domain/CardData/' = '@/src/DeckBuilding/client/domain/CardData/'
    '@/src/client/domain/GameSettings/' = '@/src/DeckBuilding/client/domain/GameSettings/'
    '@/src/server/domain/Deck/' = '@/src/DeckBuilding/server/domain/Deck/'
    '@/src/server/application/commands/Deck/' = '@/src/DeckBuilding/server/application/commands/Deck/'
    '@/src/server/application/ports/DeckRepository' = '@/src/DeckBuilding/server/application/ports/DeckRepository'
    '@/src/server/application/queries/loadDeckBuilderSettingsByGameId' = '@/src/DeckBuilding/server/application/queries/loadDeckBuilderSettingsByGameId'
    '@/src/server/application/queries/loadGameSettingsByGameId' = '@/src/DeckBuilding/server/application/queries/loadGameSettingsByGameId'
    '@/src/server/infrastructure/repositories/Deck/' = '@/src/DeckBuilding/server/infrastructure/repositories/Deck/'
    '@/src/server/specs/helpers/createDeckBuilderSettings' = '@/src/DeckBuilding/server/specs/helpers/createDeckBuilderSettings'
    '@/src/server/specs/helpers/createFakeDeck' = '@/src/DeckBuilding/server/specs/helpers/createFakeDeck'
    '@/src/server/specs/helpers/createFakeGameSettings' = '@/src/DeckBuilding/server/specs/helpers/createFakeGameSettings'
    '@/src/server/specs/helpers/dtos/deckDTO' = '@/src/DeckBuilding/server/specs/helpers/dtos/deckDTO'
    '@/src/server/specs/helpers/dtos/GameSettingsDTO' = '@/src/DeckBuilding/server/specs/helpers/dtos/GameSettingsDTO'
    '@/src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository' = '@/src/DeckBuilding/server/infrastructure/repositories/InMemoryDeckRepository'
    '@/src/client/application/commands/addCardToDeck/' = '@/src/DeckBuilding/client/application/commands/addCardToDeck/'
    '@/src/client/application/commands/removeCardFromDeck/' = '@/src/DeckBuilding/client/application/commands/removeCardFromDeck/'
    '@/src/client/application/commands/showCardDetails/' = '@/src/DeckBuilding/client/application/commands/showCardDetails/'
    '@/src/client/application/commands/hideCardDetails/' = '@/src/DeckBuilding/client/application/commands/hideCardDetails/'
    '@/src/client/application/commands/clearDeckDraft/' = '@/src/DeckBuilding/client/application/commands/clearDeckDraft/'
    '@/src/client/application/commands/filterCatalog/' = '@/src/DeckBuilding/client/application/commands/filterCatalog/'
    '@/src/client/application/commands/loadCatalogCards/' = '@/src/DeckBuilding/client/application/commands/loadCatalogCards/'
    '@/src/client/application/commands/loadDeckDraft/' = '@/src/DeckBuilding/client/application/commands/loadDeckDraft/'
    '@/src/client/application/commands/loadDeckIntoBuilder/' = '@/src/DeckBuilding/client/application/commands/loadDeckIntoBuilder/'
    '@/src/client/application/commands/saveDeckDraft/' = '@/src/DeckBuilding/client/application/commands/saveDeckDraft/'
    '@/src/client/application/commands/search/' = '@/src/DeckBuilding/client/application/commands/search/'
    '@/src/client/application/commands/switchDeckBuilderView/' = '@/src/DeckBuilding/client/application/commands/switchDeckBuilderView/'
    '@/src/client/application/commands/updateAvailableFilters/' = '@/src/DeckBuilding/client/application/commands/updateAvailableFilters/'
    '@/src/client/application/commands/initializeDeckBuilderFromLocation/' = '@/src/DeckBuilding/client/application/commands/initializeDeckBuilderFromLocation/'
    '@/src/client/application/commands/loadGameSettings/' = '@/src/DeckBuilding/client/application/commands/loadGameSettings/'
    '@/src/client/application/queries/' = '@/src/DeckBuilding/client/application/queries/'
    '@/src/client/infrastructure/components/app/DeckBuilding/' = '@/src/DeckBuilding/client/infrastructure/components/app/DeckBuilding/'
    '@/src/client/infrastructure/hooks/useDeckBuilder/' = '@/src/DeckBuilding/client/infrastructure/hooks/useDeckBuilder/'
    '@/src/client/infrastructure/hooks/useCatalogCardsByGameId/' = '@/src/DeckBuilding/client/infrastructure/hooks/useCatalogCardsByGameId/'
    '@/src/client/infrastructure/hooks/useDeckById/' = '@/src/DeckBuilding/client/infrastructure/hooks/useDeckById/'
    '@/src/client/infrastructure/hooks/useDeckId/' = '@/src/DeckBuilding/client/infrastructure/hooks/useDeckId/'
    '@/src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/' = '@/src/DeckBuilding/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/'
    '@/src/client/infrastructure/hooks/useGameSettingsByGameId/' = '@/src/DeckBuilding/client/infrastructure/hooks/useGameSettingsByGameId/'
    '@/src/client/application/services/DeckDraftService/' = '@/src/DeckBuilding/client/application/services/DeckDraftService/'
    '@/src/client/specs/helpers/factories/fakeCatalogCards' = '@/src/DeckBuilding/client/specs/helpers/factories/fakeCatalogCards'
    '@/src/client/specs/helpers/fakes/FakeDeckDraftService' = '@/src/DeckBuilding/client/specs/helpers/fakes/FakeDeckDraftService'
    
    # Authentication
    '@/src/client/infrastructure/components/app/Auth/' = '@/src/Authentication/client/infrastructure/components/app/Auth/'
    '@/src/client/infrastructure/pages/Auth/' = '@/src/Authentication/client/infrastructure/pages/Auth/'
    '@/src/server/domain/User/' = '@/src/Authentication/server/domain/User/'
    '@/src/server/domain/AppUser/' = '@/src/Authentication/server/domain/AppUser/'
    '@/src/server/application/ports/AppUserRepository' = '@/src/Authentication/server/application/ports/AppUserRepository'
    '@/src/server/application/ports/AuthenticationGateway' = '@/src/Authentication/server/application/ports/AuthenticationGateway'
    '@/src/server/infrastructure/gateways/AuthenticationGateway/' = '@/src/Authentication/server/infrastructure/gateways/AuthenticationGateway/'
    '@/src/server/infrastructure/repositories/AppUser/' = '@/src/Authentication/server/infrastructure/repositories/AppUser/'
    
    # Gaming
    '@/src/client/infrastructure/components/app/Gaming/' = '@/src/Gaming/client/infrastructure/components/app/Gaming/'
    '@/src/client/infrastructure/pages/Gaming/' = '@/src/Gaming/client/infrastructure/pages/Gaming/'
    '@/src/server/domain/Match/' = '@/src/Gaming/server/domain/Match/'
    '@/src/server/application/commands/Match/' = '@/src/Gaming/server/application/commands/Match/'
    '@/src/server/application/ports/MatchRepository' = '@/src/Gaming/server/application/ports/MatchRepository'
    '@/src/server/infrastructure/repositories/Match/' = '@/src/Gaming/server/infrastructure/repositories/Match/'
    
    # MatchMaking
    '@/src/server/domain/MatchmakingQueue/' = '@/src/MatchMaking/server/domain/MatchmakingQueue/'
    '@/src/server/application/commands/MatchMaking/' = '@/src/MatchMaking/server/application/commands/MatchMaking/'
    '@/src/server/application/ports/MatchmakingQueueRepository' = '@/src/MatchMaking/server/application/ports/MatchmakingQueueRepository'
    '@/src/server/infrastructure/repositories/MatchmakingQueue/' = '@/src/MatchMaking/server/infrastructure/repositories/MatchmakingQueue/'
    '@/src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository' = '@/src/MatchMaking/server/infrastructure/repositories/InMemoryMatchmakingQueueRepository'
    '@/src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository' = '@/src/Gaming/server/infrastructure/repositories/InMemoryMatchRepository'
    '@/src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository' = '@/src/Authentication/server/infrastructure/repositories/InMemoryAppUserRepository'
    
    # Shared - Plus de patterns spécifiques
    '@/src/client/application/store/appStore' = '@/src/Shared/client/application/store/appStore'
    '@/src/client/application/services/LocationService' = '@/src/Shared/client/application/services/LocationService'
    '@/src/client/infrastructure/components/ui/' = '@/src/Shared/client/infrastructure/components/'
    '@/src/client/infrastructure/components/redirections/' = '@/src/Shared/client/infrastructure/components/redirections/'
    '@/src/client/infrastructure/components/debug/JsonObjectViewer/' = '@/src/Shared/client/infrastructure/components/JsonObjectViewer/'
    '@/src/client/infrastructure/hooks/useDebounce' = '@/src/Shared/client/infrastructure/hooks/useDebounce/useDebounce'
    '@/src/client/infrastructure/hooks/useGameId' = '@/src/Shared/client/infrastructure/hooks/useGameId/useGameId'
    '@/src/client/infrastructure/hooks/useLocale' = '@/src/Shared/client/infrastructure/hooks/useLocale/useLocale'
    '@/src/client/infrastructure/hooks/useResponsiveColumnCount' = '@/src/Shared/client/infrastructure/hooks/useResponsiveColumnCount'
    '@/src/client/infrastructure/layouts/' = '@/src/Shared/client/infrastructure/layouts/'
    '@/src/client/infrastructure/lib/' = '@/src/Shared/client/infrastructure/lib/'
    '@/src/client/infrastructure/pages/Catalog/' = '@/src/Shared/client/infrastructure/pages/Catalog/'
    '@/src/client/infrastructure/providers/' = '@/src/Shared/client/infrastructure/providers/'
    '@/src/client/infrastructure/services/location/' = '@/src/Shared/client/infrastructure/services/location/'
    '@/src/client/infrastructure/store/store' = '@/src/Shared/client/infrastructure/store/store'
    '@/src/client/infrastructure/builders/urlBuilder' = '@/src/Shared/client/infrastructure/builders/urlBuilder'
    '@/src/client/specs/helpers/store/' = '@/src/Shared/client/specs/helpers/store/'
    '@/src/client/specs/helpers/utils/' = '@/src/Shared/client/specs/helpers/utils/'
    '@/src/client/specs/helpers/builders/' = '@/src/Shared/client/specs/helpers/builders/'
    '@/src/client/specs/helpers/FakeLocationService' = '@/src/Shared/client/specs/helpers/FakeLocationService'
    '@/src/client/specs/helpers/fakes/FakeLocationService' = '@/src/Shared/client/specs/helpers/FakeLocationService'
    '@/src/client/infrastructure/components/debug/MatchConsoleEvents/' = '@/src/Gaming/client/infrastructure/components/debug/MatchConsoleEvents/'
    '@/src/server/application/queries/loadMatchById' = '@/src/Gaming/server/application/queries/loadMatchById'
    '@/src/client/infrastructure/components/app/Catalog/' = '@/src/Shared/client/infrastructure/components/Catalog/'
    '@/src/client/infrastructure/services/deckDraft/' = '@/src/DeckBuilding/client/infrastructure/services/deckDraft/'
    '@/src/client/application/subscribers/subscribeToDeckDraft' = '@/src/DeckBuilding/client/application/subscribers/subscribeToDeckDraft'
    '@/src/server/application/ports/Context' = '@/src/Shared/server/application/ports/Context'
    '@/src/server/application/ports/CryptoPort' = '@/src/Shared/server/application/ports/CryptoPort'
    '@/src/server/application/ports/IdentityProvider' = '@/src/Shared/server/application/ports/IdentityProvider'
    '@/src/server/application/ports/TimeService' = '@/src/Shared/server/application/ports/TimeService'
    '@/src/server/application/queries/loadGameById' = '@/src/Shared/server/application/queries/loadGameById'
    '@/src/server/application/queries/loadGameList' = '@/src/Shared/server/application/queries/loadGameList'
    '@/src/server/infrastructure/gateways/Context/' = '@/src/Shared/server/infrastructure/gateways/Context/'
    '@/src/server/infrastructure/gateways/Time/' = '@/src/Shared/server/infrastructure/gateways/Time/'
    '@/src/server/infrastructure/IdentityProvider/' = '@/src/Shared/server/infrastructure/IdentityProvider/'
    '@/src/server/specs/helpers/' = '@/src/Shared/server/specs/helpers/'
}

# Fonction pour remplacer les imports dans un fichier
function Replace-ImportsInFile {
    param(
        [string]$FilePath
    )
    
    $content = Get-Content $FilePath -Raw
    $modified = $false
    
    foreach ($oldPath in $replacements.Keys) {
        $newPath = $replacements[$oldPath]
        if ($content -match [regex]::Escape($oldPath)) {
            $content = $content -replace [regex]::Escape($oldPath), $newPath
            $modified = $true
        }
    }
    
    if ($modified) {
        Set-Content $FilePath $content -NoNewline
        Write-Host "Updated: $FilePath"
    }
}

# Traiter tous les fichiers TypeScript/TSX dans src/
Get-ChildItem -Path "src" -Recurse -Include "*.ts","*.tsx" | ForEach-Object {
    Replace-ImportsInFile $_.FullName
}

Write-Host "Import replacement completed!"
