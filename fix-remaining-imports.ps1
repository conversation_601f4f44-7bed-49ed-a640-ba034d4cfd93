# Script PowerShell pour corriger les imports restants
$replacements = @{
    # Corrections spécifiques pour les helpers de tests
    '@/src/Shared/server/specs/helpers/createAppUsers' = '@/src/Authentication/server/specs/helpers/createAppUsers'
    '@/src/Shared/server/specs/helpers/createFakeDeck' = '@/src/DeckBuilding/server/specs/helpers/createFakeDeck'
    '@/src/Shared/server/specs/helpers/createDeckBuilderSettings' = '@/src/DeckBuilding/server/specs/helpers/createDeckBuilderSettings'
    '@/src/Shared/server/specs/helpers/createFakeGameSettings' = '@/src/DeckBuilding/server/specs/helpers/createFakeGameSettings'
    '@/src/Shared/server/specs/helpers/getAppUser' = '@/src/Authentication/server/specs/helpers/getAppUser'
    '@/src/Shared/server/specs/helpers/fakes/fakeUsers' = '@/src/Authentication/server/specs/helpers/fakes/fakeUsers'
    '@/src/Shared/server/specs/helpers/fakes/fakeGames' = '@/src/Shared/server/specs/helpers/fakes/fakeGames'
    
    # Corrections pour les imports relatifs vers les fakes
    '../../../helpers/fakes/fakeUsers' = '@/src/Authentication/server/specs/helpers/fakes/fakeUsers'
    '../../../helpers/fakes/fakeGames' = '@/src/Shared/server/specs/helpers/fakes/fakeGames'
    
    # Corrections pour les services DeckDraft
    '../services/DeckDraftService/DeckDraftService' = '@/src/DeckBuilding/client/application/services/DeckDraftService/DeckDraftService'
    
    # Corrections pour StartGameButton
    '@/src/Gaming/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton' = '@/src/MatchMaking/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton'
}

# Fonction pour remplacer les imports dans un fichier
function Replace-ImportsInFile {
    param(
        [string]$FilePath
    )
    
    $content = Get-Content $FilePath -Raw
    $modified = $false
    
    foreach ($oldPath in $replacements.Keys) {
        $newPath = $replacements[$oldPath]
        if ($content -match [regex]::Escape($oldPath)) {
            $content = $content -replace [regex]::Escape($oldPath), $newPath
            $modified = $true
        }
    }
    
    if ($modified) {
        Set-Content $FilePath $content -NoNewline
        Write-Host "Updated: $FilePath"
    }
}

# Traiter tous les fichiers TypeScript/TSX
Get-ChildItem -Path "." -Recurse -Include "*.ts","*.tsx" | ForEach-Object {
    Replace-ImportsInFile $_.FullName
}

Write-Host "Remaining import fixes completed!"
