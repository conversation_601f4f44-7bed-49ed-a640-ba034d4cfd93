import {AppUserRepository} from "@/src/Authentication/server/application/ports/AppUserRepository";
import {AppUser} from "@/src/Authentication/server/domain/AppUser/AppUser";

export class InMemoryAppUserRepository implements AppUserRepository {
  private users = new Map<string, AppUser>();

  add(user: AppUser) {
    this.users.set(user.id(), user);
  }

  async findById(appUserId: string): Promise<AppUser | null> {
    return this.users.get(appUserId) || null;
  }

  async save(user: AppUser) {
    this.users.set(user.id(), user);
  }
}
