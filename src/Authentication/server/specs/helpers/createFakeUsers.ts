import {TestConvexForDataModel} from "convex-test";
import {DataModel} from "@/convex/_generated/dataModel";
import {UserDTO} from "@/src/Authentication/server/specs/helpers/dtos/UserDTO";

export async function createFakeUsers(creationAuthor: TestConvexForDataModel<DataModel>, users: UserDTO[]) {
  await creationAuthor.run(async (ctx) => {
    for (const user of users) {
      await ctx.db.insert('users', user);
    }
  });
}