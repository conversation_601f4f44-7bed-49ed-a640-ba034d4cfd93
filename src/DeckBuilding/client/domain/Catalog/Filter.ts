export type Filter =
  | { id: string, text: string; name: string; dataProperty: string; dataType: 'string'; value: string; order: number }
  | { id: string, text: string; name: string; dataProperty: string; dataType: 'number'; value: number; order: number }
  | { id: string, text: string; name: string; dataProperty: string; dataType: 'boolean'; value: boolean; order: number }
  | { id: string, text: string; name: string; dataProperty: string; dataType: 'string[]'; value: string; order: number };
