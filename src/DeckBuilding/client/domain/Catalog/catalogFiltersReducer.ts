import {createReducer} from '@reduxjs/toolkit';
import {availableFiltersUpdatedEvent, filtersUpdatedEvent} from './catalogFilterEvents';
import {Filter} from './Filter';

export interface CatalogFilterState {
  active: Filter[];
  available: Filter[];
}

export const initialCatalogFiltersState: CatalogFilterState = {
  active: [],
  available: [],
};

export const catalogFiltersReducer = createReducer(initialCatalogFiltersState, builder =>
  builder
    .addCase(filtersUpdatedEvent, (state, action) => {
      state.active = action.payload.filters;
    })
    .addCase(availableFiltersUpdatedEvent, (state, action) => {
      state.available = action.payload.filters;
      state.active = action.payload.filters;
    }),
);
