import {createReducer} from '@reduxjs/toolkit';
import {searchUpdatedEvent} from './catalogSearchEvents';

export interface CatalogSearchState {
  search: string;
}

export const initialCatalogSearchState: CatalogSearchState = {
  search: '',
};

export const catalogSearchReducer = createReducer(initialCatalogSearchState, builder =>
  builder.addCase(searchUpdatedEvent, (state, action) => {
    state.search = action.payload.search;
  })
);
