import {DeckBuilderState} from './deckBuilderReducer';
import {CatalogCard} from '../Catalog/CatalogCard';
import {
  cardAddedToDeckEvent,
  cardQuantityIncreasedEvent,
  cardMaxQuantityReachedEvent,
  cardQuantityDecreasedEvent,
  cardRemovedFromDeckEvent,
  cardMinQuantityReachedEvent,
  cardDetailsDisplayedEvent,
  cardDetailsHiddenEvent,
  deckBuilderViewSwitchedEvent,
} from './deckBuilderEvents';

type DeckBuilderDomainEvent =
  | ReturnType<typeof cardAddedToDeckEvent>
  | ReturnType<typeof cardQuantityIncreasedEvent>
  | ReturnType<typeof cardMaxQuantityReachedEvent>
  | ReturnType<typeof cardQuantityDecreasedEvent>
  | ReturnType<typeof cardRemovedFromDeckEvent>
  | ReturnType<typeof cardMinQuantityReachedEvent>
  | ReturnType<typeof cardDetailsDisplayedEvent>
  | ReturnType<typeof cardDetailsHiddenEvent>
  | ReturnType<typeof deckBuilderViewSwitchedEvent>;

export class DeckBuilder {
  private domainEvents: DeckBuilderDomainEvent[] = [];

  private constructor(
    private cardsInDeck: DeckBuilderState['cardsInDeck'],
    private cardDetailsDisplayed: DeckBuilderState['cardDetailsDisplayed'],
    private view: DeckBuilderState['view'],
    private name: DeckBuilderState['name'],
  ) {}

  static fromState(state: DeckBuilderState): DeckBuilder {
    const cardsInDeck: DeckBuilderState['cardsInDeck'] = {};
    Object.entries(state.cardsInDeck).forEach(([id, {card, quantity}]) => {
      cardsInDeck[id] = {card: {...card, data: {...card.data}}, quantity};
    });
    const details = state.cardDetailsDisplayed
      ? {cardImage: state.cardDetailsDisplayed.cardImage, cardName: state.cardDetailsDisplayed.cardName}
      : null;
    return new DeckBuilder(cardsInDeck, details, state.view, state.name);
  }

  toState(): DeckBuilderState {
    const cardsInDeck: DeckBuilderState['cardsInDeck'] = {};
    Object.entries(this.cardsInDeck).forEach(([id, {card, quantity}]) => {
      cardsInDeck[id] = {card: {...card, data: {...card.data}}, quantity};
    });
    const details = this.cardDetailsDisplayed
      ? {cardImage: this.cardDetailsDisplayed.cardImage, cardName: this.cardDetailsDisplayed.cardName}
      : null;
    return {cardsInDeck, cardDetailsDisplayed: details, view: this.view, name: this.name};
  }

  getDomainEvents(): DeckBuilderDomainEvent[] {
    const events = [...this.domainEvents];
    this.domainEvents = [];
    return events;
  }

  addCard(card: CatalogCard): void {
    const existing = this.cardsInDeck[card.id];
    if (!existing) {
      this.cardsInDeck[card.id] = {card, quantity: 1};
      this.domainEvents.push(cardAddedToDeckEvent({card, quantity: 1}));
      return;
    }

    const newQuantity = existing.quantity + 1;

    if (newQuantity <= card.maxDeckQuantity) {
      existing.quantity = newQuantity;
      this.domainEvents.push(cardQuantityIncreasedEvent({id: card.id, newQuantity}));
    }

    if (newQuantity === card.maxDeckQuantity) {
      this.domainEvents.push(cardMaxQuantityReachedEvent(card.id));
    }
  }

  removeCard(card: CatalogCard): void {
    const existing = this.cardsInDeck[card.id];
    if (!existing) return;

    if (existing.quantity === card.minDeckQuantity) {
      return;
    }

    const newQuantity = existing.quantity - 1;
    this.domainEvents.push(cardQuantityDecreasedEvent({id: card.id, newQuantity}));

    if (newQuantity === card.minDeckQuantity) {
      this.domainEvents.push(cardMinQuantityReachedEvent(card.id));
    }

    if (newQuantity === 0) {
      delete this.cardsInDeck[card.id];
      this.domainEvents.push(cardRemovedFromDeckEvent(card.id));
    } else {
      existing.quantity = newQuantity;
    }
  }

  showCardDetails(card: CatalogCard): void {
    this.domainEvents.push(
      cardDetailsDisplayedEvent({cardImage: card.image, cardName: card.name}),
    );
  }

  hideCardDetails(): void {
    this.domainEvents.push(cardDetailsHiddenEvent());
  }

  switchView(view: 'catalog' | 'deck'): void {
    this.domainEvents.push(deckBuilderViewSwitchedEvent(view));
  }
}
