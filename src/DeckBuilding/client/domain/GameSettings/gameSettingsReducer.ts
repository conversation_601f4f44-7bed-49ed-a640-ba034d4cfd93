import {createReducer} from '@reduxjs/toolkit';
import {
  gameSettingsLoadedEvent,
  gameSettingsLoadingFailedEvent,
  gameSettingsLoadingStartedEvent,
} from './gameSettingsEvents';
import {GameSettings} from './GameSettings';

export interface GameSettingsState {
  settings: GameSettings | null;
  status: 'loading' | 'success' | 'error';
  error: string | null;
}

export const initialGameSettingsState: GameSettingsState = {
  settings: null,
  status: 'loading',
  error: null,
};

export const gameSettingsReducer = createReducer(initialGameSettingsState, builder =>
  builder
    .addCase(gameSettingsLoadingStartedEvent, state => {
      state.status = 'loading';
      state.error = null;
    })
    .addCase(gameSettingsLoadedEvent, (state, {payload}) => {
      state.status = 'success';
      state.settings = payload.gameSettings;
    })
    .addCase(gameSettingsLoadingFailedEvent, (state, {payload}) => {
      state.status = 'error';
      state.error = payload.error;
    }),
);
