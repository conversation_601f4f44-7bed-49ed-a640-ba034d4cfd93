'use client';

import { FC, memo, useMemo } from 'react';
import { Flex, IconButton, Text } from '@radix-ui/themes';
import Image from 'next/image';
import ShiningCard from '@/src/Shared/client/infrastructure/components/ShiningCard/ShiningCard';
import { MinusCircleIcon, PlusCircleIcon } from 'lucide-react';
import {CardData} from "@/src/DeckBuilding/client/domain/CardData/CardData.";

type CardProps = {
  id: string;
  name: string;
  image: string;
  minDeckQuantity: number;
  maxDeckQuantity: number;
  data: CardData;
};

type Props = {
  card: CardProps;
  locale: string;
  quantity: number;
  addCardToDeck: (id: string) => void;
  removeCard: (id: string) => void;
  showCardDetails: (id: string) => void;
};

const DeckBuildingCardComponent: FC<Props> = (props) => {
  const isMin = props.quantity === props.card.minDeckQuantity;
  const isMax = props.quantity === props.card.maxDeckQuantity;

  const cardNameColor = useMemo(
    () => (props.quantity > props.card.minDeckQuantity ? 'amber' : 'gray'),
    [props.quantity, props.card.minDeckQuantity],
  );

  return (
    <ShiningCard selected={props.quantity > props.card.minDeckQuantity} disableScaling
                 className={props.quantity > props.card.minDeckQuantity ? 'catalog-card' : 'catalog-card catalog-card-grayscale'}>
      <Flex direction="column" gap="1" style={{ height: '100%' }}>
        <Text size="2" align="center" color={cardNameColor} truncate>
          {props.card.name}
        </Text>

        <Flex flexGrow="1" />

        <Flex direction="column" gap="2">
          <div className="cursor-pointer!">
            <Image
              src={`/game-assets/cards/${(props.locale)}/thumbnail/${props.card.image}`}
              alt={props.card.name}
              width={367}
              height={512}
              onClick={() => props.showCardDetails(props.card.id)}
            />
          </div>

          <Flex justify="center" align="end" gap="2">
            <IconButton
              variant="ghost"
              className="override-ghost"
              disabled={isMin}
            >
              <MinusCircleIcon
                color={isMin ? 'gray' : 'plum'}
                onClick={() => props.removeCard(props.card.id)}
              />
            </IconButton>

            <Text as="span" color={isMin ? 'gray' : undefined}>
              {props.quantity}/{props.card.maxDeckQuantity}
            </Text>

            <IconButton
              variant="ghost"
              className="override-ghost"
              disabled={isMax}
            >
              <PlusCircleIcon onClick={() => props.addCardToDeck(props.card.id)} />
            </IconButton>
          </Flex>
        </Flex>
      </Flex>
    </ShiningCard>
  );
};

const DeckBuildingCard = memo(
  DeckBuildingCardComponent,
  (prev, next) =>
    prev.quantity === next.quantity &&
    prev.locale === next.locale &&
    prev.card === next.card,
);

DeckBuildingCard.displayName = 'DeckBuildingCard';

export default DeckBuildingCard;
