import {convexAuthNextjsToken} from "@convex-dev/auth/nextjs/server";
import {fetchQuery} from "convex/nextjs";
import {api} from "@/convex/_generated/api";
import {Id} from "@/convex/_generated/dataModel";

export async function loadDeckBuilderSettingsByGameId({gameId}: { gameId: string }) {
  const result = await fetchQuery(
    api.queries.loadDeckBuilderSettingsByGameId.endpoint,
    {gameId: gameId as Id<"games">},
    {token: await convexAuthNextjsToken()}
  );

  return {
    error: result.error,
    deckBuilderSettings: result.data || {}
  };
}