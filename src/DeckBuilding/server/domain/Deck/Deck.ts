export interface DeckProps {
  id?: string;
  gameId: string;
  playerId: string;
  name: string;
  cards: {cardId: string; quantity: number}[];
}

export class Deck {
  private props: DeckProps;
  constructor(props: DeckProps) {
    this.props = props;
  }

  static create(props: DeckProps) {
    return new Deck(props);
  }

  static fromSnapshot(snapshot: Required<DeckProps>): Deck {
    return new Deck({...snapshot});
  }

  toSnapshot(): Required<DeckProps> {
    return {
      id: this.props.id!,
      gameId: this.props.gameId,
      playerId: this.props.playerId,
      name: this.props.name,
      cards: this.props.cards,
    };
  }

}
