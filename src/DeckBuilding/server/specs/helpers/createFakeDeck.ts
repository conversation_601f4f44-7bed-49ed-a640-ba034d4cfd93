import {TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {DeckDTO} from "@/src/DeckBuilding/server/specs/helpers/dtos/deckDTO";

export async function createFakeDeck(deckToCreate: DeckDTO, currentUser: TestConvexForDataModel<DataModel>) {
  return currentUser.run((ctx) => ctx.db.insert('decks', {
    gameId: deckToCreate.gameId as Id<"games">,
    playerId: deckToCreate.playerId,
    name: deckToCreate.name,
    cards: deckToCreate.cards ?? [],
  }));
}