import {TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {GameSettingsDTO} from "@/src/DeckBuilding/server/specs/helpers/dtos/GameSettingsDTO";

export async function createFakeGameSettings(settings: GameSettingsDTO, t: TestConvexForDataModel<DataModel>) {
  return t.run(ctx => ctx.db.insert('gameSettings', {
    gameId: settings.gameId as Id<'games'>,
    maxCardsInDeck: settings.maxCardsInDeck,
  }));
}
