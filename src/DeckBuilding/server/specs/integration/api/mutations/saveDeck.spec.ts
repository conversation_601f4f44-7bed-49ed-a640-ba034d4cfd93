import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {createFakeGame} from "@/src/Shared/server/specs/helpers/createFakeGame";
import {api} from "@/convex/_generated/api";
import {createAppUser} from "@/src/Authentication/server/specs/helpers/createAppUsers";
import {ADMIN_IDENTITY, JOHN_APP_USER, JOHN_IDENTITY} from "@/src/Authentication/server/specs/helpers/fakes/fakeUsers";
import {getAllFrom} from "@/src/Shared/server/specs/helpers/getAllFrom";

describe('saveDeck', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asJohn: TestConvexForDataModel<DataModel>;
  let gameId: Id<'games'>;

  beforeEach(async () => {
    const test = convexTest(schema);
    asAdmin = test.withIdentity(ADMIN_IDENTITY);
    asJohn = test.withIdentity(JOHN_IDENTITY);
    await createAppUser(asAdmin, JOHN_APP_USER);
    gameId = await createFakeGame({name: 'Lorcana'}, asAdmin) as Id<'games'>;
  });

  describe('When saving a new deck', () => {
    it('should store the deck in the database', async () => {
      // Arrange

      // Act
      await saveDeck(asJohn, {
        gameId,
        name: 'My deck',
        cards: [{cardId: 'card1', quantity: 2}],
      });

      // Assert
      const decks = await getAllFrom('decks', asAdmin);
      expect(decks).toHaveLength(1);
      const deck = decks[0];
      expect(deck.name).toBe('My deck');
      expect(deck.gameId).toBe(gameId);
      expect(deck.playerId).toBe(JOHN_APP_USER.appUserId);
      expect(deck.cards).toEqual([{cardId: 'card1', quantity: 2}]);
    });
  });

  function saveDeck(
    user: TestConvexForDataModel<DataModel>,
    {gameId, name, cards}: {gameId: Id<'games'>; name: string; cards: {cardId: string; quantity: number}[]},
  ) {
    return user.mutation(api.mutations.saveDeck.endpoint, {gameId, name, cards});
  }
});
