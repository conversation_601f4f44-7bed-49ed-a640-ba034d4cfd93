import {FC} from "react";
import {Container, Flex, Heading} from "@radix-ui/themes";

type Props = {
  errorMessage: string
};

const ErrorLoadingMatchPage: FC<Props> = ({errorMessage}) => {
  return (
    <Container p="3">
      <Flex direction="column" gap="4">
        <Heading size="5">Error</Heading>
        <p>{errorMessage}</p>
      </Flex>
    </Container>
  );
};

export default ErrorLoadingMatchPage;