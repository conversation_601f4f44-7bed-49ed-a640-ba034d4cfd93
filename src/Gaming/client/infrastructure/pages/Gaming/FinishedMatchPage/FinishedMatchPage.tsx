import {FC} from "react";
import {Container, Flex, Heading} from "@radix-ui/themes";

type Props = {
  matchId: string;
  match: {
    players: string[];
    isWinner: boolean;
  };
};

const FinishedMatchPage: FC<Props> = ({matchId, match}) => {
  return (
    <Container p="3">
      <Flex direction="column" gap="4">
        <Heading size="5">Match finished</Heading>
        <p>Match ID: {matchId}</p>
        <p>{match.players.join(" vs ")}</p>
        <p>Winner: {match.isWinner ? `You` : `Your opponent`}</p>
      </Flex>
    </Container>
  );
};

export default FinishedMatchPage;