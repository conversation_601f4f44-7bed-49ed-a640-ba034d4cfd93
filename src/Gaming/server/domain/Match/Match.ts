export interface MatchProps {
  id?: string;
  gameId: string;
  players: string[];
  status: string;
  winner?: string;
}

export type MatchSnapshot = Omit<MatchProps, 'id'> & { id: string };

export class Match {
  private props: MatchProps;
  constructor(props: MatchProps) {
    this.props = props;
  }

  static create(props: MatchProps) {
    return new Match(props);
  }

  static fromSnapshot(snapshot: MatchSnapshot): Match {
    return new Match({...snapshot});
  }

  toSnapshot(): MatchSnapshot {
    return {
      id: this.props.id!,
      gameId: this.props.gameId,
      players: this.props.players,
      status: this.props.status,
      winner: this.props.winner,
    };
  }

  getId() { return this.props.id!; }
  getGameId() { return this.props.gameId; }
  getPlayers() { return [...this.props.players]; }
  getStatus() { return this.props.status; }
  getWinner() { return this.props.winner; }

  isFinished() { return this.props.status === 'finished'; }

  finishMatchWithWinner(winner: string) {
    this.props.winner = winner;
    this.props.status = 'finished';
  }

  getOpponentOf(playerId: string): string {
    return this.props.players.find(p => p !== playerId)!;
  }
}
