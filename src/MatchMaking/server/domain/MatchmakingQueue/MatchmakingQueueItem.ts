export interface MatchmakingQueueItemProps {
  id?: string;
  gameId: string;
  deckId: string;
  playerId: string;
  queuedAt: number;
}

export class MatchmakingQueueItem {
  private props: MatchmakingQueueItemProps;

  constructor(props: MatchmakingQueueItemProps) {
    this.props = props;
  }

  static create(props: MatchmakingQueueItemProps) {
    return new MatchmakingQueueItem(props);
  }

  static fromSnapshot(snapshot: Required<MatchmakingQueueItemProps>): MatchmakingQueueItem {
    return new MatchmakingQueueItem({...snapshot});
  }

  toSnapshot(): Required<MatchmakingQueueItemProps> {
    return {
      id: this.props.id!,
      gameId: this.props.gameId,
      deckId: this.props.deckId,
      playerId: this.props.playerId,
      queuedAt: this.props.queuedAt,
    };
  }

  getId() { return this.props.id!; }
  getPlayerId() { return this.props.playerId; }
  getGameId() { return this.props.gameId; }
  belongsTo(playerId: string) { return this.props.playerId === playerId; }
}
