import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {createFakeGame} from "@/src/Shared/server/specs/helpers/createFakeGame";
import {api} from "@/convex/_generated/api";
import {createAppUser} from "@/src/Authentication/server/specs/helpers/createAppUsers";
import {ADMIN_IDENTITY, JOHN_APP_USER, JOHN_IDENTITY} from "@/src/Authentication/server/specs/helpers/fakes/fakeUsers";
import {LORCANA} from "@/src/Shared/server/specs/helpers/fakes/fakeGames";
import {getAllFrom} from "@/src/Shared/server/specs/helpers/getAllFrom";
import {createFakeDeck} from "@/src/DeckBuilding/server/specs/helpers/createFakeDeck";

describe("cancelMatchRegistration", () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asJohn: TestConvexForDataModel<DataModel>;
  let gameId: Id<"games">;
  let deckId: Id<"decks">;

  beforeEach(async () => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    asJohn = testConvex.withIdentity(JOHN_IDENTITY);

    await createAppUser(asAdmin, JOHN_APP_USER);
    gameId = await createFakeGame(LORCANA, asAdmin) as Id<"games">;
    deckId = await createFakeDeck({name: 'Deck 1', gameId, playerId: JOHN_APP_USER.appUserId}, asAdmin) as Id<"decks">;
  });

  describe("When the user is in the matchmaking queue", () => {
    beforeEach(async () => {
      await insertMatchmakingQueueItem();
      await setPlayerStatus("waiting-for-opponent");
    });

    it("should remove the user from the queue", async () => {
      // Act
      await cancelRegistration();

      // Assert
      const queue = await getAllFrom("matchmakingQueue", asAdmin);
      expect(queue).toHaveLength(0);
    });

    it("should update the user status to 'idle'", async () => {
      // Act
      await cancelRegistration();

      // Assert
      const users = await getAllFrom("appUsers", asAdmin);
      const user = users.find(u => u.appUserId === JOHN_APP_USER.appUserId)!;
      expect(user.status).toBe("idle");
    });

    it("should dispatch a PlayerRemovedFromQueue event with reason 'cancelled'", async () => {
      // Act
      await cancelRegistration();

      // Assert
      const events = await getAllFrom("matchmakingEvents", asAdmin);
      const removed = events.find(e => e.type === "PlayerRemovedFromQueue")!;
      expect(removed.payload.reason).toBe("cancelled");
      expect(removed.payload.playerId).toBe(JOHN_APP_USER.appUserId);
    });

    it("should dispatch a PlayerRegistrationCancelled event", async () => {
      // Act
      await cancelRegistration();

      // Assert
      const events = await getAllFrom("matchmakingEvents", asAdmin);
      const cancelled = events.find(e => e.type === "PlayerRegistrationCancelled")!;
      expect(cancelled.payload.playerId).toBe(JOHN_APP_USER.appUserId);
    });
  });

  describe("When the user is not in the queue", () => {
    it("should do nothing", async () => {
      // Act
      await cancelRegistration();

      // Assert
      const queue = await getAllFrom("matchmakingQueue", asAdmin);
      expect(queue).toHaveLength(0);

      const events = await getAllFrom("matchmakingEvents", asAdmin);
      expect(events).toHaveLength(0);
    });
  });

  async function insertMatchmakingQueueItem() {
    await asAdmin.run(async (ctx) => {
      const queueId = await ctx.db.insert("matchmakingQueue", {
        gameId,
        playerId: JOHN_APP_USER.appUserId,
        deckId,
        queuedAt: Date.now(),
      });

      await ctx.db.insert("matchmakingEvents", {
        gameId,
        aggregateId: queueId,
        type: "PlayerAddedToMatchMakingQueue",
        payload: {
          playerId: JOHN_APP_USER.appUserId,
          gameId,
          queueId,
        },
        occurredAt: Date.now(),
      });
    });
  }

  async function setPlayerStatus(status: string) {
    await asAdmin.run(async (ctx) => {
      const user = await ctx.db
        .query("appUsers")
        .withIndex("by_appUserId", q => q.eq("appUserId", JOHN_APP_USER.appUserId))
        .unique();

      await ctx.db.patch(user!._id, {status});
    });
  }

  async function cancelRegistration() {
    await asJohn.mutation(api.mutations.cancelMatchRegistration.endpoint, {
      gameId,
    });
  }
});
