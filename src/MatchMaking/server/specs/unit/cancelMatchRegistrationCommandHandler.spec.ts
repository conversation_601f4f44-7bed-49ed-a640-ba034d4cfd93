import {describe, it, expect, beforeEach} from 'vitest';
import {CancelMatchRegistrationCommandHandler} from '@/src/MatchMaking/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler';
import {InMemoryMatchmakingQueueRepository} from '@/src/MatchMaking/server/infrastructure/repositories/InMemoryMatchmakingQueueRepository';
import {MatchmakingQueueItem} from '@/src/MatchMaking/server/domain/MatchmakingQueue/MatchmakingQueueItem';
import {createFakeContext} from '@/src/Shared/server/specs/helpers/fakes/FakeContext';
import {CancelMatchRegistrationCommand} from '@/src/MatchMaking/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand';

let context: ReturnType<typeof createFakeContext>;
let repository: InMemoryMatchmakingQueueRepository;
let handler: CancelMatchRegistrationCommandHandler;

beforeEach(() => {
  context = createFakeContext();
  repository = new InMemoryMatchmakingQueueRepository();
  handler = new CancelMatchRegistrationCommandHandler(context, repository);
});

describe('CancelMatchRegistrationCommandHandler', () => {
  describe('When the user is in the queue', () => {
    it('should remove him and dispatch events', async () => {
      // Arrange
      const item = MatchmakingQueueItem.create({gameId: 'g1', deckId: 'd1', playerId: 'u1', queuedAt: Date.now()});
      await repository.save(item);

      const command: CancelMatchRegistrationCommand = {gameId: 'g1', userId: 'u1'};

      // Act
      await handler.handle(command);

      // Assert
      const queue = await repository.findByGameId('g1');
      expect(queue.toItems()).toHaveLength(0);
      expect(context.recorded).toHaveLength(2);
      expect(context.recorded[0].event.type).toBe('PlayerRemovedFromQueue');
      expect(context.recorded[1].event.type).toBe('PlayerRegistrationCancelled');
    });
  });

  describe('When the user is not in the queue', () => {
    it('should do nothing', async () => {
      // Arrange
      const command: CancelMatchRegistrationCommand = {gameId: 'g1', userId: 'u1'};

      // Act
      await handler.handle(command);

      // Assert
      const queue = await repository.findByGameId('g1');
      expect(queue.toItems()).toHaveLength(0);
      expect(context.recorded).toHaveLength(0);
    });
  });
});
