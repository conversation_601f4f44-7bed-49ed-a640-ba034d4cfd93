import {describe, it, expect, beforeEach} from 'vitest';
import {CleanUpMatchMakingQueueCommandHandler} from '@/src/MatchMaking/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler';
import {InMemoryMatchRepository} from '@/src/Gaming/server/infrastructure/repositories/InMemoryMatchRepository';
import {InMemoryMatchmakingQueueRepository} from '@/src/MatchMaking/server/infrastructure/repositories/InMemoryMatchmakingQueueRepository';
import {MatchmakingQueueItem} from '@/src/MatchMaking/server/domain/MatchmakingQueue/MatchmakingQueueItem';
import {Match} from '@/src/Gaming/server/domain/Match/Match';
import {createFakeContext} from '@/src/Shared/server/specs/helpers/fakes/FakeContext';

let context: ReturnType<typeof createFakeContext>;
let matchRepository: InMemoryMatchRepository;
let queueRepository: InMemoryMatchmakingQueueRepository;
let handler: CleanUpMatchMakingQueueCommandHandler;

beforeEach(() => {
  context = createFakeContext();
  matchRepository = new InMemoryMatchRepository();
  queueRepository = new InMemoryMatchmakingQueueRepository();
  handler = new CleanUpMatchMakingQueueCommandHandler(context, matchRepository, queueRepository);
});

describe('CleanUpMatchMakingQueueCommandHandler', () => {
  describe('When the match exists', () => {
    it('should remove matched players from the queue', async () => {
      // Arrange
      const match = Match.create({gameId: 'g1', players: ['u1','u2'], status: 'setup'});
      const matchId = await matchRepository.save(match);
      await queueRepository.save(MatchmakingQueueItem.create({gameId: 'g1', deckId: 'd1', playerId: 'u1', queuedAt: Date.now()}));
      await queueRepository.save(MatchmakingQueueItem.create({gameId: 'g1', deckId: 'd2', playerId: 'u2', queuedAt: Date.now()}));
      await queueRepository.save(MatchmakingQueueItem.create({gameId: 'g1', deckId: 'd3', playerId: 'u3', queuedAt: Date.now()}));

      // Act
      await handler.handle({matchId, players: ['u1','u2']});

      // Assert
      const queue = await queueRepository.findByGameId('g1');
      const remaining = queue.toItems().map(i => i.getPlayerId());
      expect(remaining).toEqual(['u3']);
      expect(context.recorded).toHaveLength(2);
    });

    it('should dispatch PlayerRemovedFromQueue events', async () => {
      // Arrange
      const match = Match.create({gameId: 'g1', players: ['u1','u2'], status: 'setup'});
      const matchId = await matchRepository.save(match);
      await queueRepository.save(MatchmakingQueueItem.create({gameId: 'g1', deckId: 'd1', playerId: 'u1', queuedAt: Date.now()}));
      await queueRepository.save(MatchmakingQueueItem.create({gameId: 'g1', deckId: 'd2', playerId: 'u2', queuedAt: Date.now()}));

      // Act
      await handler.handle({matchId, players: ['u1','u2']});

      // Assert
      const [first, second] = context.recorded.map(r => r.event.type);
      expect(first).toBe('PlayerRemovedFromQueue');
      expect(second).toBe('PlayerRemovedFromQueue');
    });
  });

  describe('When the match does not exist', () => {
    it('should do nothing', async () => {
      // Arrange
      await queueRepository.save(MatchmakingQueueItem.create({gameId: 'g1', deckId: 'd1', playerId: 'u1', queuedAt: Date.now()}));

      // Act
      await handler.handle({matchId: 'unknown', players: ['u1']});

      // Assert
      const queue = await queueRepository.findByGameId('g1');
      expect(queue.toItems()).toHaveLength(1);
      expect(context.recorded).toHaveLength(0);
    });
  });
});
