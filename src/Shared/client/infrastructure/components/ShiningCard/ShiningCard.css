.glass-shine-effect {
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
  background-color: transparent;
  transition: transform 200ms ease, background-color 200ms ease, box-shadow 200ms ease, border-color 200ms ease;
}

.glass-shine-effect::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-30deg);
  animation: none;
  pointer-events: none;
}

.glass-shine-effect:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 32px 0 rgba(107, 107, 107, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.glass-shine-effect.selected {
  transform: scale(1.05);
  background-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 8px 32px 0 rgba(107, 107, 107, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

/* Disable scaling effect but keep other hover effects */
.glass-shine-effect.no-scaling:hover,
.glass-shine-effect.no-scaling.selected {
  transform: none;
}

.glass-shine-effect:hover::before,
.glass-shine-effect.selected::before {
  animation: shine 0.5s forwards;
}

@keyframes shine {
  to {
    left: 150%;
  }
}

.catalog-card img {
  cursor: pointer;
}

.catalog-card-grayscale img {
  border-radius: 8px;
  filter: grayscale(25%);
  transition: filter 0.3s ease;
}

.catalog-card-grayscale:hover img {
  filter: grayscale(0);
  transition: filter 0.3s ease;
}