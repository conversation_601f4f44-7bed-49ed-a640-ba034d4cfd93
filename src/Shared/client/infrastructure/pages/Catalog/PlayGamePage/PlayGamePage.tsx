import {FC} from "react";
import {Container, Flex} from "@radix-ui/themes";
import {StartGameButton} from "@/src/MatchMaking/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton";

type Props = {
  locale: string;
  gameId: string;
};

const PlayGamePage: FC<Props> = ({locale, gameId}) => {
  console.log("locale", locale);

  return (
    <Container p="3">
      <Flex direction="column" gap="3">
        <StartGameButton gameId={gameId}/>
      </Flex>
    </Container>
  );
};

export default PlayGamePage;