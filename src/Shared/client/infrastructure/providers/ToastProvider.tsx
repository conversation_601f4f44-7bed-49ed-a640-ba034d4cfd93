'use client';

import {FC, PropsWithChildren, createContext, useContext, useState} from 'react';
import * as Toast from '@radix-ui/react-toast';

type ToastContextValue = (message: string) => void;

const ToastContext = createContext<ToastContextValue>(() => {});

export const useToast = () => useContext(ToastContext);

export const ToastProvider: FC<PropsWithChildren> = ({children}) => {
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState('');

  const showToast = (msg: string) => {
    setMessage(msg);
    setOpen(true);
  };

  return (
    <ToastContext.Provider value={showToast}>
      <Toast.Provider swipeDirection="right">
        {children}
        <Toast.Root open={open} onOpenChange={setOpen} className="bg-gray-900 text-white px-3 py-2 rounded shadow">
          <Toast.Title>{message}</Toast.Title>
        </Toast.Root>
        <Toast.Viewport className="fixed bottom-0 right-0 p-4" />
      </Toast.Provider>
    </ToastContext.Provider>
  );
};
