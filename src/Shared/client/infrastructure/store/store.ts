import {configureStore} from "@reduxjs/toolkit";
import {DeckDraftService} from "@/src/DeckBuilding/client/application/services/DeckDraftService/DeckDraftService";
import {BrowserLocationService} from "@/src/Shared/client/infrastructure/services/location/browserLocationService";
import {BrowserDeckDraftService} from "@/src/DeckBuilding/client/infrastructure/services/deckDraft/browserDeckDraftService";
import {subscribeToDeckDraft} from "@/src/DeckBuilding/client/application/subscribers/subscribeToDeckDraft";
import {rootReducers} from "@/src/Shared/client/application/store/appStore";

export const locationService = new BrowserLocationService();
export let deckDraftService: DeckDraftService = new BrowserDeckDraftService();
export const setDeckDraftService = (service: DeckDraftService) => {
  deckDraftService = service;
};

export const store = configureStore({
  reducer: rootReducers,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      thunk: {extraArgument: {locationService, deckDraftService}},
      serializableCheck: {
        ignoredActions: ['catalog/cardsLoaded'],
      },
    }),
});

subscribeToDeckDraft(store);
