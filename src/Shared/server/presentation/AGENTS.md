# Presentation Guidelines

This layer translates HTTP or RPC requests into application commands or queries and formats the responses.

- Controllers receive requests and call command/query handlers.
- Presenters shape the response data for clients.
- Request DTOs and view models live in dedicated folders.

When adding a new API endpoint, create a controller or serverless function here and keep it free of business logic.

