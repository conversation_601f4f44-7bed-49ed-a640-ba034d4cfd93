# Specs Guidelines

Tests under this folder are integration tests executed with `convex-test`. They exercise use cases through public APIs.

- Follow the Arrange/Act/Assert structure from the repository instructions.
- Use helper utilities from `helpers/` for setup.
- Prefer sociable tests and avoid mocking the domain.
- Specs live under `integration` grouped by API or gateway.
- When asserting known data, use `!` instead of optional chaining to avoid silent failures.

Add new tests alongside features to drive development.

