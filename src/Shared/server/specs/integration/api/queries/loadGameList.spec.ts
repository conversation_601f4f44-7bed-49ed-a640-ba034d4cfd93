import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel} from "@/convex/_generated/dataModel";
import {createFakeGame} from "@/src/Shared/server/specs/helpers/createFakeGame";
import {api} from "@/convex/_generated/api";
import {createAppUser} from "@/src/Authentication/server/specs/helpers/createAppUsers";
import {ADMIN_IDENTITY, SOPHIE_APP_USER, SOPHIE_IDENTITY} from "@/src/Authentication/server/specs/helpers/fakes/fakeUsers";
import {GOBELIN_DES_BOIS, LORCANA} from "@/src/Shared/server/specs/helpers/fakes/fakeGames";

describe('loadGameList', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    asSophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    createAppUser(asAdmin, SOPHIE_APP_USER);
  });

  describe('When no game exist', () => {
    it('should return an empty list', async () => {
      // Act
      const result = await loadGameList(asSophie);

      // Assert
      expect(result.data).toEqual([]);
    });
  });

  describe('When multiple games exist', () => {
    it('should load all available games', async () => {
      // Arrange
      const lorcanaId = await createFakeGame(LORCANA, asAdmin);
      const gobelinDesBoisId = await createFakeGame(GOBELIN_DES_BOIS, asAdmin);

      // Act
      const result = await loadGameList(asSophie);

      // Assert
      expect(result.data).toEqual([
        {id: lorcanaId, ...LORCANA},
        {id: gobelinDesBoisId, ...GOBELIN_DES_BOIS},
      ]);
    });
  });

  function loadGameList(currentUser: TestConvexForDataModel<DataModel>) {
    return currentUser.query(api.queries.loadGameList.endpoint, {});
  }
});