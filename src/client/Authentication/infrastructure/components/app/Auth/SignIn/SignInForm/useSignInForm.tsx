import {useAuthActions} from '@convex-dev/auth/react';
import {useCallback, useState} from 'react';
import {useLocale} from "@/src/Shared/client/infrastructure/hooks/useLocale/useLocale";
import {buildGameListUrl} from "@/src/Shared/client/infrastructure/builders/urlBuilder";

export const useSignInForm = () => {
  const [isLoading, setLoading] = useState(false);
  const locale = useLocale();
  const {signIn} = useAuthActions();

  const handleSignIn = useCallback(async () => {
    setLoading(true);
    await signIn('github', {redirectTo: buildGameListUrl(locale)});
  }, [signIn, locale]);

  return {
    isLoading,
    handleSignIn,
  };
};
