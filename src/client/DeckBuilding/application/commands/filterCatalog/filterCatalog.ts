import {createAsyncThunk} from "@reduxjs/toolkit";
import {CatalogFilters} from "@/src/DeckBuilding/client/domain/Catalog/CatalogFilters";
import {RootState, ThunkExtra} from "@/src/Shared/client/application/store/appStore";
import {FilterCatalogRequest} from "@/src/DeckBuilding/client/application/commands/filterCatalog/filterCatalogRequest";

const FILTER_CATALOG_EVENT_TYPE = 'catalog/filterCatalog';

export const filterCatalog = createAsyncThunk<void, FilterCatalogRequest, {
  state: RootState;
  extra: ThunkExtra;
}>(FILTER_CATALOG_EVENT_TYPE, async ({filters}, {dispatch, getState, extra}) => {
  const catalogFilters = CatalogFilters.fromState(getState().catalogFilters);
  const events = catalogFilters.applyFilters(filters);
  events.forEach(dispatch);
  extra.locationService.setFilters(catalogFilters.toState().active.map(f => f.name));
});
