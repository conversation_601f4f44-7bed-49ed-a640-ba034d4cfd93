import {
  catalogCardsLoadedEvent,
  catalogCardsLoadingFailedEvent,
  catalogCardsLoadingStartedEvent
} from "@/src/DeckBuilding/client/domain/Catalog/catalogEvents";
import {AppDispatch} from "@/src/Shared/client/application/store/appStore";
import {LoadCatalogCardsRequest} from "@/src/DeckBuilding/client/application/commands/loadCatalogCards/loadCatalogCardsRequest";

export const loadCatalogCards = (request: LoadCatalogCardsRequest) => (dispatch: AppDispatch): void => {
  if (!request) {
    dispatch(catalogCardsLoadingStartedEvent());
    return;
  }

  if (request.error) {
    dispatch(catalogCardsLoadingFailedEvent({error: request.error}));
    return;
  }

  const cards = request.data?.cards || [];
  dispatch(catalogCardsLoadedEvent({cards}));
};