import {createAsyncThunk} from "@reduxjs/toolkit";
import {RootState} from "@/src/Shared/client/application/store/appStore";
import {deckLoadedEvent} from "@/src/DeckBuilding/client/domain/DeckBuilder/deckBuilderEvents";
import {getCatalogCardById} from "@/src/DeckBuilding/client/application/queries/getCatalogCardById/getCatalogCardById";
import {
  LoadDeckIntoBuilderRequest
} from "@/src/DeckBuilding/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest";

export const loadDeckIntoBuilder = createAsyncThunk<void, LoadDeckIntoBuilderRequest, {state: RootState}>(
  'deckBuilder/loadDeck',
  async ({deck}, {dispatch, getState}) => {
    const state = getState();
    const deckCards = deck.cards.map(({cardId, quantity}) => ({
      card: getCatalogCardById(state, cardId),
      quantity,
    }));
    dispatch(deckLoadedEvent({name: deck.name, cards: deckCards}));
  }
);
