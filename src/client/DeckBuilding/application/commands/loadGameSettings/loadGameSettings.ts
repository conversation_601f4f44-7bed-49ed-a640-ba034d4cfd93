import {AppDispatch} from '@/src/Shared/client/application/store/appStore';
import {
  gameSettingsLoadedEvent,
  gameSettingsLoadingFailedEvent,
  gameSettingsLoadingStartedEvent,
} from '@/src/DeckBuilding/client/domain/GameSettings/gameSettingsEvents';
import {LoadGameSettingsRequest} from './loadGameSettingsRequest';

export const loadGameSettings = (request: LoadGameSettingsRequest) => (dispatch: AppDispatch): void => {
  if (!request) {
    dispatch(gameSettingsLoadingStartedEvent());
    return;
  }

  if (request.error) {
    dispatch(gameSettingsLoadingFailedEvent({error: request.error}));
    return;
  }

  const settings = {maxCardsInDeck: request.data!.maxCardsInDeck};
  dispatch(gameSettingsLoadedEvent({gameSettings: settings}));
};
