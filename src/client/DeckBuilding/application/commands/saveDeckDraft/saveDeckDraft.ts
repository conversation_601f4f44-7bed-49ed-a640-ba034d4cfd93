import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState, ThunkExtra} from '@/src/Shared/client/application/store/appStore';

export const saveDeckDraft = createAsyncThunk<void, void, {state: RootState; extra: ThunkExtra}>(
  'deckBuilder/saveDraft',
  async (_, {getState, extra: {deckDraftService}}) => {
    const draft = ({
      name: getState().deckBuilder.name,
      cards: Object.values(getState().deckBuilder.cardsInDeck).map(({card, quantity}) => ({
        cardId: card.id,
        quantity,
      })),
    });
    deckDraftService.saveDraft(draft);
  }
);
