import {RootState} from "@/src/Shared/client/application/store/appStore";
import {createAsyncThunk} from "@reduxjs/toolkit";
import {SHOW_CARD_DETAILS_EVENT_TYPE} from "@/src/DeckBuilding/client/domain/DeckBuilder/deckBuilderEvents";
import {DeckBuilder} from "@/src/DeckBuilding/client/domain/DeckBuilder/DeckBuilder";
import {ShowCardDetailsRequest} from "@/src/DeckBuilding/client/application/commands/showCardDetails/showCardDetailsRequest";
import {getCatalogCardById} from "@/src/DeckBuilding/client/application/queries/getCatalogCardById/getCatalogCardById";

export const showCardDetails = createAsyncThunk<void, ShowCardDetailsRequest, { state: RootState }>(
  SHOW_CARD_DETAILS_EVENT_TYPE,
  async ({cardId}, {dispatch, getState}) => {
    const state = getState();
    const card = getCatalogCardById(state, cardId);
    const deckBuilder = DeckBuilder.fromState(state.deckBuilder);
    deckBuilder.showCardDetails(card);
    deckBuilder.getDomainEvents().forEach(dispatch);
  }
);