import {createAsyncThunk} from "@reduxjs/toolkit";
import {RootState, ThunkExtra} from "@/src/Shared/client/application/store/appStore";
import {SWITCH_DECK_BUILDER_VIEW_EVENT_TYPE} from "@/src/DeckBuilding/client/domain/DeckBuilder/deckBuilderEvents";
import {DeckBuilder} from "@/src/DeckBuilding/client/domain/DeckBuilder/DeckBuilder";
import {SwitchDeckBuilderViewRequest} from "@/src/DeckBuilding/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest";

export const switchDeckBuilderView = createAsyncThunk<void, SwitchDeckBuilderViewRequest, { state: RootState; extra: ThunkExtra }>(
  SWITCH_DECK_BUILDER_VIEW_EVENT_TYPE,
  async ({view}, {dispatch, getState}) => {
    const deckBuilder = DeckBuilder.fromState(getState().deckBuilder);
    deckBuilder.switchView(view);
    deckBuilder.getDomainEvents().forEach(dispatch);
  }
);
