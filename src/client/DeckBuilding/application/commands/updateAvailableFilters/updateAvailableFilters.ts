import {AppDispatch} from "@/src/Shared/client/application/store/appStore";
import {availableFiltersUpdatedEvent} from "@/src/DeckBuilding/client/domain/Catalog/catalogFilterEvents";
import {
  UpdateAvailableFiltersRequest
} from "@/src/DeckBuilding/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest";

export const updateAvailableFilters = ({filters}: UpdateAvailableFiltersRequest) => (dispatch: AppDispatch): void => {
  dispatch(availableFiltersUpdatedEvent({filters}));
};