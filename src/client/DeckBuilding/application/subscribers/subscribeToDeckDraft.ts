import {AnyAction, Store} from '@reduxjs/toolkit';
import {RootState, AppDispatch} from '@/src/Shared/client/application/store/appStore';
import {saveDeckDraft} from '@/src/DeckBuilding/client/application/commands/saveDeckDraft/saveDeckDraft';
import {initialDeckBuilderState} from '@/src/DeckBuilding/client/domain/DeckBuilder/deckBuilderReducer';

export const subscribeToDeckDraft = (
  store: Store<RootState, AnyAction> & {dispatch: AppDispatch},
) => {
  const buildDraft = (state: RootState['deckBuilder']) => ({
    name: state.name,
    cards: Object.values(state.cardsInDeck).map(({card, quantity}) => ({
      cardId: card.id,
      quantity,
    })),
  });

  let previous = JSON.stringify(buildDraft(initialDeckBuilderState));
  store.subscribe(() => {
    const state = store.getState();
    const current = JSON.stringify(buildDraft(state.deckBuilder));
    if (current !== previous) {
      previous = current;
      store.dispatch(saveDeckDraft());
    }
  });
};
