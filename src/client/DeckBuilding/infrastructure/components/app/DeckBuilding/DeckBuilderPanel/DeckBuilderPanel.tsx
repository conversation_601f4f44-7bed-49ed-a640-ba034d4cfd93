'use client';

import {FC, memo, useState, useEffect} from 'react';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '@/src/Shared/client/application/store/appStore';
import {Button, Flex, ScrollArea, Text,} from '@radix-ui/themes';
import Image from 'next/image';
import {useAutoAnimate} from '@formkit/auto-animate/react';
import DeckCardDetailsDialog
  from '@/src/DeckBuilding/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog';
import {useDeckBuilder} from '@/src/DeckBuilding/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder';
import {useMutation} from 'convex/react';
import {api} from '@/convex/_generated/api';
import {useToast} from '@/src/Shared/client/infrastructure/providers/ToastProvider';
import {useGameId} from '@/src/Shared/client/infrastructure/hooks/useGameId/useGameId';
import {useDeckId} from '@/src/DeckBuilding/client/infrastructure/hooks/useDeckId/useDeckId';
import {useLocale} from '@/src/Shared/client/infrastructure/hooks/useLocale/useLocale';
import {Id} from '@/convex/_generated/dataModel';
import {useRouter, useSearchParams} from 'next/navigation';
import DeckCardRow from './DeckCardRow';
import TotalCardsInDeckCounter from "@/src/DeckBuilding/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter";
import SaveDeckDialog from "@/src/DeckBuilding/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog";
import {buildEditDeckUrlWithQuery} from '@/src/Shared/client/infrastructure/builders/urlBuilder';
import {clearDeckDraft} from '@/src/DeckBuilding/client/application/commands/clearDeckDraft/clearDeckDraft';
import {hasDeckDraft} from '@/src/DeckBuilding/client/application/queries/hasDeckDraft/hasDeckDraft';
import {deckDraftService} from '@/src/Shared/client/infrastructure/store/store';

const DeckBuilderPanel: FC = () => {
  const {
    deckCards,
    addCardToDeck,
    removeCard,
    showCardDetails,
    name,
  } = useDeckBuilder();
  const dispatch = useDispatch<AppDispatch>();
  const gameId = useGameId();
  const locale = useLocale();
  const router = useRouter();
  const searchParams = useSearchParams();
  const saveDeck = useMutation(api.mutations.saveDeck.endpoint);
  const updateDeck = useMutation(api.mutations.updateDeck.endpoint);
  const [isSaveDialogOpen, setSaveDialogOpen] = useState(false);
  const toast = useToast();

  const deckId = useDeckId();
  const handleSave = async (name: string) => {
    try {
      if (deckId) {
        await updateDeck({
          deckId: deckId as Id<'decks'>,
          name,
          cards: deckCards.map(c => ({cardId: c.id, quantity: c.quantity ?? 1})),
        });
        dispatch(clearDeckDraft());
      } else {
        const id = await saveDeck({
          gameId: gameId as Id<'games'>,
          name,
          cards: deckCards.map(c => ({cardId: c.id, quantity: c.quantity ?? 1})),
        });
        const search = new URLSearchParams(searchParams.toString());
        search.delete('deckId');
        search.delete('locale');
        const query = search.toString();
        router.replace(
          buildEditDeckUrlWithQuery(
            locale,
            gameId,
            id as string,
            query ? `?${query}` : ''
          )
        );
        dispatch(clearDeckDraft());
      }
    } catch (error) {
      toast((error as Error).message);
    }
  };

  const [hoveredCardId, setHoveredCardId] = useState<string | null>(null);
  const [parentRef] = useAutoAnimate<HTMLDivElement>({easing: 'linear'});
  const [draftExists, setDraftExists] = useState(false);

  useEffect(() => {
    setDraftExists(hasDeckDraft(deckDraftService));
  }, []);

  return (
    <Flex direction="column" gap="3" className="h-full">
      <Flex justify="between" align="center">
        <TotalCardsInDeckCounter/>
        <Button
          size="2"
          mr="1"
          color="indigo"
          onClick={() => {
            if (deckId && name) {
              void handleSave(name);
            } else {
              setSaveDialogOpen(true);
            }
          }}
        >
          {`Save this Deck${draftExists ? '*' : ''}`}
        </Button>
      </Flex>

      <ScrollArea type="always" scrollbars="vertical" className="h-full">
        {deckCards.length === 0 ? (
          <Flex direction="column" align="center" p="4">
            <Text size="4" color="gray" align="center" mt="4">
              Your deck is empty.
            </Text>
            <Image
              src="/logos/deck.png"
              alt="Deck loading"
              width={200}
              height={200}
            />
            <Text size="3" color="gray" align="center" my="2">
              Add cards by clicking the plus button on each card.
            </Text>
            <Image
              src="/ui/arrow-left.png"
              alt="Arrow left"
              width={60}
              height={60}
            />
          </Flex>
        ) : (
          <Flex direction="column" gap="1" pr="4" ref={parentRef}>
            {deckCards.map(card => (
              <DeckCardRow
                key={card.id}
                card={card}
                isHovered={card.id === hoveredCardId}
                setHoveredCardId={setHoveredCardId}
                addCardToDeck={addCardToDeck}
                removeCard={removeCard}
                showCardDetails={showCardDetails}
              />
            ))}
          </Flex>
        )}
      </ScrollArea>

      <DeckCardDetailsDialog/>
      <SaveDeckDialog
        open={isSaveDialogOpen}
        onOpenChange={setSaveDialogOpen}
        onSave={handleSave}
      />
    </Flex>
  );
};

export default memo(DeckBuilderPanel);
