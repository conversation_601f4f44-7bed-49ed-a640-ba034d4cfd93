import {FC, memo} from "react";
import {Box, Flex, IconButton, Text} from "@radix-ui/themes";
import ShiningCard from "@/src/Shared/client/infrastructure/components/ShiningCard/ShiningCard";
import Image from "next/image";
import {MinusCircleIcon, PlusCircleIcon} from "lucide-react";
import {CardData} from "@/src/DeckBuilding/client/domain/CardData/CardData.";

type DeckCard = {
  id: string;
  name: string;
  image: string;
  minDeckQuantity: number;
  maxDeckQuantity: number;
  quantity?: number;
  data: CardData;
};

type RowProps = {
  card: DeckCard;
  isHovered: boolean;
  setHoveredCardId: (id: string | null) => void;
  addCardToDeck: (id: string) => void;
  removeCard: (id: string) => void;
  showCardDetails: (id: string) => void;
};

const DeckCardRow: FC<RowProps> = memo(
  ({
     card,
     isHovered,
     setHoveredCardId,
     addCardToDeck,
     removeCard,
     showCardDetails,
   }) => {
    const qty = card.quantity ?? card.minDeckQuantity;
    const isMin = qty === card.minDeckQuantity;
    const isMax = qty === card.maxDeckQuantity;
    const cardColor = (card.data as { color?: string }).color;

    return (
      <Box>
        <ShiningCard
          style={{ padding: '5px' }}
          disableScaling
          selected={isHovered}
          onMouseEnter={() => setHoveredCardId(card.id)}
          onMouseLeave={() => setHoveredCardId(null)}
        >
          <Flex justify="between" align="center">
            <Flex align="center" gap="2" onClick={() => showCardDetails(card.id)} className="w-full">
              {cardColor && (
                <Image
                  src={`/game-assets/filters/colors/${cardColor}.svg`}
                  alt={cardColor}
                  width={30}
                  height={30}
                />
              )}
              <Text
                size="2"
                as="label"
                className="cursor-pointer"
                color={isHovered ? 'amber' : undefined}

              >
                {card.name}
              </Text>
            </Flex>
            <Flex gap="1" align="center">
              <IconButton
                variant="ghost"
                className="override-ghost"
                disabled={isMin}
              >
                <MinusCircleIcon
                  color={isMin ? 'gray' : 'plum'}
                  onClick={() => removeCard(card.id)}
                />
              </IconButton>
              <Text as="span" color={isMin ? 'gray' : undefined}>
                {qty}/{card.maxDeckQuantity}
              </Text>
              <IconButton
                variant="ghost"
                className="override-ghost"
                disabled={isMax}
              >
                <PlusCircleIcon onClick={() => addCardToDeck(card.id)} />
              </IconButton>
            </Flex>
          </Flex>
        </ShiningCard>
      </Box>
    );
  },
  (prev, next) =>
    (prev.card.quantity ?? prev.card.minDeckQuantity) ===
    (next.card.quantity ?? next.card.minDeckQuantity) &&
    prev.isHovered === next.isHovered,
);

DeckCardRow.displayName = 'DeckCardRow';

export default DeckCardRow;
