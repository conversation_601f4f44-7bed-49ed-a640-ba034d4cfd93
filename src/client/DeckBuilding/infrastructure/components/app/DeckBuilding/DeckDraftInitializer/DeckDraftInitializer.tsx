'use client';

import {useEffect, useRef} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch, RootState} from '@/src/Shared/client/application/store/appStore';
import {loadDeckDraft} from '@/src/DeckBuilding/client/application/commands/loadDeckDraft/loadDeckDraft';
import {isCatalogLoading} from '@/src/DeckBuilding/client/application/queries/isCatalogLoading/isCatalogLoading';

export const DeckDraftInitializer = () => {
  const dispatch = useDispatch<AppDispatch>();
  const initialized = useRef(false);
  const catalogLoaded = useSelector((state: RootState) => !isCatalogLoading(state));

  useEffect(() => {
    if (initialized.current) return;
    if (!catalogLoaded) return;
    dispatch(loadDeckDraft());
    initialized.current = true;
  }, [dispatch, catalogLoaded]);

  return null;
};
