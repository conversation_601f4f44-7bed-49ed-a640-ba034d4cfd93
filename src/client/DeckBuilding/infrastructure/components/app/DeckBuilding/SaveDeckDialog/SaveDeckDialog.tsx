'use client';

import {<PERSON><PERSON>, <PERSON>alog, <PERSON>lex, <PERSON>Field, VisuallyHidden} from '@radix-ui/themes';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import {FC, useState} from 'react';

type SaveDeckDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (name: string) => Promise<void> | void;
};

const SaveDeckDialog: FC<SaveDeckDialogProps> = ({open, onOpenChange, onSave}) => {
  const [name, setName] = useState('');

  const handleSave = async () => {
    await onSave(name);
    onOpenChange(false);
    setName('');
  };

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Content style={{width: 300}}>
        <DialogPrimitive.Title className="sr-only">
          <VisuallyHidden>Save deck</VisuallyHidden>
        </DialogPrimitive.Title>
        <DialogPrimitive.Description className="sr-only">
          <VisuallyHidden>Enter a name and save your deck</VisuallyHidden>
        </DialogPrimitive.Description>
        <Flex direction="column" gap="3">
          <TextField.Root placeholder="Deck name" value={name} onChange={e => setName(e.target.value)} autoFocus />
          <Flex justify="end" gap="2">
            <Button variant="soft" color="gray" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button color="indigo" onClick={handleSave} disabled={!name.trim()}>
              Save
            </Button>
          </Flex>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
};

export default SaveDeckDialog;
