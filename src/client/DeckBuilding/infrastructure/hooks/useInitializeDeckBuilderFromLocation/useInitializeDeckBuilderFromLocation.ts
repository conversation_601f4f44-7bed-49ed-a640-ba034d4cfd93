import {useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {initializeDeckBuilderFromLocation} from '@/src/DeckBuilding/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation';
import {AppDispatch} from '@/src/Shared/client/application/store/appStore';
import {getAvailableFilters} from '@/src/DeckBuilding/client/application/queries/getAvailableFilters/getAvailableFilters';

export const useInitializeDeckBuilderFromLocation = () => {
  const dispatch = useDispatch<AppDispatch>();
  const availableFilters = useSelector(getAvailableFilters);

  useEffect(() => {
    if (availableFilters.length > 0) {
      dispatch(initializeDeckBuilderFromLocation());
    }
  }, [dispatch, availableFilters]);
};
