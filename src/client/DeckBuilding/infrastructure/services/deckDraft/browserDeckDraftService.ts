import {DeckDraftService} from "@/src/DeckBuilding/client/application/services/DeckDraftService/DeckDraftService";
import {DeckDraft} from "@/src/DeckBuilding/client/domain/DeckBuilder/DeckDraft";

const STORAGE_KEY = 'deckBuilderDraft';

export class BrowserDeckDraftService implements DeckDraftService {
  loadDraft(): DeckDraft | null {
    if (typeof window === 'undefined') return null;
    const value = window.localStorage.getItem(STORAGE_KEY);
    return value ? JSON.parse(value) as DeckDraft : null;
  }

  saveDraft(draft: DeckDraft): void {
    if (typeof window === 'undefined') return;
    window.localStorage.setItem(STORAGE_KEY, JSON.stringify(draft));
  }

  clearDraft(): void {
    if (typeof window === 'undefined') return;
    window.localStorage.removeItem(STORAGE_KEY);
  }

  hasDraft(): boolean {
    if (typeof window === 'undefined') return false;
    return window.localStorage.getItem(STORAGE_KEY) !== null;
  }
}
