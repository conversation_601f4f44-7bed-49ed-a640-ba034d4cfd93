import {render, screen, fireEvent} from '@testing-library/react'
vi.mock('next/navigation', () => ({
  useSearchParams: () => new URLSearchParams('locale=en'),
}))
global.ResizeObserver = class {
  observe() {}
  unobserve() {}
  disconnect() {}
}
import GameDetailsPage from '@/src/Shared/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage'

describe('When using GameDetailsPage', () => {
  it('should display screenshots by default', () => {
    // Arrange
    render(<GameDetailsPage gameId="1" locale="en" />)

    // Act
    // no user action

    // Assert
    expect(screen.getAllByAltText(/Screenshot/)).toHaveLength(6)
  })

  it('should display videos when switching view', async () => {
    // Arrange
    render(<GameDetailsPage gameId="1" locale="en" />)

    // Act
    const tab = screen.getByRole('tab', {name: /Videos/})
    fireEvent.keyDown(tab, {key: 'Enter'})

    // Assert
    expect(screen.getByRole('tab', {name: /Videos/})).toHaveAttribute('data-state', 'active')
  })
})
