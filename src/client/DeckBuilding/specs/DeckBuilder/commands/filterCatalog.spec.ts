import {filterCatalog} from "@/src/DeckBuilding/client/application/commands/filterCatalog/filterCatalog";
import {createTestingStore} from "@/src/Shared/client/specs/helpers/store/createTestingStore";
import {getActiveFilters} from "@/src/DeckBuilding/client/application/queries/getActiveFilters/getActiveFilters";
import {FakeLocationService} from "@/src/Shared/client/specs/helpers/FakeLocationService";

describe('filterCatalog', () => {
  describe('When filters are applied', () => {
    it('should update the location with the active filters', async () => {
      // Arrange
      const locationService = new FakeLocationService();
      const {dispatch, getState} = createTestingStore({
        catalogFilters: {
          available: [
            {id: '1', name: 'INKABLE', text: 'Inkable', dataProperty: 'inkable', dataType: 'boolean', value: true, order: 1},
            {id: '2', name: 'CHARACTER', text: 'Character', dataProperty: 'type', dataType: 'string', value: 'CHARACTER', order: 2},
          ]
        }
      }, {locationService});

      // Act
      await dispatch(filterCatalog({filters: ['INKABLE']}));

      // Assert
      expect(getActiveFilters(getState())).toEqual(['INKABLE']);
      expect(locationService.getFilters()).toEqual(['INKABLE']);
    });
  });
});
