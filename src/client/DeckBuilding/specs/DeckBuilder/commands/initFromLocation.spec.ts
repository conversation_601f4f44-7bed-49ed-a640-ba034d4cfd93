import {initializeDeckBuilderFromLocation} from "@/src/DeckBuilding/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation";
import {createTestingStore} from "@/src/Shared/client/specs/helpers/store/createTestingStore";
import {getActiveFilters} from "@/src/DeckBuilding/client/application/queries/getActiveFilters/getActiveFilters";
import {FakeLocationService} from "@/src/Shared/client/specs/helpers/FakeLocationService";
import {getSearchTerm} from "@/src/DeckBuilding/client/application/queries/getSearchTerm/getSearchTerm";

describe('initializeDeckBuilderFromLocation', () => {
  describe('When the location contains filters', () => {
    it('should load the deck builder filters from the location', async () => {
      // Arrange
      const locationService = new FakeLocationService({filters: ['INKABLE']});
      const {dispatch, getState} = createTestingStore({
        catalogFilters: {
          available: [
            {id: '1', name: 'INKABLE', text: 'Inkable', dataProperty: 'inkable', dataType: 'boolean', value: true, order: 1},
          ]
        }
      }, {locationService});

      // Act
      await dispatch(initializeDeckBuilderFromLocation());

      // Assert
      expect(getActiveFilters(getState())).toEqual(['INKABLE']);
    });
  });
  describe('When the location contains a search term', () => {
    it('should load the search term from the location', async () => {
      // Arrange
      const locationService = new FakeLocationService({search: 'goofy'});
      const {dispatch, getState} = createTestingStore({}, {locationService});

      // Act
      await dispatch(initializeDeckBuilderFromLocation());

      // Assert
      expect(getSearchTerm(getState())).toBe('goofy');
    });
  });
});
