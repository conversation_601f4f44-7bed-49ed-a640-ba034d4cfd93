import {createTestingStore} from "@/src/Shared/client/specs/helpers/store/createTestingStore";
import {switchDeckBuilderView} from "@/src/DeckBuilding/client/application/commands/switchDeckBuilderView/switchDeckBuilderView";
import {getDeckBuilderView} from "@/src/DeckBuilding/client/application/queries/getDeckBuilderView/getDeckBuilderView";

import {FakeLocationService} from "@/src/Shared/client/specs/helpers/FakeLocationService";

describe('switchDeckBuilderView', () => {
  describe('When the view is switched to deck', () => {
    it('should update the current view', async () => {
      // Arrange
      const locationService = new FakeLocationService();
      const {dispatch, getState} = createTestingStore({}, {locationService});

      // Act
      await dispatch(switchDeckBuilderView({view: 'deck'}));

      // Assert
      expect(getDeckBuilderView(getState())).toBe('deck');
    });
  });

  describe('When the view is switched to catalog', () => {
    it('should update the current view', async () => {
      // Arrange
      const locationService = new FakeLocationService();
      const {dispatch, getState} = createTestingStore({ deckBuilder: { view: 'deck' } }, {locationService});

      // Act
      await dispatch(switchDeckBuilderView({view: 'catalog'}));

      // Assert
      expect(getDeckBuilderView(getState())).toBe('catalog');
    });
  });
});
