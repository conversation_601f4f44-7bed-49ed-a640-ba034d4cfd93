import {DeckBuilderCard} from "@/src/DeckBuilding/client/domain/DeckBuilder/DeckBuilderCard";

export const PINOCCHIO_STRINGS_ATTACHED: DeckBuilderCard = {
  id: "kn7d9mbgfv6j7zng3zr3a35dh17g5wk4",
  image: "1934.jpg",
  maxDeckQuantity: 4,
  minDeckQuantity: 0,
  name: "Pinocchio - Strings Attached",
  data: {
    cardId: 1934,
    color: "AMETHYST",
    cost: 4,
    inkable: true,
    lore: 2,
    strength: 0,
    subtypes: ["STORYBORN", "HERO"],
    type: "CHARACTER",
    willpower: 4,
  },
};

export const GOOFY_GROUNDBREAKING_CHEF: DeckBuilderCard = {
  id: "jb4n0g7lrm3t2d5a9wqv1xsyuec8hzkj",
  image: "1933.jpg",
  maxDeckQuantity: 4,
  minDeckQuantity: 0,
  name: "Goofy - Groundbreaking Chef",
  data: {
    cardId: 1933,
    color: "AMBER",
    cost: 4,
    inkable: true,
    lore: 2,
    strength: 3,
    subtypes: ["STORYBORN", "HERO"],
    type: "CHARACTER",
    willpower: 4,
  },
};

export const ELSA_ICE_MAKER: DeckBuilderCard = {
  id: "zo2d6cbx7f9v3mpwalqjtkhg5yenr04b",
  image: "1932.jpg",
  maxDeckQuantity: 4,
  minDeckQuantity: 0,
  name: "Elsa - Ice Maker",
  data: {
    cardId: 1932,
    color: "AMETHYST-SAPPHIRE",
    cost: 7,
    inkable: false,
    lore: 2,
    strength: 5,
    subtypes: ["FLOODBORN", "HERO", "QUEEN", "SORCERER"],
    type: "CHARACTER",
    willpower: 5,
  },
};

export const BOLT_SUPERDOG: DeckBuilderCard = {
  id: "nxpe8uz0bqf7mkylswgch1j3a29rtdv5",
  image: "1931.jpg",
  maxDeckQuantity: 4,
  minDeckQuantity: 0,
  name: "Bolt - Superdog",
  data: {
    cardId: 1931,
    color: "AMBER-STEEL",
    cost: 5,
    inkable: true,
    lore: 2,
    strength: 3,
    subtypes: ["FLOODBORN", "HERO"],
    type: "CHARACTER",
    willpower: 5,
  },
};

export const INK_MOAT_FIRST_LINE_OF_DEFENSE: DeckBuilderCard = {
  id: "tkx9ew3m0dch8rsvjqafl721n6py5zob",
  image: "1930.jpg",
  maxDeckQuantity: 4,
  minDeckQuantity: 0,
  name: "Ink Moat - First Line of Defense",
  data: {
    cardId: 1930,
    color: "AMETHYST",
    cost: 0,
    inkable: false,
    lore: 0,
    subtypes: ["OBSTACLE"],
    type: "LOCATION",
    willpower: 5,
  },
};
