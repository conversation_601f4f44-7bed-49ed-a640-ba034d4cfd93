import {DeckDraftService} from "@/src/DeckBuilding/client/application/services/DeckDraftService/DeckDraftService";
import {DeckDraft} from "@/src/DeckBuilding/client/domain/DeckBuilder/DeckDraft";

export class FakeDeckDraftService implements DeckDraftService {
  private draft: DeckDraft | null;

  constructor(draft: DeckDraft | null = null) {
    this.draft = draft;
  }

  loadDraft(): DeckDraft | null {
    return this.draft;
  }

  saveDraft(draft: DeckDraft): void {
    this.draft = draft;
  }

  clearDraft(): void {
    this.draft = null;
  }

  hasDraft(): boolean {
    return this.draft !== null;
  }
}
