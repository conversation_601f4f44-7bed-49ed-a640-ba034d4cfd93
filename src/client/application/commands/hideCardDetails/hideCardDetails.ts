import {createAsyncThunk} from "@reduxjs/toolkit";
import {HIDE_CARD_DETAILS_EVENT_TYPE} from "@/src/client/domain/DeckBuilder/deckBuilderEvents";
import {DeckBuilder} from "@/src/client/domain/DeckBuilder/DeckBuilder";
import {RootState} from "@/src/client/application/store/appStore";

export const hideCardDetails = createAsyncThunk<void, void, { state: RootState }>(
  HIDE_CARD_DETAILS_EVENT_TYPE,
  async (_, {dispatch, getState}) => {
    const deckBuilder = DeckBuilder.fromState(getState().deckBuilder);
    deckBuilder.hideCardDetails();
    deckBuilder.getDomainEvents().forEach(dispatch);
  }
);