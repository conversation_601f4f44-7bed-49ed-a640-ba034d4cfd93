import {
  catalogCardsLoadedEvent,
  catalogCardsLoadingFailedEvent,
  catalogCardsLoadingStartedEvent
} from "@/src/client/domain/Catalog/catalogEvents";
import {AppDispatch} from "@/src/client/application/store/appStore";
import {LoadCatalogCardsRequest} from "@/src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest";

export const loadCatalogCards = (request: LoadCatalogCardsRequest) => (dispatch: AppDispatch): void => {
  if (!request) {
    dispatch(catalogCardsLoadingStartedEvent());
    return;
  }

  if (request.error) {
    dispatch(catalogCardsLoadingFailedEvent({error: request.error}));
    return;
  }

  const cards = request.data?.cards || [];
  dispatch(catalogCardsLoadedEvent({cards}));
};