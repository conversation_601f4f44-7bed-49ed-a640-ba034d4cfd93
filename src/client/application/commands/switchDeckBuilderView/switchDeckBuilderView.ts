import {createAsyncThunk} from "@reduxjs/toolkit";
import {RootState, ThunkExtra} from "@/src/client/application/store/appStore";
import {SWITCH_DECK_BUILDER_VIEW_EVENT_TYPE} from "@/src/client/domain/DeckBuilder/deckBuilderEvents";
import {DeckBuilder} from "@/src/client/domain/DeckBuilder/DeckBuilder";
import {SwitchDeckBuilderViewRequest} from "@/src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest";

export const switchDeckBuilderView = createAsyncThunk<void, SwitchDeckBuilderViewRequest, { state: RootState; extra: ThunkExtra }>(
  SWITCH_DECK_BUILDER_VIEW_EVENT_TYPE,
  async ({view}, {dispatch, getState}) => {
    const deckBuilder = DeckBuilder.fromState(getState().deckBuilder);
    deckBuilder.switchView(view);
    deckBuilder.getDomainEvents().forEach(dispatch);
  }
);
