import {AppDispatch} from "@/src/client/application/store/appStore";
import {availableFiltersUpdatedEvent} from "@/src/client/domain/Catalog/catalogFilterEvents";
import {
  UpdateAvailableFiltersRequest
} from "@/src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest";

export const updateAvailableFilters = ({filters}: UpdateAvailableFiltersRequest) => (dispatch: AppDispatch): void => {
  dispatch(availableFiltersUpdatedEvent({filters}));
};