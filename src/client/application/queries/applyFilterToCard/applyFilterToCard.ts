import {Filter} from '@/src/client/domain/Catalog/Filter';
import {CardData} from "@/src/client/domain/CardData/CardData.";

export function applyFilterToCard(card: { data: CardData }, filter: Filter): boolean {
  const raw = card.data?.[filter.dataProperty];

  if (filter.dataType === 'string') {
    return applyStringFilter(raw, filter.value);
  }

  if (filter.dataType === 'number') {
    return applyNumberFilter(raw, filter.value);
  }

  if (filter.dataType === 'boolean') {
    return applyBooleanFilter(raw, filter.value);
  }

  if (filter.dataType === 'string[]') {
    return applyStringArrayFilter(raw, filter.value);
  }

  return false;
}

function applyStringFilter(raw: unknown, value: string): boolean {
  if (typeof raw !== 'string') return false;
  return raw === value || raw.split('-').includes(value);
}

function applyNumberFilter(raw: unknown, value: number): boolean {
  return raw === value;
}

function applyBooleanFilter(raw: unknown, value: boolean): boolean {
  return raw === value;
}

function applyStringArrayFilter(raw: unknown, value: string): boolean {
  return Array.isArray(raw) && raw.includes(value);
}