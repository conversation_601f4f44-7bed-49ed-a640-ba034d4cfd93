import {RootState} from "@/src/client/application/store/appStore";
import {createSelector} from "reselect";

export const getCardDetails = createSelector(
  (state: RootState) => state.deckBuilder.cardDetailsDisplayed,
  (cardDetailsDisplayed) => {
    if (!cardDetailsDisplayed) {
      return null;
    }

    return {
      cardImage: cardDetailsDisplayed.cardImage,
      cardName: cardDetailsDisplayed.cardName,
    };
  }
);