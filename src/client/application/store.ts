import type {AnyAction, ThunkDispatch} from '@reduxjs/toolkit';
import type {DeckBuilderState} from '@/src/client/domain/DeckBuilder/deckBuilderReducer';
import type {CatalogState} from '@/src/client/domain/Catalog/catalogReducer';
import type {CatalogFilterState} from '@/src/client/domain/Catalog/catalogFiltersReducer';
import type {CatalogSearchState} from '@/src/client/domain/Catalog/catalogSearchReducer';
import type {GameSettingsState} from '@/src/client/domain/GameSettings/gameSettingsReducer';
import type {LocationService} from './services/LocationService';
import type {DeckDraftService} from './services/DeckDraftService';

export interface RootState {
  deckBuilder: DeckBuilderState;
  catalog: CatalogState;
  catalogFilters: CatalogFilterState;
  catalogSearch: CatalogSearchState;
  gameSettings: GameSettingsState;
}

export type ThunkExtra = {
  locationService: LocationService;
  deckDraftService: DeckDraftService;
};

export type AppDispatch = ThunkDispatch<RootState, ThunkExtra, AnyAction>;
