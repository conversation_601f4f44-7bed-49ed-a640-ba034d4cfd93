'use client';

import {useEffect, useRef} from 'react';
import {store as appStore} from '@/src/client/domain/store';
import {subscribeToDeckDraft} from '@/src/client/application/subscribers/subscribeToDeckDraft';

export const DeckDraftPersistenceInitializer = () => {
  const initialized = useRef(false);

  useEffect(() => {
    if (initialized.current) return;
    if (typeof window === 'undefined') return;

    const unsubscribe = subscribeToDeckDraft(appStore);
    initialized.current = true;

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  return null;
};
