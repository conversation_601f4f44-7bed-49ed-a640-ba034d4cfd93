'use client';

import {useEffect, useRef} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch, RootState} from '@/src/client/application/store/appStore';
import {deckDraftService} from '@/src/client/infrastructure/store/store';
import {loadDeckIntoBuilder} from '@/src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder';
import {getCatalogCardById} from '@/src/client/application/queries/getCatalogCardById/getCatalogCardById';
import {isCatalogLoading} from '@/src/client/application/queries/isCatalogLoading/isCatalogLoading';
import {hasDeckDraft} from '@/src/client/application/queries/hasDeckDraft/hasDeckDraft';

export function EditDeckInitializer({deck}: {deck: {name: string; cards: {cardId: string; quantity: number}[]} | null | undefined}) {
  const dispatch = useDispatch<AppDispatch>();
  const initialized = useRef(false);
  const catalogLoaded = useSelector((state: RootState) => !isCatalogLoading(state));
  const allCardsAvailable = useSelector((state: RootState) =>
    deck ? deck.cards.every(({cardId}) => !!getCatalogCardById(state, cardId)) : false
  );

  useEffect(() => {
    if (initialized.current) return;
    if (!deck) return;
    if (!catalogLoaded) return;
    if (!allCardsAvailable) return;
    if (hasDeckDraft(deckDraftService)) return;
    if (deck.cards.length === 0) return;
    dispatch(loadDeckIntoBuilder({deck}));
    initialized.current = true;
  }, [dispatch, catalogLoaded, allCardsAvailable, deck]);

  return null;
}
