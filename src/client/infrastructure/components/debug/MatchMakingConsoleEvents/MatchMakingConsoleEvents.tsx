'use client';

import {useQuery} from "convex/react";
import {api} from "@/convex/_generated/api";
import {Id} from "@/convex/_generated/dataModel";
import {Badge, Card, Code, Flex} from "@radix-ui/themes";

type Props = {
  gameId: Id<"games">;
};

export const MatchMakingConsoleEvents = ({gameId}: Props) => {
  const events = useQuery(api.queries.debug.matchmakingEventsForGame, {gameId});

  return (
    <Card>
      <Flex direction="column" gap="1">
        {!events && <div>Loading...</div>}
        {events?.length === 0 && <div>No events</div>}
        {events?.slice().map((event) => (
          <Flex key={event._id} align="center" gap="3">
            <Badge variant="outline" size="2">{new Date(event._creationTime).toLocaleTimeString()}</Badge>
            <Badge variant="outline" size="2">{event.type}</Badge>
            <Code>
              {JSON.stringify(event.payload, null, 2)}
            </Code>
          </Flex>
        ))}
      </Flex>
    </Card>
  );
};

export default MatchMakingConsoleEvents;
