import {render, fireEvent, screen, waitFor} from '@testing-library/react';
import {Provider} from 'react-redux';
import {createTestingStore} from '@/src/client/specs/helpers/store/createTestingStore';
import {FakeDeckDraftService} from '@/src/client/specs/helpers/fakes/FakeDeckDraftService';
import DeckBuilderPanel from '@/src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel';
import {vi, Mock} from 'vitest';

vi.mock('next/navigation', () => ({
  useSearchParams: () => new URLSearchParams('locale=en&filters=INKABLE'),
  useRouter: vi.fn(),
  useParams: vi.fn(),
}));

import {useParams, useRouter} from 'next/navigation';
import type {AppRouterInstance} from 'next/dist/shared/lib/app-router-context.shared-runtime';

const saveMock = vi.fn();
const updateMock = vi.fn();

vi.mock('convex/react', () => ({
  useMutation: (endpoint: string) => {
    if (endpoint === 'save') return saveMock;
    return updateMock;
  },
}));

vi.mock('@/src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId', () => ({
  useGameSettingsByGameId: () => ({settings: {maxCardsInDeck: 60}, isLoading: false, error: null})
}));

vi.mock('@/convex/_generated/api', () => ({
  api: {mutations: {saveDeck: {endpoint: 'save'}, updateDeck: {endpoint: 'update'}}},
}));

global.ResizeObserver = class {
  observe() {}
  unobserve() {}
  disconnect() {}
};


describe('When using DeckBuilderPanel', () => {
  describe('When creating a deck', () => {
    it('should display a draft indicator when a draft exists', async () => {
      // Arrange
      (useParams as Mock).mockReturnValue({gameId: 'g1'});
      const store = createTestingStore({}, {deckDraftService: new FakeDeckDraftService({name: 'draft', cards: []})});
      render(
        <Provider store={store}>
          <DeckBuilderPanel />
        </Provider>
      );

      // Act
      await waitFor(() => {
        expect(screen.getByRole('button', {name: /Save this Deck\*/i})).toBeInTheDocument();
      });
      // Assert done in waitFor
    });
    it('should open the save dialog', () => {
      // Arrange
      (useParams as Mock).mockReturnValue({gameId: 'g1'});
      const store = createTestingStore();
      render(
        <Provider store={store}>
          <DeckBuilderPanel />
        </Provider>
      );

      // Act
      fireEvent.click(screen.getByRole('button', {name: /Save this Deck/i}));

      // Assert
      expect(screen.getByPlaceholderText('Deck name')).toBeInTheDocument();
      expect(saveMock).not.toHaveBeenCalled();
    });


    it('should save the deck and redirect to the edit url', async () => {
      // Arrange
      (useParams as Mock).mockReturnValue({gameId: 'g1'});
      const routerReplace = vi.fn();
      saveMock.mockResolvedValue('d1');
      vi.mocked(useRouter).mockReturnValue({
        back: vi.fn(),
        forward: vi.fn(),
        refresh: vi.fn(),
        push: vi.fn(),
        replace: routerReplace,
        prefetch: vi.fn(),
      } as unknown as AppRouterInstance);
      window.history.replaceState({}, '', '/en/games/g1/deck-builder?filters=INKABLE');
      const store = createTestingStore();
      render(
        <Provider store={store}>
          <DeckBuilderPanel />
        </Provider>
      );

      // Act
      fireEvent.click(screen.getByRole('button', {name: /Save this Deck/i}));
      fireEvent.change(screen.getByPlaceholderText('Deck name'), {target: {value: 'My deck'}});
      await fireEvent.click(screen.getByRole('button', {name: 'Save'}));

      // Assert
      expect(routerReplace).toHaveBeenCalledWith(
        '/en/games/g1/deck-builder/d1?filters=INKABLE'
      );
    });
  });

  describe('When editing a deck', () => {
    it('should update the deck', () => {
      // Arrange
      (useParams as Mock).mockReturnValue({gameId: 'g1', deckId: 'd1'});
      const store = createTestingStore({deckBuilder: {name: 'My deck'}});
      render(
        <Provider store={store}>
          <DeckBuilderPanel />
        </Provider>
      );

      // Act
      fireEvent.click(screen.getByRole('button', {name: /Save this Deck/i}));

      // Assert
      expect(updateMock).toHaveBeenCalledWith({deckId: 'd1', name: 'My deck', cards: []});
    });
  });
});
