import {render, act} from '@testing-library/react';
import {Provider} from 'react-redux';
import {createTestingStore} from '@/src/client/specs/createTestingStore';
import {DeckDraftPersistenceInitializer} from '@/src/client/infrastructure/components/app/DeckBuilding/DeckDraftPersistenceInitializer/DeckDraftPersistenceInitializer';
import {FakeDeckDraftService} from '@/src/client/specs/fakes/FakeDeckDraftService';

describe('DeckDraftPersistenceInitializer', () => {
  describe('When mounted in browser environment', () => {
    it('should render without errors', async () => {
      // Arrange
      const deckDraftService = new FakeDeckDraftService();
      const store = createTestingStore({}, {deckDraftService});

      // Act & Assert - should not throw
      await act(async () => {
        expect(() => {
          render(
            <Provider store={store}>
              <DeckDraftPersistenceInitializer />
            </Provider>
          );
        }).not.toThrow();
      });
    });
  });
});
