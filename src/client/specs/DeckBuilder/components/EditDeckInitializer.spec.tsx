import {render, act, waitFor} from '@testing-library/react';
import {Provider} from 'react-redux';
import {createTestingStore} from '@/src/client/specs/helpers/store/createTestingStore';
import {EditDeckInitializer} from '@/src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer';
import {PINOCCHIO_STRINGS_ATTACHED} from '@/src/client/specs/helpers/factories/fakeCatalogCards';
import {FakeDeckDraftService} from "@/src/client/specs/helpers/fakes/FakeDeckDraftService";
import {loadCatalogCards} from '@/src/client/application/commands/loadCatalogCards/loadCatalogCards';
import {LoadCatalogCardsRequest} from '@/src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest';

describe('When EditDeckInitializer mounts', () => {
  it('should load the deck when catalog is already available', async () => {
    // Arrange
    const store = createTestingStore({
      catalog: {
        cards: { [PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED },
        status: 'success'
      }
    });

    // Act
    await act(async () => {
      render(
        <Provider store={store}>
          <EditDeckInitializer deck={{name: 'Deck', cards: [{cardId: PINOCCHIO_STRINGS_ATTACHED.id, quantity: 2}]}} />
        </Provider>
      );
    });

    await waitFor(() => {
      expect(store.getState().deckBuilder.cardsInDeck[PINOCCHIO_STRINGS_ATTACHED.id].quantity).toBe(2);
    });
    // Assert done in waitFor
  });

  it('should load the deck after catalog cards are loaded', async () => {
    // Arrange
    const store = createTestingStore();
    await act(async () => {
      render(
        <Provider store={store}>
          <EditDeckInitializer deck={{name: 'Deck', cards: [{cardId: PINOCCHIO_STRINGS_ATTACHED.id, quantity: 1}]}} />
        </Provider>
      );
    });

    // Act
    const request: LoadCatalogCardsRequest = {error: null, data: {cards: [PINOCCHIO_STRINGS_ATTACHED]}};
    await act(async () => {
      loadCatalogCards(request)(store.dispatch);
    });

    await waitFor(() => {
      expect(store.getState().deckBuilder.cardsInDeck[PINOCCHIO_STRINGS_ATTACHED.id].quantity).toBe(1);
    });
  });
});

describe('When a draft already exists', () => {
  it('should not load the deck from the server', async () => {
    // Arrange
    const store = createTestingStore(
      {
        catalog: {
          cards: {[PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED},
          status: 'success',
        },
      },
      {deckDraftService: new FakeDeckDraftService({
        name: 'Draft',
        cards: [{cardId: PINOCCHIO_STRINGS_ATTACHED.id, quantity: 2}],
      })},
    );

    // Act
    await act(async () => {
      render(
        <Provider store={store}>
          <EditDeckInitializer deck={{name: 'Deck', cards: [{cardId: PINOCCHIO_STRINGS_ATTACHED.id, quantity: 1}]}} />
        </Provider>
      );
    });

    // Assert
    expect(store.getState().deckBuilder.cardsInDeck).toEqual({});
  });
});
