import {buildEditDeckUrl, buildEditDeckUrlWithQuery} from '@/src/client/infrastructure/builders/urlBuilder';

describe('When building deck urls', () => {
  it('should create the edit deck url', () => {
    // Arrange
    const locale = 'en';
    const gameId = 'g1';
    const deckId = 'd1';

    // Act
    const url = buildEditDeckUrl(locale, gameId, deckId);

    // Assert
    expect(url).toBe('/en/games/g1/deck-builder/d1');
  });

  it('should append the query string when provided', () => {
    // Arrange
    const locale = 'en';
    const gameId = 'g1';
    const deckId = 'd1';
    const query = '?filters=INKABLE';

    // Act
    const url = buildEditDeckUrlWithQuery(locale, gameId, deckId, query);

    // Assert
    expect(url).toBe('/en/games/g1/deck-builder/d1?filters=INKABLE');
  });
});
