import {LocationService} from "@/src/client/application/services/LocationService";

export class FakeLocationService implements LocationService {
  private filters: string[];
  private search: string;

  constructor(init: Partial<{ filters: string[]; search: string }> = {}) {
    this.filters = init.filters ?? [];
    this.search = init.search ?? '';
  }

  getFilters(): string[] {
    return this.filters;
  }

  setFilters(filters: string[]): void {
    this.filters = filters;
  }

  getSearch(): string {
    return this.search;
  }

  setSearch(search: string): void {
    this.search = search;
  }
}
