import {SaveDeckCommand} from "./SaveDeckCommand";
import {DeckRepository} from "@/src/DeckBuilding/server/application/ports/DeckRepository";
import {Deck} from "@/src/DeckBuilding/server/domain/Deck/Deck";
export class SaveDeckCommandHandler {
  private readonly repository: DeckRepository;
  constructor(repository: DeckRepository) {
    this.repository = repository;
  }

  handle(command: SaveDeckCommand) {
    return this.repository.save(
      Deck.create({
        gameId: command.gameId,
        playerId: command.playerId,
        name: command.name,
        cards: command.cards,
      })
    );
  }
}
