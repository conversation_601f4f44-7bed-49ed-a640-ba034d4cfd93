import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {createFakeGame} from "@/src/Shared/server/specs/helpers/createFakeGame";
import {api} from "@/convex/_generated/api";
import {createAppUser} from "@/src/Authentication/server/specs/helpers/createAppUsers";
import {ADMIN_IDENTITY, SOPHIE_APP_USER, SOPHIE_IDENTITY} from "@/src/Authentication/server/specs/helpers/fakes/fakeUsers";
import {LORCANA} from "@/src/Shared/server/specs/helpers/fakes/fakeGames";

describe('loadCatalogCardsByGameId', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    asSophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    createAppUser(asAdmin, SOPHIE_APP_USER);
  });

  describe('When cards exist for the game', () => {
    it('should return them', async () => {
      // Arrange
      const gameId = await createFakeGame(LORCANA, asAdmin) as Id<'games'>;
      await asAdmin.run(ctx => ctx.db.insert('catalogCards', {
        gameId,
        name: 'Card 1',
        image: 'img',
        language: 'en',
        minDeckQuantity: 1,
        maxDeckQuantity: 4,
        data: {},
      }));

      // Act
      const result = await loadCards(asSophie, gameId);

      // Assert
      expect(result.data!.cards).toHaveLength(1);
      expect(result.data!.cards[0].name).toBe('Card 1');
    });
  });

  describe('When no cards exist for the game', () => {
    it('should return an empty list', async () => {
      // Arrange
      const gameId = await createFakeGame(LORCANA, asAdmin) as Id<'games'>;

      // Act
      const result = await loadCards(asSophie, gameId);

      // Assert
      expect(result.data!.cards).toEqual([]);
    });
  });

  function loadCards(user: TestConvexForDataModel<DataModel>, gameId: Id<'games'>) {
    return user.query(api.queries.loadCatalogCardsByGameId.endpoint, {gameId});
  }
});
