<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="utf-8" />
    <title>dependency graph</title>
    <style>
      .node:active path,
.node:hover path,
.node.current path,
.node:active polygon,
.node:hover polygon,
.node.current polygon {
  stroke: fuchsia;
  stroke-width: 2;
}

.edge:active path,
.edge:hover path,
.edge.current path,
.edge:active ellipse,
.edge:hover ellipse,
.edge.current ellipse {
  stroke: url(#edgeGradient);
  stroke-width: 3;
  stroke-opacity: 1;
}

.edge:active polygon,
.edge:hover polygon,
.edge.current polygon {
  stroke: fuchsia;
  stroke-width: 3;
  fill: fuchsia;
  stroke-opacity: 1;
  fill-opacity: 1;
}

.edge:active text,
.edge:hover text {
  fill: fuchsia;
}

.cluster path {
  stroke-width: 3;
}

.cluster:active path,
.cluster:hover path {
  fill: #ffff0011;
}

div.hint {
  background-color: #000000aa;
  color: white;
  font-family: Arial, Helvetica, sans-serif;
  border-radius: 1rem;
  position: fixed;
  top: calc(50% - 4em);
  right: calc(50% - 10em);
  border: none;
  padding: 1em 3em 1em 1em;
}

.hint button {
  position: absolute;
  font-weight: bolder;
  right: 0.6em;
  top: 0.6em;
  color: inherit;
  background-color: inherit;
  border: 1px solid currentColor;
  border-radius: 1em;
  margin-left: 0.6em;
}

.hint a {
  color: inherit;
}

#button_help {
  color: white;
  background-color: #00000011;
  border-radius: 1em;
  position: fixed;
  top: 1em;
  right: 1em;
  font-size: 24pt;
  font-weight: bolder;
  width: 2em;
  height: 2em;
  border: none;
}

#button_help:hover {
  cursor: pointer;
  background-color: #00000077;
}

@media print {
  #button_help {
    display: none;
  }

  div.hint {
    display: none;
  }
}

    </style>
  </head>
  <body>
    <button id="button_help">?</button>
    <div id="hints" class="hint" style="display: none">
      <button id="close-hints">x</button>
      <span id="hint-text"></span>
      <ul>
        <li><b>Hover</b> - highlight</li>
        <li><b>Right-click</b> - pin highlight</li>
        <li><b>ESC</b> - clear</li>
      </ul>
    </div>
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.0 (20241103.1931)
 -->
<!-- Title: dependency&#45;cruiser output Pages: 1 -->
<svg width="1983pt" height="6364pt"
 viewBox="0.00 0.00 1983.12 6364.28" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 6360.28)">
<title>dependency&#45;cruiser output</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-6360.28 1979.12,-6360.28 1979.12,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster_app</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M20,-5522.28C20,-5522.28 310.38,-5522.28 310.38,-5522.28 316.38,-5522.28 322.38,-5528.28 322.38,-5534.28 322.38,-5534.28 322.38,-6336.28 322.38,-6336.28 322.38,-6342.28 316.38,-6348.28 310.38,-6348.28 310.38,-6348.28 20,-6348.28 20,-6348.28 14,-6348.28 8,-6342.28 8,-6336.28 8,-6336.28 8,-5534.28 8,-5534.28 8,-5528.28 14,-5522.28 20,-5522.28"/>
<text text-anchor="middle" x="165.19" y="-6335.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">app</text>
</g>
<g id="clust2" class="cluster">
<title>cluster_app/[locale]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M28,-5587.28C28,-5587.28 160.25,-5587.28 160.25,-5587.28 166.25,-5587.28 172.25,-5593.28 172.25,-5599.28 172.25,-5599.28 172.25,-6309.28 172.25,-6309.28 172.25,-6315.28 166.25,-6321.28 160.25,-6321.28 160.25,-6321.28 28,-6321.28 28,-6321.28 22,-6321.28 16,-6315.28 16,-6309.28 16,-6309.28 16,-5599.28 16,-5599.28 16,-5593.28 22,-5587.28 28,-5587.28"/>
<text text-anchor="middle" x="94.12" y="-6308.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[locale]</text>
</g>
<g id="clust3" class="cluster">
<title>cluster_app/[locale]/(connected)</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M36,-5804.28C36,-5804.28 152.25,-5804.28 152.25,-5804.28 158.25,-5804.28 164.25,-5810.28 164.25,-5816.28 164.25,-5816.28 164.25,-6282.28 164.25,-6282.28 164.25,-6288.28 158.25,-6294.28 152.25,-6294.28 152.25,-6294.28 36,-6294.28 36,-6294.28 30,-6294.28 24,-6288.28 24,-6282.28 24,-6282.28 24,-5816.28 24,-5816.28 24,-5810.28 30,-5804.28 36,-5804.28"/>
<text text-anchor="middle" x="94.12" y="-6281.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">(connected)</text>
</g>
<g id="clust4" class="cluster">
<title>cluster_app/[locale]/(connected)/games</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M44,-5908.28C44,-5908.28 144.25,-5908.28 144.25,-5908.28 150.25,-5908.28 156.25,-5914.28 156.25,-5920.28 156.25,-5920.28 156.25,-6255.28 156.25,-6255.28 156.25,-6261.28 150.25,-6267.28 144.25,-6267.28 144.25,-6267.28 44,-6267.28 44,-6267.28 38,-6267.28 32,-6261.28 32,-6255.28 32,-6255.28 32,-5920.28 32,-5920.28 32,-5914.28 38,-5908.28 44,-5908.28"/>
<text text-anchor="middle" x="94.12" y="-6254.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">games</text>
</g>
<g id="clust5" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M52,-5942.28C52,-5942.28 136.25,-5942.28 136.25,-5942.28 142.25,-5942.28 148.25,-5948.28 148.25,-5954.28 148.25,-5954.28 148.25,-6228.28 148.25,-6228.28 148.25,-6234.28 142.25,-6240.28 136.25,-6240.28 136.25,-6240.28 52,-6240.28 52,-6240.28 46,-6240.28 40,-6234.28 40,-6228.28 40,-6228.28 40,-5954.28 40,-5954.28 40,-5948.28 46,-5942.28 52,-5942.28"/>
<text text-anchor="middle" x="94.12" y="-6227.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[gameId]</text>
</g>
<g id="clust6" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/deck&#45;builder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M60,-6037.28C60,-6037.28 128.25,-6037.28 128.25,-6037.28 134.25,-6037.28 140.25,-6043.28 140.25,-6049.28 140.25,-6049.28 140.25,-6201.28 140.25,-6201.28 140.25,-6207.28 134.25,-6213.28 128.25,-6213.28 128.25,-6213.28 60,-6213.28 60,-6213.28 54,-6213.28 48,-6207.28 48,-6201.28 48,-6201.28 48,-6049.28 48,-6049.28 48,-6043.28 54,-6037.28 60,-6037.28"/>
<text text-anchor="middle" x="94.12" y="-6200.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">deck&#45;builder</text>
</g>
<g id="clust7" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M68,-6102.28C68,-6102.28 120.25,-6102.28 120.25,-6102.28 126.25,-6102.28 132.25,-6108.28 132.25,-6114.28 132.25,-6114.28 132.25,-6174.28 132.25,-6174.28 132.25,-6180.28 126.25,-6186.28 120.25,-6186.28 120.25,-6186.28 68,-6186.28 68,-6186.28 62,-6186.28 56,-6180.28 56,-6174.28 56,-6174.28 56,-6114.28 56,-6114.28 56,-6108.28 62,-6102.28 68,-6102.28"/>
<text text-anchor="middle" x="94.12" y="-6173.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[deckId]</text>
</g>
<g id="clust8" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/play</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M71.12,-5950.28C71.12,-5950.28 117.12,-5950.28 117.12,-5950.28 123.12,-5950.28 129.12,-5956.28 129.12,-5962.28 129.12,-5962.28 129.12,-5991.28 129.12,-5991.28 129.12,-5997.28 123.12,-6003.28 117.12,-6003.28 117.12,-6003.28 71.12,-6003.28 71.12,-6003.28 65.12,-6003.28 59.12,-5997.28 59.12,-5991.28 59.12,-5991.28 59.12,-5962.28 59.12,-5962.28 59.12,-5956.28 65.12,-5950.28 71.12,-5950.28"/>
<text text-anchor="middle" x="94.12" y="-5990.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">play</text>
</g>
<g id="clust9" class="cluster">
<title>cluster_app/[locale]/(connected)/matches</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M63.12,-5812.28C63.12,-5812.28 125.12,-5812.28 125.12,-5812.28 131.12,-5812.28 137.12,-5818.28 137.12,-5824.28 137.12,-5824.28 137.12,-5888.28 137.12,-5888.28 137.12,-5894.28 131.12,-5900.28 125.12,-5900.28 125.12,-5900.28 63.12,-5900.28 63.12,-5900.28 57.12,-5900.28 51.12,-5894.28 51.12,-5888.28 51.12,-5888.28 51.12,-5824.28 51.12,-5824.28 51.12,-5818.28 57.12,-5812.28 63.12,-5812.28"/>
<text text-anchor="middle" x="94.12" y="-5887.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">matches</text>
</g>
<g id="clust10" class="cluster">
<title>cluster_app/[locale]/(connected)/matches/[matchId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M71.12,-5820.28C71.12,-5820.28 117.12,-5820.28 117.12,-5820.28 123.12,-5820.28 129.12,-5826.28 129.12,-5832.28 129.12,-5832.28 129.12,-5861.28 129.12,-5861.28 129.12,-5867.28 123.12,-5873.28 117.12,-5873.28 117.12,-5873.28 71.12,-5873.28 71.12,-5873.28 65.12,-5873.28 59.12,-5867.28 59.12,-5861.28 59.12,-5861.28 59.12,-5832.28 59.12,-5832.28 59.12,-5826.28 65.12,-5820.28 71.12,-5820.28"/>
<text text-anchor="middle" x="94.12" y="-5860.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[matchId]</text>
</g>
<g id="clust11" class="cluster">
<title>cluster_app/[locale]/(not&#45;connected)</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M63.12,-5621.28C63.12,-5621.28 125.12,-5621.28 125.12,-5621.28 131.12,-5621.28 137.12,-5627.28 137.12,-5633.28 137.12,-5633.28 137.12,-5723.28 137.12,-5723.28 137.12,-5729.28 131.12,-5735.28 125.12,-5735.28 125.12,-5735.28 63.12,-5735.28 63.12,-5735.28 57.12,-5735.28 51.12,-5729.28 51.12,-5723.28 51.12,-5723.28 51.12,-5633.28 51.12,-5633.28 51.12,-5627.28 57.12,-5621.28 63.12,-5621.28"/>
<text text-anchor="middle" x="94.12" y="-5722.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">(not&#45;connected)</text>
</g>
<g id="clust12" class="cluster">
<title>cluster_app/[locale]/(not&#45;connected)/signin</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M71.12,-5655.28C71.12,-5655.28 117.12,-5655.28 117.12,-5655.28 123.12,-5655.28 129.12,-5661.28 129.12,-5667.28 129.12,-5667.28 129.12,-5696.28 129.12,-5696.28 129.12,-5702.28 123.12,-5708.28 117.12,-5708.28 117.12,-5708.28 71.12,-5708.28 71.12,-5708.28 65.12,-5708.28 59.12,-5702.28 59.12,-5696.28 59.12,-5696.28 59.12,-5667.28 59.12,-5667.28 59.12,-5661.28 65.12,-5655.28 71.12,-5655.28"/>
<text text-anchor="middle" x="94.12" y="-5695.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">signin</text>
</g>
<g id="clust13" class="cluster">
<title>cluster_app/[locale]/access&#45;denied</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M67.12,-5743.28C67.12,-5743.28 122.12,-5743.28 122.12,-5743.28 128.12,-5743.28 134.12,-5749.28 134.12,-5755.28 134.12,-5755.28 134.12,-5784.28 134.12,-5784.28 134.12,-5790.28 128.12,-5796.28 122.12,-5796.28 122.12,-5796.28 67.12,-5796.28 67.12,-5796.28 61.12,-5796.28 55.12,-5790.28 55.12,-5784.28 55.12,-5784.28 55.12,-5755.28 55.12,-5755.28 55.12,-5749.28 61.12,-5743.28 67.12,-5743.28"/>
<text text-anchor="middle" x="94.62" y="-5783.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">access&#45;denied</text>
</g>
<g id="clust14" class="cluster">
<title>cluster_src</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M192.25,-56.28C192.25,-56.28 1955.12,-56.28 1955.12,-56.28 1961.12,-56.28 1967.12,-62.28 1967.12,-68.28 1967.12,-68.28 1967.12,-5502.28 1967.12,-5502.28 1967.12,-5508.28 1961.12,-5514.28 1955.12,-5514.28 1955.12,-5514.28 192.25,-5514.28 192.25,-5514.28 186.25,-5514.28 180.25,-5508.28 180.25,-5502.28 180.25,-5502.28 180.25,-68.28 180.25,-68.28 180.25,-62.28 186.25,-56.28 192.25,-56.28"/>
<text text-anchor="middle" x="1073.69" y="-5501.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">src</text>
</g>
<g id="clust15" class="cluster">
<title>cluster_src/client</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M200.25,-64.28C200.25,-64.28 1947.12,-64.28 1947.12,-64.28 1953.12,-64.28 1959.12,-70.28 1959.12,-76.28 1959.12,-76.28 1959.12,-5475.28 1959.12,-5475.28 1959.12,-5481.28 1953.12,-5487.28 1947.12,-5487.28 1947.12,-5487.28 200.25,-5487.28 200.25,-5487.28 194.25,-5487.28 188.25,-5481.28 188.25,-5475.28 188.25,-5475.28 188.25,-76.28 188.25,-76.28 188.25,-70.28 194.25,-64.28 200.25,-64.28"/>
<text text-anchor="middle" x="1073.69" y="-5474.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">client</text>
</g>
<g id="clust16" class="cluster">
<title>cluster_src/client/application</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M779,-613.28C779,-613.28 1597.62,-613.28 1597.62,-613.28 1603.62,-613.28 1609.62,-619.28 1609.62,-625.28 1609.62,-625.28 1609.62,-2842.28 1609.62,-2842.28 1609.62,-2848.28 1603.62,-2854.28 1597.62,-2854.28 1597.62,-2854.28 779,-2854.28 779,-2854.28 773,-2854.28 767,-2848.28 767,-2842.28 767,-2842.28 767,-625.28 767,-625.28 767,-619.28 773,-613.28 779,-613.28"/>
<text text-anchor="middle" x="1188.31" y="-2841.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">application</text>
</g>
<g id="clust17" class="cluster">
<title>cluster_src/client/application/commands</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M961,-621.28C961,-621.28 1450.12,-621.28 1450.12,-621.28 1456.12,-621.28 1462.12,-627.28 1462.12,-633.28 1462.12,-633.28 1462.12,-1429.28 1462.12,-1429.28 1462.12,-1435.28 1456.12,-1441.28 1450.12,-1441.28 1450.12,-1441.28 961,-1441.28 961,-1441.28 955,-1441.28 949,-1435.28 949,-1429.28 949,-1429.28 949,-633.28 949,-633.28 949,-627.28 955,-621.28 961,-621.28"/>
<text text-anchor="middle" x="1205.56" y="-1428.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">commands</text>
</g>
<g id="clust18" class="cluster">
<title>cluster_src/client/application/commands/addCardToDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1005.75,-934.28C1005.75,-934.28 1283.12,-934.28 1283.12,-934.28 1289.12,-934.28 1295.12,-940.28 1295.12,-946.28 1295.12,-946.28 1295.12,-975.28 1295.12,-975.28 1295.12,-981.28 1289.12,-987.28 1283.12,-987.28 1283.12,-987.28 1005.75,-987.28 1005.75,-987.28 999.75,-987.28 993.75,-981.28 993.75,-975.28 993.75,-975.28 993.75,-946.28 993.75,-946.28 993.75,-940.28 999.75,-934.28 1005.75,-934.28"/>
<text text-anchor="middle" x="1144.44" y="-974.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">addCardToDeck</text>
</g>
<g id="clust19" class="cluster">
<title>cluster_src/client/application/commands/clearDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1008.75,-1300.28C1008.75,-1300.28 1086.5,-1300.28 1086.5,-1300.28 1092.5,-1300.28 1098.5,-1306.28 1098.5,-1312.28 1098.5,-1312.28 1098.5,-1341.28 1098.5,-1341.28 1098.5,-1347.28 1092.5,-1353.28 1086.5,-1353.28 1086.5,-1353.28 1008.75,-1353.28 1008.75,-1353.28 1002.75,-1353.28 996.75,-1347.28 996.75,-1341.28 996.75,-1341.28 996.75,-1312.28 996.75,-1312.28 996.75,-1306.28 1002.75,-1300.28 1008.75,-1300.28"/>
<text text-anchor="middle" x="1047.62" y="-1340.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">clearDeckDraft</text>
</g>
<g id="clust20" class="cluster">
<title>cluster_src/client/application/commands/filterCatalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1190.75,-1239.28C1190.75,-1239.28 1442.12,-1239.28 1442.12,-1239.28 1448.12,-1239.28 1454.12,-1245.28 1454.12,-1251.28 1454.12,-1251.28 1454.12,-1280.28 1454.12,-1280.28 1454.12,-1286.28 1448.12,-1292.28 1442.12,-1292.28 1442.12,-1292.28 1190.75,-1292.28 1190.75,-1292.28 1184.75,-1292.28 1178.75,-1286.28 1178.75,-1280.28 1178.75,-1280.28 1178.75,-1251.28 1178.75,-1251.28 1178.75,-1245.28 1184.75,-1239.28 1190.75,-1239.28"/>
<text text-anchor="middle" x="1316.44" y="-1279.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">filterCatalog</text>
</g>
<g id="clust21" class="cluster">
<title>cluster_src/client/application/commands/hideCardDetails</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1005.75,-629.28C1005.75,-629.28 1089.5,-629.28 1089.5,-629.28 1095.5,-629.28 1101.5,-635.28 1101.5,-641.28 1101.5,-641.28 1101.5,-670.28 1101.5,-670.28 1101.5,-676.28 1095.5,-682.28 1089.5,-682.28 1089.5,-682.28 1005.75,-682.28 1005.75,-682.28 999.75,-682.28 993.75,-676.28 993.75,-670.28 993.75,-670.28 993.75,-641.28 993.75,-641.28 993.75,-635.28 999.75,-629.28 1005.75,-629.28"/>
<text text-anchor="middle" x="1047.62" y="-669.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hideCardDetails</text>
</g>
<g id="clust22" class="cluster">
<title>cluster_src/client/application/commands/initializeDeckBuilderFromLocation</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M969,-1178.28C969,-1178.28 1126.25,-1178.28 1126.25,-1178.28 1132.25,-1178.28 1138.25,-1184.28 1138.25,-1190.28 1138.25,-1190.28 1138.25,-1219.28 1138.25,-1219.28 1138.25,-1225.28 1132.25,-1231.28 1126.25,-1231.28 1126.25,-1231.28 969,-1231.28 969,-1231.28 963,-1231.28 957,-1225.28 957,-1219.28 957,-1219.28 957,-1190.28 957,-1190.28 957,-1184.28 963,-1178.28 969,-1178.28"/>
<text text-anchor="middle" x="1047.62" y="-1218.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">initializeDeckBuilderFromLocation</text>
</g>
<g id="clust23" class="cluster">
<title>cluster_src/client/application/commands/loadCatalogCards</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1001.62,-995.28C1001.62,-995.28 1287.25,-995.28 1287.25,-995.28 1293.25,-995.28 1299.25,-1001.28 1299.25,-1007.28 1299.25,-1007.28 1299.25,-1036.28 1299.25,-1036.28 1299.25,-1042.28 1293.25,-1048.28 1287.25,-1048.28 1287.25,-1048.28 1001.62,-1048.28 1001.62,-1048.28 995.62,-1048.28 989.62,-1042.28 989.62,-1036.28 989.62,-1036.28 989.62,-1007.28 989.62,-1007.28 989.62,-1001.28 995.62,-995.28 1001.62,-995.28"/>
<text text-anchor="middle" x="1144.44" y="-1035.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadCatalogCards</text>
</g>
<g id="clust24" class="cluster">
<title>cluster_src/client/application/commands/loadDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1009.88,-1239.28C1009.88,-1239.28 1085.38,-1239.28 1085.38,-1239.28 1091.38,-1239.28 1097.38,-1245.28 1097.38,-1251.28 1097.38,-1251.28 1097.38,-1280.28 1097.38,-1280.28 1097.38,-1286.28 1091.38,-1292.28 1085.38,-1292.28 1085.38,-1292.28 1009.88,-1292.28 1009.88,-1292.28 1003.88,-1292.28 997.88,-1286.28 997.88,-1280.28 997.88,-1280.28 997.88,-1251.28 997.88,-1251.28 997.88,-1245.28 1003.88,-1239.28 1009.88,-1239.28"/>
<text text-anchor="middle" x="1047.62" y="-1279.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadDeckDraft</text>
</g>
<g id="clust25" class="cluster">
<title>cluster_src/client/application/commands/loadDeckIntoBuilder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M997.88,-751.28C997.88,-751.28 1291.38,-751.28 1291.38,-751.28 1297.38,-751.28 1303.38,-757.28 1303.38,-763.28 1303.38,-763.28 1303.38,-792.28 1303.38,-792.28 1303.38,-798.28 1297.38,-804.28 1291.38,-804.28 1291.38,-804.28 997.88,-804.28 997.88,-804.28 991.88,-804.28 985.88,-798.28 985.88,-792.28 985.88,-792.28 985.88,-763.28 985.88,-763.28 985.88,-757.28 991.88,-751.28 997.88,-751.28"/>
<text text-anchor="middle" x="1144.62" y="-791.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadDeckIntoBuilder</text>
</g>
<g id="clust26" class="cluster">
<title>cluster_src/client/application/commands/loadGameSettings</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1001.25,-1056.28C1001.25,-1056.28 1287.62,-1056.28 1287.62,-1056.28 1293.62,-1056.28 1299.62,-1062.28 1299.62,-1068.28 1299.62,-1068.28 1299.62,-1097.28 1299.62,-1097.28 1299.62,-1103.28 1293.62,-1109.28 1287.62,-1109.28 1287.62,-1109.28 1001.25,-1109.28 1001.25,-1109.28 995.25,-1109.28 989.25,-1103.28 989.25,-1097.28 989.25,-1097.28 989.25,-1068.28 989.25,-1068.28 989.25,-1062.28 995.25,-1056.28 1001.25,-1056.28"/>
<text text-anchor="middle" x="1144.44" y="-1096.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadGameSettings</text>
</g>
<g id="clust27" class="cluster">
<title>cluster_src/client/application/commands/removeCardFromDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M992.62,-1117.28C992.62,-1117.28 1296.25,-1117.28 1296.25,-1117.28 1302.25,-1117.28 1308.25,-1123.28 1308.25,-1129.28 1308.25,-1129.28 1308.25,-1158.28 1308.25,-1158.28 1308.25,-1164.28 1302.25,-1170.28 1296.25,-1170.28 1296.25,-1170.28 992.62,-1170.28 992.62,-1170.28 986.62,-1170.28 980.62,-1164.28 980.62,-1158.28 980.62,-1158.28 980.62,-1129.28 980.62,-1129.28 980.62,-1123.28 986.62,-1117.28 992.62,-1117.28"/>
<text text-anchor="middle" x="1144.44" y="-1157.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">removeCardFromDeck</text>
</g>
<g id="clust28" class="cluster">
<title>cluster_src/client/application/commands/saveDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1009.12,-1361.28C1009.12,-1361.28 1086.12,-1361.28 1086.12,-1361.28 1092.12,-1361.28 1098.12,-1367.28 1098.12,-1373.28 1098.12,-1373.28 1098.12,-1402.28 1098.12,-1402.28 1098.12,-1408.28 1092.12,-1414.28 1086.12,-1414.28 1086.12,-1414.28 1009.12,-1414.28 1009.12,-1414.28 1003.12,-1414.28 997.12,-1408.28 997.12,-1402.28 997.12,-1402.28 997.12,-1373.28 997.12,-1373.28 997.12,-1367.28 1003.12,-1361.28 1009.12,-1361.28"/>
<text text-anchor="middle" x="1047.62" y="-1401.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">saveDeckDraft</text>
</g>
<g id="clust29" class="cluster">
<title>cluster_src/client/application/commands/search</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1201,-1330.28C1201,-1330.28 1431.25,-1330.28 1431.25,-1330.28 1437.25,-1330.28 1443.25,-1336.28 1443.25,-1342.28 1443.25,-1342.28 1443.25,-1371.28 1443.25,-1371.28 1443.25,-1377.28 1437.25,-1383.28 1431.25,-1383.28 1431.25,-1383.28 1201,-1383.28 1201,-1383.28 1195,-1383.28 1189,-1377.28 1189,-1371.28 1189,-1371.28 1189,-1342.28 1189,-1342.28 1189,-1336.28 1195,-1330.28 1201,-1330.28"/>
<text text-anchor="middle" x="1316.12" y="-1370.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">search</text>
</g>
<g id="clust30" class="cluster">
<title>cluster_src/client/application/commands/showCardDetails</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1003.88,-690.28C1003.88,-690.28 1285,-690.28 1285,-690.28 1291,-690.28 1297,-696.28 1297,-702.28 1297,-702.28 1297,-731.28 1297,-731.28 1297,-737.28 1291,-743.28 1285,-743.28 1285,-743.28 1003.88,-743.28 1003.88,-743.28 997.88,-743.28 991.88,-737.28 991.88,-731.28 991.88,-731.28 991.88,-702.28 991.88,-702.28 991.88,-696.28 997.88,-690.28 1003.88,-690.28"/>
<text text-anchor="middle" x="1144.44" y="-730.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">showCardDetails</text>
</g>
<g id="clust31" class="cluster">
<title>cluster_src/client/application/commands/switchDeckBuilderView</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M991.5,-812.28C991.5,-812.28 1297.75,-812.28 1297.75,-812.28 1303.75,-812.28 1309.75,-818.28 1309.75,-824.28 1309.75,-824.28 1309.75,-853.28 1309.75,-853.28 1309.75,-859.28 1303.75,-865.28 1297.75,-865.28 1297.75,-865.28 991.5,-865.28 991.5,-865.28 985.5,-865.28 979.5,-859.28 979.5,-853.28 979.5,-853.28 979.5,-824.28 979.5,-824.28 979.5,-818.28 985.5,-812.28 991.5,-812.28"/>
<text text-anchor="middle" x="1144.62" y="-852.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">switchDeckBuilderView</text>
</g>
<g id="clust32" class="cluster">
<title>cluster_src/client/application/commands/updateAvailableFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M993.38,-873.28C993.38,-873.28 1295.5,-873.28 1295.5,-873.28 1301.5,-873.28 1307.5,-879.28 1307.5,-885.28 1307.5,-885.28 1307.5,-914.28 1307.5,-914.28 1307.5,-920.28 1301.5,-926.28 1295.5,-926.28 1295.5,-926.28 993.38,-926.28 993.38,-926.28 987.38,-926.28 981.38,-920.28 981.38,-914.28 981.38,-914.28 981.38,-885.28 981.38,-885.28 981.38,-879.28 987.38,-873.28 993.38,-873.28"/>
<text text-anchor="middle" x="1144.44" y="-913.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">updateAvailableFilters</text>
</g>
<g id="clust33" class="cluster">
<title>cluster_src/client/application/queries</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1158.38,-1449.28C1158.38,-1449.28 1444.5,-1449.28 1444.5,-1449.28 1450.5,-1449.28 1456.5,-1455.28 1456.5,-1461.28 1456.5,-1461.28 1456.5,-2623.28 1456.5,-2623.28 1456.5,-2629.28 1450.5,-2635.28 1444.5,-2635.28 1444.5,-2635.28 1158.38,-2635.28 1158.38,-2635.28 1152.38,-2635.28 1146.38,-2629.28 1146.38,-2623.28 1146.38,-2623.28 1146.38,-1461.28 1146.38,-1461.28 1146.38,-1455.28 1152.38,-1449.28 1158.38,-1449.28"/>
<text text-anchor="middle" x="1301.44" y="-2622.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">queries</text>
</g>
<g id="clust34" class="cluster">
<title>cluster_src/client/application/queries/applyFilterToCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1346.75,-2067.28C1346.75,-2067.28 1436.5,-2067.28 1436.5,-2067.28 1442.5,-2067.28 1448.5,-2073.28 1448.5,-2079.28 1448.5,-2079.28 1448.5,-2108.28 1448.5,-2108.28 1448.5,-2114.28 1442.5,-2120.28 1436.5,-2120.28 1436.5,-2120.28 1346.75,-2120.28 1346.75,-2120.28 1340.75,-2120.28 1334.75,-2114.28 1334.75,-2108.28 1334.75,-2108.28 1334.75,-2079.28 1334.75,-2079.28 1334.75,-2073.28 1340.75,-2067.28 1346.75,-2067.28"/>
<text text-anchor="middle" x="1391.62" y="-2107.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">applyFilterToCard</text>
</g>
<g id="clust35" class="cluster">
<title>cluster_src/client/application/queries/findDeckCardById</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1178.38,-1457.28C1178.38,-1457.28 1269.62,-1457.28 1269.62,-1457.28 1275.62,-1457.28 1281.62,-1463.28 1281.62,-1469.28 1281.62,-1469.28 1281.62,-1498.28 1281.62,-1498.28 1281.62,-1504.28 1275.62,-1510.28 1269.62,-1510.28 1269.62,-1510.28 1178.38,-1510.28 1178.38,-1510.28 1172.38,-1510.28 1166.38,-1504.28 1166.38,-1498.28 1166.38,-1498.28 1166.38,-1469.28 1166.38,-1469.28 1166.38,-1463.28 1172.38,-1457.28 1178.38,-1457.28"/>
<text text-anchor="middle" x="1224" y="-1497.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">findDeckCardById</text>
</g>
<g id="clust36" class="cluster">
<title>cluster_src/client/application/queries/getActiveFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1184.38,-1640.28C1184.38,-1640.28 1263.62,-1640.28 1263.62,-1640.28 1269.62,-1640.28 1275.62,-1646.28 1275.62,-1652.28 1275.62,-1652.28 1275.62,-1681.28 1275.62,-1681.28 1275.62,-1687.28 1269.62,-1693.28 1263.62,-1693.28 1263.62,-1693.28 1184.38,-1693.28 1184.38,-1693.28 1178.38,-1693.28 1172.38,-1687.28 1172.38,-1681.28 1172.38,-1681.28 1172.38,-1652.28 1172.38,-1652.28 1172.38,-1646.28 1178.38,-1640.28 1184.38,-1640.28"/>
<text text-anchor="middle" x="1224" y="-1680.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getActiveFilters</text>
</g>
<g id="clust37" class="cluster">
<title>cluster_src/client/application/queries/getAvailableFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1177.62,-1579.28C1177.62,-1579.28 1270.38,-1579.28 1270.38,-1579.28 1276.38,-1579.28 1282.38,-1585.28 1282.38,-1591.28 1282.38,-1591.28 1282.38,-1620.28 1282.38,-1620.28 1282.38,-1626.28 1276.38,-1632.28 1270.38,-1632.28 1270.38,-1632.28 1177.62,-1632.28 1177.62,-1632.28 1171.62,-1632.28 1165.62,-1626.28 1165.62,-1620.28 1165.62,-1620.28 1165.62,-1591.28 1165.62,-1591.28 1165.62,-1585.28 1171.62,-1579.28 1177.62,-1579.28"/>
<text text-anchor="middle" x="1224" y="-1619.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getAvailableFilters</text>
</g>
<g id="clust38" class="cluster">
<title>cluster_src/client/application/queries/getCardDetails</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1184.75,-1701.28C1184.75,-1701.28 1263.25,-1701.28 1263.25,-1701.28 1269.25,-1701.28 1275.25,-1707.28 1275.25,-1713.28 1275.25,-1713.28 1275.25,-1742.28 1275.25,-1742.28 1275.25,-1748.28 1269.25,-1754.28 1263.25,-1754.28 1263.25,-1754.28 1184.75,-1754.28 1184.75,-1754.28 1178.75,-1754.28 1172.75,-1748.28 1172.75,-1742.28 1172.75,-1742.28 1172.75,-1713.28 1172.75,-1713.28 1172.75,-1707.28 1178.75,-1701.28 1184.75,-1701.28"/>
<text text-anchor="middle" x="1224" y="-1741.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCardDetails</text>
</g>
<g id="clust39" class="cluster">
<title>cluster_src/client/application/queries/getCardsInDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1182.5,-1762.28C1182.5,-1762.28 1265.5,-1762.28 1265.5,-1762.28 1271.5,-1762.28 1277.5,-1768.28 1277.5,-1774.28 1277.5,-1774.28 1277.5,-1803.28 1277.5,-1803.28 1277.5,-1809.28 1271.5,-1815.28 1265.5,-1815.28 1265.5,-1815.28 1182.5,-1815.28 1182.5,-1815.28 1176.5,-1815.28 1170.5,-1809.28 1170.5,-1803.28 1170.5,-1803.28 1170.5,-1774.28 1170.5,-1774.28 1170.5,-1768.28 1176.5,-1762.28 1182.5,-1762.28"/>
<text text-anchor="middle" x="1224" y="-1802.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCardsInDeck</text>
</g>
<g id="clust40" class="cluster">
<title>cluster_src/client/application/queries/getCatalogCardById</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1173.88,-1518.28C1173.88,-1518.28 1274.12,-1518.28 1274.12,-1518.28 1280.12,-1518.28 1286.12,-1524.28 1286.12,-1530.28 1286.12,-1530.28 1286.12,-1559.28 1286.12,-1559.28 1286.12,-1565.28 1280.12,-1571.28 1274.12,-1571.28 1274.12,-1571.28 1173.88,-1571.28 1173.88,-1571.28 1167.88,-1571.28 1161.88,-1565.28 1161.88,-1559.28 1161.88,-1559.28 1161.88,-1530.28 1161.88,-1530.28 1161.88,-1524.28 1167.88,-1518.28 1173.88,-1518.28"/>
<text text-anchor="middle" x="1224" y="-1558.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCatalogCardById</text>
</g>
<g id="clust41" class="cluster">
<title>cluster_src/client/application/queries/getCatalogCards</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1180.62,-1823.28C1180.62,-1823.28 1267.38,-1823.28 1267.38,-1823.28 1273.38,-1823.28 1279.38,-1829.28 1279.38,-1835.28 1279.38,-1835.28 1279.38,-1864.28 1279.38,-1864.28 1279.38,-1870.28 1273.38,-1876.28 1267.38,-1876.28 1267.38,-1876.28 1180.62,-1876.28 1180.62,-1876.28 1174.62,-1876.28 1168.62,-1870.28 1168.62,-1864.28 1168.62,-1864.28 1168.62,-1835.28 1168.62,-1835.28 1168.62,-1829.28 1174.62,-1823.28 1180.62,-1823.28"/>
<text text-anchor="middle" x="1224" y="-1863.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCatalogCards</text>
</g>
<g id="clust42" class="cluster">
<title>cluster_src/client/application/queries/getCatalogError</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1183.25,-2006.28C1183.25,-2006.28 1264.75,-2006.28 1264.75,-2006.28 1270.75,-2006.28 1276.75,-2012.28 1276.75,-2018.28 1276.75,-2018.28 1276.75,-2047.28 1276.75,-2047.28 1276.75,-2053.28 1270.75,-2059.28 1264.75,-2059.28 1264.75,-2059.28 1183.25,-2059.28 1183.25,-2059.28 1177.25,-2059.28 1171.25,-2053.28 1171.25,-2047.28 1171.25,-2047.28 1171.25,-2018.28 1171.25,-2018.28 1171.25,-2012.28 1177.25,-2006.28 1183.25,-2006.28"/>
<text text-anchor="middle" x="1224" y="-2046.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getCatalogError</text>
</g>
<g id="clust43" class="cluster">
<title>cluster_src/client/application/queries/getDeckBuilderView</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1174.25,-1884.28C1174.25,-1884.28 1273.75,-1884.28 1273.75,-1884.28 1279.75,-1884.28 1285.75,-1890.28 1285.75,-1896.28 1285.75,-1896.28 1285.75,-1925.28 1285.75,-1925.28 1285.75,-1931.28 1279.75,-1937.28 1273.75,-1937.28 1273.75,-1937.28 1174.25,-1937.28 1174.25,-1937.28 1168.25,-1937.28 1162.25,-1931.28 1162.25,-1925.28 1162.25,-1925.28 1162.25,-1896.28 1162.25,-1896.28 1162.25,-1890.28 1168.25,-1884.28 1174.25,-1884.28"/>
<text text-anchor="middle" x="1224" y="-1924.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getDeckBuilderView</text>
</g>
<g id="clust44" class="cluster">
<title>cluster_src/client/application/queries/getDeckName</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1186.25,-1945.28C1186.25,-1945.28 1261.75,-1945.28 1261.75,-1945.28 1267.75,-1945.28 1273.75,-1951.28 1273.75,-1957.28 1273.75,-1957.28 1273.75,-1986.28 1273.75,-1986.28 1273.75,-1992.28 1267.75,-1998.28 1261.75,-1998.28 1261.75,-1998.28 1186.25,-1998.28 1186.25,-1998.28 1180.25,-1998.28 1174.25,-1992.28 1174.25,-1986.28 1174.25,-1986.28 1174.25,-1957.28 1174.25,-1957.28 1174.25,-1951.28 1180.25,-1945.28 1186.25,-1945.28"/>
<text text-anchor="middle" x="1224" y="-1985.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getDeckName</text>
</g>
<g id="clust45" class="cluster">
<title>cluster_src/client/application/queries/getFilteredCards</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1181.38,-2067.28C1181.38,-2067.28 1266.62,-2067.28 1266.62,-2067.28 1272.62,-2067.28 1278.62,-2073.28 1278.62,-2079.28 1278.62,-2079.28 1278.62,-2108.28 1278.62,-2108.28 1278.62,-2114.28 1272.62,-2120.28 1266.62,-2120.28 1266.62,-2120.28 1181.38,-2120.28 1181.38,-2120.28 1175.38,-2120.28 1169.38,-2114.28 1169.38,-2108.28 1169.38,-2108.28 1169.38,-2079.28 1169.38,-2079.28 1169.38,-2073.28 1175.38,-2067.28 1181.38,-2067.28"/>
<text text-anchor="middle" x="1224" y="-2107.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getFilteredCards</text>
</g>
<g id="clust46" class="cluster">
<title>cluster_src/client/application/queries/getGameSettings</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1180.25,-2189.28C1180.25,-2189.28 1267.75,-2189.28 1267.75,-2189.28 1273.75,-2189.28 1279.75,-2195.28 1279.75,-2201.28 1279.75,-2201.28 1279.75,-2230.28 1279.75,-2230.28 1279.75,-2236.28 1273.75,-2242.28 1267.75,-2242.28 1267.75,-2242.28 1180.25,-2242.28 1180.25,-2242.28 1174.25,-2242.28 1168.25,-2236.28 1168.25,-2230.28 1168.25,-2230.28 1168.25,-2201.28 1168.25,-2201.28 1168.25,-2195.28 1174.25,-2189.28 1180.25,-2189.28"/>
<text text-anchor="middle" x="1224" y="-2229.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getGameSettings</text>
</g>
<g id="clust47" class="cluster">
<title>cluster_src/client/application/queries/getGameSettingsError</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1170.5,-2250.28C1170.5,-2250.28 1277.5,-2250.28 1277.5,-2250.28 1283.5,-2250.28 1289.5,-2256.28 1289.5,-2262.28 1289.5,-2262.28 1289.5,-2291.28 1289.5,-2291.28 1289.5,-2297.28 1283.5,-2303.28 1277.5,-2303.28 1277.5,-2303.28 1170.5,-2303.28 1170.5,-2303.28 1164.5,-2303.28 1158.5,-2297.28 1158.5,-2291.28 1158.5,-2291.28 1158.5,-2262.28 1158.5,-2262.28 1158.5,-2256.28 1164.5,-2250.28 1170.5,-2250.28"/>
<text text-anchor="middle" x="1224" y="-2290.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getGameSettingsError</text>
</g>
<g id="clust48" class="cluster">
<title>cluster_src/client/application/queries/getMaxCardsInDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1173.88,-2311.28C1173.88,-2311.28 1274.12,-2311.28 1274.12,-2311.28 1280.12,-2311.28 1286.12,-2317.28 1286.12,-2323.28 1286.12,-2323.28 1286.12,-2352.28 1286.12,-2352.28 1286.12,-2358.28 1280.12,-2364.28 1274.12,-2364.28 1274.12,-2364.28 1173.88,-2364.28 1173.88,-2364.28 1167.88,-2364.28 1161.88,-2358.28 1161.88,-2352.28 1161.88,-2352.28 1161.88,-2323.28 1161.88,-2323.28 1161.88,-2317.28 1167.88,-2311.28 1173.88,-2311.28"/>
<text text-anchor="middle" x="1224" y="-2351.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getMaxCardsInDeck</text>
</g>
<g id="clust49" class="cluster">
<title>cluster_src/client/application/queries/getSearchTerm</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1184.38,-2433.28C1184.38,-2433.28 1263.62,-2433.28 1263.62,-2433.28 1269.62,-2433.28 1275.62,-2439.28 1275.62,-2445.28 1275.62,-2445.28 1275.62,-2474.28 1275.62,-2474.28 1275.62,-2480.28 1269.62,-2486.28 1263.62,-2486.28 1263.62,-2486.28 1184.38,-2486.28 1184.38,-2486.28 1178.38,-2486.28 1172.38,-2480.28 1172.38,-2474.28 1172.38,-2474.28 1172.38,-2445.28 1172.38,-2445.28 1172.38,-2439.28 1178.38,-2433.28 1184.38,-2433.28"/>
<text text-anchor="middle" x="1224" y="-2473.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getSearchTerm</text>
</g>
<g id="clust50" class="cluster">
<title>cluster_src/client/application/queries/getTotalCardsInDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1172.75,-2494.28C1172.75,-2494.28 1275.25,-2494.28 1275.25,-2494.28 1281.25,-2494.28 1287.25,-2500.28 1287.25,-2506.28 1287.25,-2506.28 1287.25,-2535.28 1287.25,-2535.28 1287.25,-2541.28 1281.25,-2547.28 1275.25,-2547.28 1275.25,-2547.28 1172.75,-2547.28 1172.75,-2547.28 1166.75,-2547.28 1160.75,-2541.28 1160.75,-2535.28 1160.75,-2535.28 1160.75,-2506.28 1160.75,-2506.28 1160.75,-2500.28 1166.75,-2494.28 1172.75,-2494.28"/>
<text text-anchor="middle" x="1224" y="-2534.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">getTotalCardsInDeck</text>
</g>
<g id="clust51" class="cluster">
<title>cluster_src/client/application/queries/hasDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1187.75,-2128.28C1187.75,-2128.28 1260.25,-2128.28 1260.25,-2128.28 1266.25,-2128.28 1272.25,-2134.28 1272.25,-2140.28 1272.25,-2140.28 1272.25,-2169.28 1272.25,-2169.28 1272.25,-2175.28 1266.25,-2181.28 1260.25,-2181.28 1260.25,-2181.28 1187.75,-2181.28 1187.75,-2181.28 1181.75,-2181.28 1175.75,-2175.28 1175.75,-2169.28 1175.75,-2169.28 1175.75,-2140.28 1175.75,-2140.28 1175.75,-2134.28 1181.75,-2128.28 1187.75,-2128.28"/>
<text text-anchor="middle" x="1224" y="-2168.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hasDeckDraft</text>
</g>
<g id="clust52" class="cluster">
<title>cluster_src/client/application/queries/isCatalogLoading</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1179.12,-2372.28C1179.12,-2372.28 1268.88,-2372.28 1268.88,-2372.28 1274.88,-2372.28 1280.88,-2378.28 1280.88,-2384.28 1280.88,-2384.28 1280.88,-2413.28 1280.88,-2413.28 1280.88,-2419.28 1274.88,-2425.28 1268.88,-2425.28 1268.88,-2425.28 1179.12,-2425.28 1179.12,-2425.28 1173.12,-2425.28 1167.12,-2419.28 1167.12,-2413.28 1167.12,-2413.28 1167.12,-2384.28 1167.12,-2384.28 1167.12,-2378.28 1173.12,-2372.28 1179.12,-2372.28"/>
<text text-anchor="middle" x="1224" y="-2412.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">isCatalogLoading</text>
</g>
<g id="clust53" class="cluster">
<title>cluster_src/client/application/queries/isGameSettingsLoading</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1166.38,-2555.28C1166.38,-2555.28 1281.62,-2555.28 1281.62,-2555.28 1287.62,-2555.28 1293.62,-2561.28 1293.62,-2567.28 1293.62,-2567.28 1293.62,-2596.28 1293.62,-2596.28 1293.62,-2602.28 1287.62,-2608.28 1281.62,-2608.28 1281.62,-2608.28 1166.38,-2608.28 1166.38,-2608.28 1160.38,-2608.28 1154.38,-2602.28 1154.38,-2596.28 1154.38,-2596.28 1154.38,-2567.28 1154.38,-2567.28 1154.38,-2561.28 1160.38,-2555.28 1166.38,-2555.28"/>
<text text-anchor="middle" x="1224" y="-2595.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">isGameSettingsLoading</text>
</g>
<g id="clust54" class="cluster">
<title>cluster_src/client/application/services</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1485.38,-2713.28C1485.38,-2713.28 1589.62,-2713.28 1589.62,-2713.28 1595.62,-2713.28 1601.62,-2719.28 1601.62,-2725.28 1601.62,-2725.28 1601.62,-2815.28 1601.62,-2815.28 1601.62,-2821.28 1595.62,-2827.28 1589.62,-2827.28 1589.62,-2827.28 1485.38,-2827.28 1485.38,-2827.28 1479.38,-2827.28 1473.38,-2821.28 1473.38,-2815.28 1473.38,-2815.28 1473.38,-2725.28 1473.38,-2725.28 1473.38,-2719.28 1479.38,-2713.28 1485.38,-2713.28"/>
<text text-anchor="middle" x="1537.5" y="-2814.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">services</text>
</g>
<g id="clust55" class="cluster">
<title>cluster_src/client/application/services/DeckDraftService</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1493.38,-2721.28C1493.38,-2721.28 1581.62,-2721.28 1581.62,-2721.28 1587.62,-2721.28 1593.62,-2727.28 1593.62,-2733.28 1593.62,-2733.28 1593.62,-2762.28 1593.62,-2762.28 1593.62,-2768.28 1587.62,-2774.28 1581.62,-2774.28 1581.62,-2774.28 1493.38,-2774.28 1493.38,-2774.28 1487.38,-2774.28 1481.38,-2768.28 1481.38,-2762.28 1481.38,-2762.28 1481.38,-2733.28 1481.38,-2733.28 1481.38,-2727.28 1487.38,-2721.28 1493.38,-2721.28"/>
<text text-anchor="middle" x="1537.5" y="-2761.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckDraftService</text>
</g>
<g id="clust56" class="cluster">
<title>cluster_src/client/application/store</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1364.38,-2643.28C1364.38,-2643.28 1418.88,-2643.28 1418.88,-2643.28 1424.88,-2643.28 1430.88,-2649.28 1430.88,-2655.28 1430.88,-2655.28 1430.88,-2684.28 1430.88,-2684.28 1430.88,-2690.28 1424.88,-2696.28 1418.88,-2696.28 1418.88,-2696.28 1364.38,-2696.28 1364.38,-2696.28 1358.38,-2696.28 1352.38,-2690.28 1352.38,-2684.28 1352.38,-2684.28 1352.38,-2655.28 1352.38,-2655.28 1352.38,-2649.28 1358.38,-2643.28 1364.38,-2643.28"/>
<text text-anchor="middle" x="1391.62" y="-2683.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">store</text>
</g>
<g id="clust57" class="cluster">
<title>cluster_src/client/application/subscribers</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M787,-1985.28C787,-1985.28 894,-1985.28 894,-1985.28 900,-1985.28 906,-1991.28 906,-1997.28 906,-1997.28 906,-2026.28 906,-2026.28 906,-2032.28 900,-2038.28 894,-2038.28 894,-2038.28 787,-2038.28 787,-2038.28 781,-2038.28 775,-2032.28 775,-2026.28 775,-2026.28 775,-1997.28 775,-1997.28 775,-1991.28 781,-1985.28 787,-1985.28"/>
<text text-anchor="middle" x="840.5" y="-2025.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">subscribers</text>
</g>
<g id="clust58" class="cluster">
<title>cluster_src/client/domain</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1329.75,-186.28C1329.75,-186.28 1939.12,-186.28 1939.12,-186.28 1945.12,-186.28 1951.12,-192.28 1951.12,-198.28 1951.12,-198.28 1951.12,-593.28 1951.12,-593.28 1951.12,-599.28 1945.12,-605.28 1939.12,-605.28 1939.12,-605.28 1329.75,-605.28 1329.75,-605.28 1323.75,-605.28 1317.75,-599.28 1317.75,-593.28 1317.75,-593.28 1317.75,-198.28 1317.75,-198.28 1317.75,-192.28 1323.75,-186.28 1329.75,-186.28"/>
<text text-anchor="middle" x="1634.44" y="-592.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">domain</text>
</g>
<g id="clust59" class="cluster">
<title>cluster_src/client/domain/CardData</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1872.12,-525.28C1872.12,-525.28 1931.12,-525.28 1931.12,-525.28 1937.12,-525.28 1943.12,-531.28 1943.12,-537.28 1943.12,-537.28 1943.12,-566.28 1943.12,-566.28 1943.12,-572.28 1937.12,-578.28 1931.12,-578.28 1931.12,-578.28 1872.12,-578.28 1872.12,-578.28 1866.12,-578.28 1860.12,-572.28 1860.12,-566.28 1860.12,-566.28 1860.12,-537.28 1860.12,-537.28 1860.12,-531.28 1866.12,-525.28 1872.12,-525.28"/>
<text text-anchor="middle" x="1901.62" y="-565.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">CardData</text>
</g>
<g id="clust60" class="cluster">
<title>cluster_src/client/domain/Catalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1482.12,-361.28C1482.12,-361.28 1924.62,-361.28 1924.62,-361.28 1930.62,-361.28 1936.62,-367.28 1936.62,-373.28 1936.62,-373.28 1936.62,-505.28 1936.62,-505.28 1936.62,-511.28 1930.62,-517.28 1924.62,-517.28 1924.62,-517.28 1482.12,-517.28 1482.12,-517.28 1476.12,-517.28 1470.12,-511.28 1470.12,-505.28 1470.12,-505.28 1470.12,-373.28 1470.12,-373.28 1470.12,-367.28 1476.12,-361.28 1482.12,-361.28"/>
<text text-anchor="middle" x="1703.38" y="-504.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Catalog</text>
</g>
<g id="clust61" class="cluster">
<title>cluster_src/client/domain/DeckBuilder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1358.38,-194.28C1358.38,-194.28 1840.12,-194.28 1840.12,-194.28 1846.12,-194.28 1852.12,-200.28 1852.12,-206.28 1852.12,-206.28 1852.12,-266.28 1852.12,-266.28 1852.12,-272.28 1846.12,-278.28 1840.12,-278.28 1840.12,-278.28 1358.38,-278.28 1358.38,-278.28 1352.38,-278.28 1346.38,-272.28 1346.38,-266.28 1346.38,-266.28 1346.38,-206.28 1346.38,-206.28 1346.38,-200.28 1352.38,-194.28 1358.38,-194.28"/>
<text text-anchor="middle" x="1599.25" y="-265.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilder</text>
</g>
<g id="clust62" class="cluster">
<title>cluster_src/client/domain/GameSettings</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1337.75,-286.28C1337.75,-286.28 1712.12,-286.28 1712.12,-286.28 1718.12,-286.28 1724.12,-292.28 1724.12,-298.28 1724.12,-298.28 1724.12,-341.28 1724.12,-341.28 1724.12,-347.28 1718.12,-353.28 1712.12,-353.28 1712.12,-353.28 1337.75,-353.28 1337.75,-353.28 1331.75,-353.28 1325.75,-347.28 1325.75,-341.28 1325.75,-341.28 1325.75,-298.28 1325.75,-298.28 1325.75,-292.28 1331.75,-286.28 1337.75,-286.28"/>
<text text-anchor="middle" x="1524.94" y="-340.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameSettings</text>
</g>
<g id="clust63" class="cluster">
<title>cluster_src/client/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M208.25,-2990.28C208.25,-2990.28 1124.25,-2990.28 1124.25,-2990.28 1130.25,-2990.28 1136.25,-2996.28 1136.25,-3002.28 1136.25,-3002.28 1136.25,-5339.28 1136.25,-5339.28 1136.25,-5345.28 1130.25,-5351.28 1124.25,-5351.28 1124.25,-5351.28 208.25,-5351.28 208.25,-5351.28 202.25,-5351.28 196.25,-5345.28 196.25,-5339.28 196.25,-5339.28 196.25,-3002.28 196.25,-3002.28 196.25,-2996.28 202.25,-2990.28 208.25,-2990.28"/>
<text text-anchor="middle" x="666.25" y="-5338.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust64" class="cluster">
<title>cluster_src/client/infrastructure/builders</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M812.5,-5209.28C812.5,-5209.28 868.5,-5209.28 868.5,-5209.28 874.5,-5209.28 880.5,-5215.28 880.5,-5221.28 880.5,-5221.28 880.5,-5250.28 880.5,-5250.28 880.5,-5256.28 874.5,-5262.28 868.5,-5262.28 868.5,-5262.28 812.5,-5262.28 812.5,-5262.28 806.5,-5262.28 800.5,-5256.28 800.5,-5250.28 800.5,-5250.28 800.5,-5221.28 800.5,-5221.28 800.5,-5215.28 806.5,-5209.28 812.5,-5209.28"/>
<text text-anchor="middle" x="840.5" y="-5249.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">builders</text>
</g>
<g id="clust65" class="cluster">
<title>cluster_src/client/infrastructure/components</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M374.75,-3765.28C374.75,-3765.28 1104.38,-3765.28 1104.38,-3765.28 1110.38,-3765.28 1116.38,-3771.28 1116.38,-3777.28 1116.38,-3777.28 1116.38,-5189.28 1116.38,-5189.28 1116.38,-5195.28 1110.38,-5201.28 1104.38,-5201.28 1104.38,-5201.28 374.75,-5201.28 374.75,-5201.28 368.75,-5201.28 362.75,-5195.28 362.75,-5189.28 362.75,-5189.28 362.75,-3777.28 362.75,-3777.28 362.75,-3771.28 368.75,-3765.28 374.75,-3765.28"/>
<text text-anchor="middle" x="739.56" y="-5188.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">components</text>
</g>
<g id="clust66" class="cluster">
<title>cluster_src/client/infrastructure/components/app</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M382.75,-4087.28C382.75,-4087.28 716.75,-4087.28 716.75,-4087.28 722.75,-4087.28 728.75,-4093.28 728.75,-4099.28 728.75,-4099.28 728.75,-5162.28 728.75,-5162.28 728.75,-5168.28 722.75,-5174.28 716.75,-5174.28 716.75,-5174.28 382.75,-5174.28 382.75,-5174.28 376.75,-5174.28 370.75,-5168.28 370.75,-5162.28 370.75,-5162.28 370.75,-4099.28 370.75,-4099.28 370.75,-4093.28 376.75,-4087.28 382.75,-4087.28"/>
<text text-anchor="middle" x="549.75" y="-5161.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">app</text>
</g>
<g id="clust67" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Auth</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M412,-4095.28C412,-4095.28 695.38,-4095.28 695.38,-4095.28 701.38,-4095.28 707.38,-4101.28 707.38,-4107.28 707.38,-4107.28 707.38,-4237.28 707.38,-4237.28 707.38,-4243.28 701.38,-4249.28 695.38,-4249.28 695.38,-4249.28 412,-4249.28 412,-4249.28 406,-4249.28 400,-4243.28 400,-4237.28 400,-4237.28 400,-4107.28 400,-4107.28 400,-4101.28 406,-4095.28 412,-4095.28"/>
<text text-anchor="middle" x="553.69" y="-4236.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Auth</text>
</g>
<g id="clust68" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Auth/SignIn</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M420,-4103.28C420,-4103.28 687.38,-4103.28 687.38,-4103.28 693.38,-4103.28 699.38,-4109.28 699.38,-4115.28 699.38,-4115.28 699.38,-4210.28 699.38,-4210.28 699.38,-4216.28 693.38,-4222.28 687.38,-4222.28 687.38,-4222.28 420,-4222.28 420,-4222.28 414,-4222.28 408,-4216.28 408,-4210.28 408,-4210.28 408,-4115.28 408,-4115.28 408,-4109.28 414,-4103.28 420,-4103.28"/>
<text text-anchor="middle" x="553.69" y="-4209.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignIn</text>
</g>
<g id="clust69" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Auth/SignIn/SignInButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M428,-4111.28C428,-4111.28 502,-4111.28 502,-4111.28 508,-4111.28 514,-4117.28 514,-4123.28 514,-4123.28 514,-4152.28 514,-4152.28 514,-4158.28 508,-4164.28 502,-4164.28 502,-4164.28 428,-4164.28 428,-4164.28 422,-4164.28 416,-4158.28 416,-4152.28 416,-4152.28 416,-4123.28 416,-4123.28 416,-4117.28 422,-4111.28 428,-4111.28"/>
<text text-anchor="middle" x="465" y="-4151.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignInButton</text>
</g>
<g id="clust70" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Auth/SignIn/SignInForm</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M595.62,-4111.28C595.62,-4111.28 679.38,-4111.28 679.38,-4111.28 685.38,-4111.28 691.38,-4117.28 691.38,-4123.28 691.38,-4123.28 691.38,-4183.28 691.38,-4183.28 691.38,-4189.28 685.38,-4195.28 679.38,-4195.28 679.38,-4195.28 595.62,-4195.28 595.62,-4195.28 589.62,-4195.28 583.62,-4189.28 583.62,-4183.28 583.62,-4183.28 583.62,-4123.28 583.62,-4123.28 583.62,-4117.28 589.62,-4111.28 595.62,-4111.28"/>
<text text-anchor="middle" x="637.5" y="-4182.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignInForm</text>
</g>
<g id="clust71" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Catalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M406.5,-4780.28C406.5,-4780.28 523.5,-4780.28 523.5,-4780.28 529.5,-4780.28 535.5,-4786.28 535.5,-4792.28 535.5,-4792.28 535.5,-4917.28 535.5,-4917.28 535.5,-4923.28 529.5,-4929.28 523.5,-4929.28 523.5,-4929.28 406.5,-4929.28 406.5,-4929.28 400.5,-4929.28 394.5,-4923.28 394.5,-4917.28 394.5,-4917.28 394.5,-4792.28 394.5,-4792.28 394.5,-4786.28 400.5,-4780.28 406.5,-4780.28"/>
<text text-anchor="middle" x="465" y="-4916.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Catalog</text>
</g>
<g id="clust72" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Catalog/GameDetailsButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M414.5,-4788.28C414.5,-4788.28 515.5,-4788.28 515.5,-4788.28 521.5,-4788.28 527.5,-4794.28 527.5,-4800.28 527.5,-4800.28 527.5,-4829.28 527.5,-4829.28 527.5,-4835.28 521.5,-4841.28 515.5,-4841.28 515.5,-4841.28 414.5,-4841.28 414.5,-4841.28 408.5,-4841.28 402.5,-4835.28 402.5,-4829.28 402.5,-4829.28 402.5,-4800.28 402.5,-4800.28 402.5,-4794.28 408.5,-4788.28 414.5,-4788.28"/>
<text text-anchor="middle" x="465" y="-4828.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameDetailsButton</text>
</g>
<g id="clust73" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Catalog/PlayGameButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M419.75,-4849.28C419.75,-4849.28 510.25,-4849.28 510.25,-4849.28 516.25,-4849.28 522.25,-4855.28 522.25,-4861.28 522.25,-4861.28 522.25,-4890.28 522.25,-4890.28 522.25,-4896.28 516.25,-4902.28 510.25,-4902.28 510.25,-4902.28 419.75,-4902.28 419.75,-4902.28 413.75,-4902.28 407.75,-4896.28 407.75,-4890.28 407.75,-4890.28 407.75,-4861.28 407.75,-4861.28 407.75,-4855.28 413.75,-4849.28 419.75,-4849.28"/>
<text text-anchor="middle" x="465" y="-4889.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">PlayGameButton</text>
</g>
<g id="clust74" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M390.75,-4257.28C390.75,-4257.28 708.75,-4257.28 708.75,-4257.28 714.75,-4257.28 720.75,-4263.28 720.75,-4269.28 720.75,-4269.28 720.75,-4760.28 720.75,-4760.28 720.75,-4766.28 714.75,-4772.28 708.75,-4772.28 708.75,-4772.28 390.75,-4772.28 390.75,-4772.28 384.75,-4772.28 378.75,-4766.28 378.75,-4760.28 378.75,-4760.28 378.75,-4269.28 378.75,-4269.28 378.75,-4263.28 384.75,-4257.28 390.75,-4257.28"/>
<text text-anchor="middle" x="549.75" y="-4759.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilding</text>
</g>
<g id="clust75" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M408.12,-4387.28C408.12,-4387.28 521.88,-4387.28 521.88,-4387.28 527.88,-4387.28 533.88,-4393.28 533.88,-4399.28 533.88,-4399.28 533.88,-4428.28 533.88,-4428.28 533.88,-4434.28 527.88,-4440.28 521.88,-4440.28 521.88,-4440.28 408.12,-4440.28 408.12,-4440.28 402.12,-4440.28 396.12,-4434.28 396.12,-4428.28 396.12,-4428.28 396.12,-4399.28 396.12,-4399.28 396.12,-4393.28 402.12,-4387.28 408.12,-4387.28"/>
<text text-anchor="middle" x="465" y="-4427.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilderCardsGrid</text>
</g>
<g id="clust76" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M413.38,-4326.28C413.38,-4326.28 516.62,-4326.28 516.62,-4326.28 522.62,-4326.28 528.62,-4332.28 528.62,-4338.28 528.62,-4338.28 528.62,-4367.28 528.62,-4367.28 528.62,-4373.28 522.62,-4379.28 516.62,-4379.28 516.62,-4379.28 413.38,-4379.28 413.38,-4379.28 407.38,-4379.28 401.38,-4373.28 401.38,-4367.28 401.38,-4367.28 401.38,-4338.28 401.38,-4338.28 401.38,-4332.28 407.38,-4326.28 413.38,-4326.28"/>
<text text-anchor="middle" x="465" y="-4366.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilderInitializer</text>
</g>
<g id="clust77" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M417.12,-4509.28C417.12,-4509.28 677.88,-4509.28 677.88,-4509.28 683.88,-4509.28 689.88,-4515.28 689.88,-4521.28 689.88,-4521.28 689.88,-4550.28 689.88,-4550.28 689.88,-4556.28 683.88,-4562.28 677.88,-4562.28 677.88,-4562.28 417.12,-4562.28 417.12,-4562.28 411.12,-4562.28 405.12,-4556.28 405.12,-4550.28 405.12,-4550.28 405.12,-4521.28 405.12,-4521.28 405.12,-4515.28 411.12,-4509.28 417.12,-4509.28"/>
<text text-anchor="middle" x="547.5" y="-4549.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilderPanel</text>
</g>
<g id="clust78" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M589.25,-4295.28C589.25,-4295.28 685.75,-4295.28 685.75,-4295.28 691.75,-4295.28 697.75,-4301.28 697.75,-4307.28 697.75,-4307.28 697.75,-4336.28 697.75,-4336.28 697.75,-4342.28 691.75,-4348.28 685.75,-4348.28 685.75,-4348.28 589.25,-4348.28 589.25,-4348.28 583.25,-4348.28 577.25,-4342.28 577.25,-4336.28 577.25,-4336.28 577.25,-4307.28 577.25,-4307.28 577.25,-4301.28 583.25,-4295.28 589.25,-4295.28"/>
<text text-anchor="middle" x="637.5" y="-4335.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuildingCard</text>
</g>
<g id="clust79" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M414.5,-4692.28C414.5,-4692.28 667,-4692.28 667,-4692.28 673,-4692.28 679,-4698.28 679,-4704.28 679,-4704.28 679,-4733.28 679,-4733.28 679,-4739.28 673,-4745.28 667,-4745.28 667,-4745.28 414.5,-4745.28 414.5,-4745.28 408.5,-4745.28 402.5,-4739.28 402.5,-4733.28 402.5,-4733.28 402.5,-4704.28 402.5,-4704.28 402.5,-4698.28 408.5,-4692.28 414.5,-4692.28"/>
<text text-anchor="middle" x="540.75" y="-4732.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuildingFilters</text>
</g>
<g id="clust80" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M398.75,-4448.28C398.75,-4448.28 531.25,-4448.28 531.25,-4448.28 537.25,-4448.28 543.25,-4454.28 543.25,-4460.28 543.25,-4460.28 543.25,-4489.28 543.25,-4489.28 543.25,-4495.28 537.25,-4501.28 531.25,-4501.28 531.25,-4501.28 398.75,-4501.28 398.75,-4501.28 392.75,-4501.28 386.75,-4495.28 386.75,-4489.28 386.75,-4489.28 386.75,-4460.28 386.75,-4460.28 386.75,-4454.28 392.75,-4448.28 398.75,-4448.28"/>
<text text-anchor="middle" x="465" y="-4488.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuildingSkeletonCard</text>
</g>
<g id="clust81" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M578.38,-4570.28C578.38,-4570.28 696.62,-4570.28 696.62,-4570.28 702.62,-4570.28 708.62,-4576.28 708.62,-4582.28 708.62,-4582.28 708.62,-4611.28 708.62,-4611.28 708.62,-4617.28 702.62,-4623.28 696.62,-4623.28 696.62,-4623.28 578.38,-4623.28 578.38,-4623.28 572.38,-4623.28 566.38,-4617.28 566.38,-4611.28 566.38,-4611.28 566.38,-4582.28 566.38,-4582.28 566.38,-4576.28 572.38,-4570.28 578.38,-4570.28"/>
<text text-anchor="middle" x="637.5" y="-4610.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckCardDetailsDialog</text>
</g>
<g id="clust82" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M416,-4265.28C416,-4265.28 514,-4265.28 514,-4265.28 520,-4265.28 526,-4271.28 526,-4277.28 526,-4277.28 526,-4306.28 526,-4306.28 526,-4312.28 520,-4318.28 514,-4318.28 514,-4318.28 416,-4318.28 416,-4318.28 410,-4318.28 404,-4312.28 404,-4306.28 404,-4306.28 404,-4277.28 404,-4277.28 404,-4271.28 410,-4265.28 416,-4265.28"/>
<text text-anchor="middle" x="465" y="-4305.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckDraftInitializer</text>
</g>
<g id="clust83" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M417.88,-4631.28C417.88,-4631.28 512.12,-4631.28 512.12,-4631.28 518.12,-4631.28 524.12,-4637.28 524.12,-4643.28 524.12,-4643.28 524.12,-4672.28 524.12,-4672.28 524.12,-4678.28 518.12,-4684.28 512.12,-4684.28 512.12,-4684.28 417.88,-4684.28 417.88,-4684.28 411.88,-4684.28 405.88,-4678.28 405.88,-4672.28 405.88,-4672.28 405.88,-4643.28 405.88,-4643.28 405.88,-4637.28 411.88,-4631.28 417.88,-4631.28"/>
<text text-anchor="middle" x="465" y="-4671.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">EditDeckInitializer</text>
</g>
<g id="clust84" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M592.25,-4631.28C592.25,-4631.28 682.75,-4631.28 682.75,-4631.28 688.75,-4631.28 694.75,-4637.28 694.75,-4643.28 694.75,-4643.28 694.75,-4672.28 694.75,-4672.28 694.75,-4678.28 688.75,-4684.28 682.75,-4684.28 682.75,-4684.28 592.25,-4684.28 592.25,-4684.28 586.25,-4684.28 580.25,-4678.28 580.25,-4672.28 580.25,-4672.28 580.25,-4643.28 580.25,-4643.28 580.25,-4637.28 586.25,-4631.28 592.25,-4631.28"/>
<text text-anchor="middle" x="637.5" y="-4671.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SaveDeckDialog</text>
</g>
<g id="clust85" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M574.25,-4356.28C574.25,-4356.28 700.75,-4356.28 700.75,-4356.28 706.75,-4356.28 712.75,-4362.28 712.75,-4368.28 712.75,-4368.28 712.75,-4397.28 712.75,-4397.28 712.75,-4403.28 706.75,-4409.28 700.75,-4409.28 700.75,-4409.28 574.25,-4409.28 574.25,-4409.28 568.25,-4409.28 562.25,-4403.28 562.25,-4397.28 562.25,-4397.28 562.25,-4368.28 562.25,-4368.28 562.25,-4362.28 568.25,-4356.28 574.25,-4356.28"/>
<text text-anchor="middle" x="637.5" y="-4396.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">TotalCardsInDeckCounter</text>
</g>
<g id="clust86" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Gaming</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M408,-4937.28C408,-4937.28 522,-4937.28 522,-4937.28 528,-4937.28 534,-4943.28 534,-4949.28 534,-4949.28 534,-5135.28 534,-5135.28 534,-5141.28 528,-5147.28 522,-5147.28 522,-5147.28 408,-5147.28 408,-5147.28 402,-5147.28 396,-5141.28 396,-5135.28 396,-5135.28 396,-4949.28 396,-4949.28 396,-4943.28 402,-4937.28 408,-4937.28"/>
<text text-anchor="middle" x="465" y="-5134.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Gaming</text>
</g>
<g id="clust87" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Gaming/LeaveMatchButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M416,-4945.28C416,-4945.28 514,-4945.28 514,-4945.28 520,-4945.28 526,-4951.28 526,-4957.28 526,-4957.28 526,-4986.28 526,-4986.28 526,-4992.28 520,-4998.28 514,-4998.28 514,-4998.28 416,-4998.28 416,-4998.28 410,-4998.28 404,-4992.28 404,-4986.28 404,-4986.28 404,-4957.28 404,-4957.28 404,-4951.28 410,-4945.28 416,-4945.28"/>
<text text-anchor="middle" x="465" y="-4985.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">LeaveMatchButton</text>
</g>
<g id="clust88" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Gaming/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M441.88,-5067.28C441.88,-5067.28 488.12,-5067.28 488.12,-5067.28 494.12,-5067.28 500.12,-5073.28 500.12,-5079.28 500.12,-5079.28 500.12,-5108.28 500.12,-5108.28 500.12,-5114.28 494.12,-5120.28 488.12,-5120.28 488.12,-5120.28 441.88,-5120.28 441.88,-5120.28 435.88,-5120.28 429.88,-5114.28 429.88,-5108.28 429.88,-5108.28 429.88,-5079.28 429.88,-5079.28 429.88,-5073.28 435.88,-5067.28 441.88,-5067.28"/>
<text text-anchor="middle" x="465" y="-5107.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust89" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Gaming/StartGameButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M419.38,-5006.28C419.38,-5006.28 510.62,-5006.28 510.62,-5006.28 516.62,-5006.28 522.62,-5012.28 522.62,-5018.28 522.62,-5018.28 522.62,-5047.28 522.62,-5047.28 522.62,-5053.28 516.62,-5059.28 510.62,-5059.28 510.62,-5059.28 419.38,-5059.28 419.38,-5059.28 413.38,-5059.28 407.38,-5053.28 407.38,-5047.28 407.38,-5047.28 407.38,-5018.28 407.38,-5018.28 407.38,-5012.28 413.38,-5006.28 419.38,-5006.28"/>
<text text-anchor="middle" x="465" y="-5046.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">StartGameButton</text>
</g>
<g id="clust90" class="cluster">
<title>cluster_src/client/infrastructure/components/debug</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M387.75,-3869.28C387.75,-3869.28 542.25,-3869.28 542.25,-3869.28 548.25,-3869.28 554.25,-3875.28 554.25,-3881.28 554.25,-3881.28 554.25,-4067.28 554.25,-4067.28 554.25,-4073.28 548.25,-4079.28 542.25,-4079.28 542.25,-4079.28 387.75,-4079.28 387.75,-4079.28 381.75,-4079.28 375.75,-4073.28 375.75,-4067.28 375.75,-4067.28 375.75,-3881.28 375.75,-3881.28 375.75,-3875.28 381.75,-3869.28 387.75,-3869.28"/>
<text text-anchor="middle" x="465" y="-4066.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">debug</text>
</g>
<g id="clust91" class="cluster">
<title>cluster_src/client/infrastructure/components/debug/JsonObjectViewer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M417.5,-3877.28C417.5,-3877.28 512.5,-3877.28 512.5,-3877.28 518.5,-3877.28 524.5,-3883.28 524.5,-3889.28 524.5,-3889.28 524.5,-3918.28 524.5,-3918.28 524.5,-3924.28 518.5,-3930.28 512.5,-3930.28 512.5,-3930.28 417.5,-3930.28 417.5,-3930.28 411.5,-3930.28 405.5,-3924.28 405.5,-3918.28 405.5,-3918.28 405.5,-3889.28 405.5,-3889.28 405.5,-3883.28 411.5,-3877.28 417.5,-3877.28"/>
<text text-anchor="middle" x="465" y="-3917.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">JsonObjectViewer</text>
</g>
<g id="clust92" class="cluster">
<title>cluster_src/client/infrastructure/components/debug/MatchConsoleEvents</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M410.75,-3938.28C410.75,-3938.28 519.25,-3938.28 519.25,-3938.28 525.25,-3938.28 531.25,-3944.28 531.25,-3950.28 531.25,-3950.28 531.25,-3979.28 531.25,-3979.28 531.25,-3985.28 525.25,-3991.28 519.25,-3991.28 519.25,-3991.28 410.75,-3991.28 410.75,-3991.28 404.75,-3991.28 398.75,-3985.28 398.75,-3979.28 398.75,-3979.28 398.75,-3950.28 398.75,-3950.28 398.75,-3944.28 404.75,-3938.28 410.75,-3938.28"/>
<text text-anchor="middle" x="465" y="-3978.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchConsoleEvents</text>
</g>
<g id="clust93" class="cluster">
<title>cluster_src/client/infrastructure/components/debug/MatchMakingConsoleEvents</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M395.75,-3999.28C395.75,-3999.28 534.25,-3999.28 534.25,-3999.28 540.25,-3999.28 546.25,-4005.28 546.25,-4011.28 546.25,-4011.28 546.25,-4040.28 546.25,-4040.28 546.25,-4046.28 540.25,-4052.28 534.25,-4052.28 534.25,-4052.28 395.75,-4052.28 395.75,-4052.28 389.75,-4052.28 383.75,-4046.28 383.75,-4040.28 383.75,-4040.28 383.75,-4011.28 383.75,-4011.28 383.75,-4005.28 389.75,-3999.28 395.75,-3999.28"/>
<text text-anchor="middle" x="465" y="-4039.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchMakingConsoleEvents</text>
</g>
<g id="clust94" class="cluster">
<title>cluster_src/client/infrastructure/components/redirections</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M404.62,-3773.28C404.62,-3773.28 525.38,-3773.28 525.38,-3773.28 531.38,-3773.28 537.38,-3779.28 537.38,-3785.28 537.38,-3785.28 537.38,-3849.28 537.38,-3849.28 537.38,-3855.28 531.38,-3861.28 525.38,-3861.28 525.38,-3861.28 404.62,-3861.28 404.62,-3861.28 398.62,-3861.28 392.62,-3855.28 392.62,-3849.28 392.62,-3849.28 392.62,-3785.28 392.62,-3785.28 392.62,-3779.28 398.62,-3773.28 404.62,-3773.28"/>
<text text-anchor="middle" x="465" y="-3848.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">redirections</text>
</g>
<g id="clust95" class="cluster">
<title>cluster_src/client/infrastructure/components/redirections/RedirectToGameList</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M412.62,-3781.28C412.62,-3781.28 517.38,-3781.28 517.38,-3781.28 523.38,-3781.28 529.38,-3787.28 529.38,-3793.28 529.38,-3793.28 529.38,-3822.28 529.38,-3822.28 529.38,-3828.28 523.38,-3834.28 517.38,-3834.28 517.38,-3834.28 412.62,-3834.28 412.62,-3834.28 406.62,-3834.28 400.62,-3828.28 400.62,-3822.28 400.62,-3822.28 400.62,-3793.28 400.62,-3793.28 400.62,-3787.28 406.62,-3781.28 412.62,-3781.28"/>
<text text-anchor="middle" x="465" y="-3821.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">RedirectToGameList</text>
</g>
<g id="clust96" class="cluster">
<title>cluster_src/client/infrastructure/components/ui</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M790.25,-4012.28C790.25,-4012.28 1096.38,-4012.28 1096.38,-4012.28 1102.38,-4012.28 1108.38,-4018.28 1108.38,-4024.28 1108.38,-4024.28 1108.38,-4332.28 1108.38,-4332.28 1108.38,-4338.28 1102.38,-4344.28 1096.38,-4344.28 1096.38,-4344.28 790.25,-4344.28 790.25,-4344.28 784.25,-4344.28 778.25,-4338.28 778.25,-4332.28 778.25,-4332.28 778.25,-4024.28 778.25,-4024.28 778.25,-4018.28 784.25,-4012.28 790.25,-4012.28"/>
<text text-anchor="middle" x="943.31" y="-4331.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ui</text>
</g>
<g id="clust97" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/Background</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M805,-4020.28C805,-4020.28 876,-4020.28 876,-4020.28 882,-4020.28 888,-4026.28 888,-4032.28 888,-4032.28 888,-4061.28 888,-4061.28 888,-4067.28 882,-4073.28 876,-4073.28 876,-4073.28 805,-4073.28 805,-4073.28 799,-4073.28 793,-4067.28 793,-4061.28 793,-4061.28 793,-4032.28 793,-4032.28 793,-4026.28 799,-4020.28 805,-4020.28"/>
<text text-anchor="middle" x="840.5" y="-4060.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Background</text>
</g>
<g id="clust98" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/ShiningButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M800.88,-4081.28C800.88,-4081.28 1088.38,-4081.28 1088.38,-4081.28 1094.38,-4081.28 1100.38,-4087.28 1100.38,-4093.28 1100.38,-4093.28 1100.38,-4122.28 1100.38,-4122.28 1100.38,-4128.28 1094.38,-4134.28 1088.38,-4134.28 1088.38,-4134.28 800.88,-4134.28 800.88,-4134.28 794.88,-4134.28 788.88,-4128.28 788.88,-4122.28 788.88,-4122.28 788.88,-4093.28 788.88,-4093.28 788.88,-4087.28 794.88,-4081.28 800.88,-4081.28"/>
<text text-anchor="middle" x="944.62" y="-4121.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ShiningButton</text>
</g>
<g id="clust99" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/ShiningCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M803.88,-4142.28C803.88,-4142.28 1085.38,-4142.28 1085.38,-4142.28 1091.38,-4142.28 1097.38,-4148.28 1097.38,-4154.28 1097.38,-4154.28 1097.38,-4183.28 1097.38,-4183.28 1097.38,-4189.28 1091.38,-4195.28 1085.38,-4195.28 1085.38,-4195.28 803.88,-4195.28 803.88,-4195.28 797.88,-4195.28 791.88,-4189.28 791.88,-4183.28 791.88,-4183.28 791.88,-4154.28 791.88,-4154.28 791.88,-4148.28 797.88,-4142.28 803.88,-4142.28"/>
<text text-anchor="middle" x="944.62" y="-4182.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ShiningCard</text>
</g>
<g id="clust100" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/SkeletonHelper</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M798.25,-4203.28C798.25,-4203.28 882.75,-4203.28 882.75,-4203.28 888.75,-4203.28 894.75,-4209.28 894.75,-4215.28 894.75,-4215.28 894.75,-4244.28 894.75,-4244.28 894.75,-4250.28 888.75,-4256.28 882.75,-4256.28 882.75,-4256.28 798.25,-4256.28 798.25,-4256.28 792.25,-4256.28 786.25,-4250.28 786.25,-4244.28 786.25,-4244.28 786.25,-4215.28 786.25,-4215.28 786.25,-4209.28 792.25,-4203.28 798.25,-4203.28"/>
<text text-anchor="middle" x="840.5" y="-4243.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SkeletonHelper</text>
</g>
<g id="clust101" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/Sparkles</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M811.75,-4264.28C811.75,-4264.28 869.25,-4264.28 869.25,-4264.28 875.25,-4264.28 881.25,-4270.28 881.25,-4276.28 881.25,-4276.28 881.25,-4305.28 881.25,-4305.28 881.25,-4311.28 875.25,-4317.28 869.25,-4317.28 869.25,-4317.28 811.75,-4317.28 811.75,-4317.28 805.75,-4317.28 799.75,-4311.28 799.75,-4305.28 799.75,-4305.28 799.75,-4276.28 799.75,-4276.28 799.75,-4270.28 805.75,-4264.28 811.75,-4264.28"/>
<text text-anchor="middle" x="840.5" y="-4304.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Sparkles</text>
</g>
<g id="clust102" class="cluster">
<title>cluster_src/client/infrastructure/hooks</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M746.38,-2998.28C746.38,-2998.28 1093,-2998.28 1093,-2998.28 1099,-2998.28 1105,-3004.28 1105,-3010.28 1105,-3010.28 1105,-3588.28 1105,-3588.28 1105,-3594.28 1099,-3600.28 1093,-3600.28 1093,-3600.28 746.38,-3600.28 746.38,-3600.28 740.38,-3600.28 734.38,-3594.28 734.38,-3588.28 734.38,-3588.28 734.38,-3010.28 734.38,-3010.28 734.38,-3004.28 740.38,-2998.28 746.38,-2998.28"/>
<text text-anchor="middle" x="919.69" y="-3587.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hooks</text>
</g>
<g id="clust103" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useCatalogCardsByGameId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M785,-3067.28C785,-3067.28 897,-3067.28 897,-3067.28 903,-3067.28 909,-3073.28 909,-3079.28 909,-3079.28 909,-3108.28 909,-3108.28 909,-3114.28 903,-3120.28 897,-3120.28 897,-3120.28 785,-3120.28 785,-3120.28 779,-3120.28 773,-3114.28 773,-3108.28 773,-3108.28 773,-3079.28 773,-3079.28 773,-3073.28 779,-3067.28 785,-3067.28"/>
<text text-anchor="middle" x="841" y="-3107.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useCatalogCardsByGameId</text>
</g>
<g id="clust104" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useDebounce</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M817.5,-3128.28C817.5,-3128.28 1085,-3128.28 1085,-3128.28 1091,-3128.28 1097,-3134.28 1097,-3140.28 1097,-3140.28 1097,-3169.28 1097,-3169.28 1097,-3175.28 1091,-3181.28 1085,-3181.28 1085,-3181.28 817.5,-3181.28 817.5,-3181.28 811.5,-3181.28 805.5,-3175.28 805.5,-3169.28 805.5,-3169.28 805.5,-3140.28 805.5,-3140.28 805.5,-3134.28 811.5,-3128.28 817.5,-3128.28"/>
<text text-anchor="middle" x="951.25" y="-3168.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDebounce</text>
</g>
<g id="clust105" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useDeckBuilder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M799.75,-3006.28C799.75,-3006.28 881.25,-3006.28 881.25,-3006.28 887.25,-3006.28 893.25,-3012.28 893.25,-3018.28 893.25,-3018.28 893.25,-3047.28 893.25,-3047.28 893.25,-3053.28 887.25,-3059.28 881.25,-3059.28 881.25,-3059.28 799.75,-3059.28 799.75,-3059.28 793.75,-3059.28 787.75,-3053.28 787.75,-3047.28 787.75,-3047.28 787.75,-3018.28 787.75,-3018.28 787.75,-3012.28 793.75,-3006.28 799.75,-3006.28"/>
<text text-anchor="middle" x="840.5" y="-3046.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDeckBuilder</text>
</g>
<g id="clust106" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useDeckById</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M802.75,-3189.28C802.75,-3189.28 878.25,-3189.28 878.25,-3189.28 884.25,-3189.28 890.25,-3195.28 890.25,-3201.28 890.25,-3201.28 890.25,-3230.28 890.25,-3230.28 890.25,-3236.28 884.25,-3242.28 878.25,-3242.28 878.25,-3242.28 802.75,-3242.28 802.75,-3242.28 796.75,-3242.28 790.75,-3236.28 790.75,-3230.28 790.75,-3230.28 790.75,-3201.28 790.75,-3201.28 790.75,-3195.28 796.75,-3189.28 802.75,-3189.28"/>
<text text-anchor="middle" x="840.5" y="-3229.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDeckById</text>
</g>
<g id="clust107" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useDeckId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M810.25,-3250.28C810.25,-3250.28 870.75,-3250.28 870.75,-3250.28 876.75,-3250.28 882.75,-3256.28 882.75,-3262.28 882.75,-3262.28 882.75,-3291.28 882.75,-3291.28 882.75,-3297.28 876.75,-3303.28 870.75,-3303.28 870.75,-3303.28 810.25,-3303.28 810.25,-3303.28 804.25,-3303.28 798.25,-3297.28 798.25,-3291.28 798.25,-3291.28 798.25,-3262.28 798.25,-3262.28 798.25,-3256.28 804.25,-3250.28 810.25,-3250.28"/>
<text text-anchor="middle" x="840.5" y="-3290.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDeckId</text>
</g>
<g id="clust108" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useGameId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M808.38,-3433.28C808.38,-3433.28 872.62,-3433.28 872.62,-3433.28 878.62,-3433.28 884.62,-3439.28 884.62,-3445.28 884.62,-3445.28 884.62,-3474.28 884.62,-3474.28 884.62,-3480.28 878.62,-3486.28 872.62,-3486.28 872.62,-3486.28 808.38,-3486.28 808.38,-3486.28 802.38,-3486.28 796.38,-3480.28 796.38,-3474.28 796.38,-3474.28 796.38,-3445.28 796.38,-3445.28 796.38,-3439.28 802.38,-3433.28 808.38,-3433.28"/>
<text text-anchor="middle" x="840.5" y="-3473.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useGameId</text>
</g>
<g id="clust109" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useGameSettingsByGameId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M772,-3520.28C772,-3520.28 909,-3520.28 909,-3520.28 915,-3520.28 921,-3526.28 921,-3532.28 921,-3532.28 921,-3561.28 921,-3561.28 921,-3567.28 915,-3573.28 909,-3573.28 909,-3573.28 772,-3573.28 772,-3573.28 766,-3573.28 760,-3567.28 760,-3561.28 760,-3561.28 760,-3532.28 760,-3532.28 760,-3526.28 766,-3520.28 772,-3520.28"/>
<text text-anchor="middle" x="840.5" y="-3560.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useGameSettingsByGameId</text>
</g>
<g id="clust110" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M754.38,-3311.28C754.38,-3311.28 926.62,-3311.28 926.62,-3311.28 932.62,-3311.28 938.62,-3317.28 938.62,-3323.28 938.62,-3323.28 938.62,-3352.28 938.62,-3352.28 938.62,-3358.28 932.62,-3364.28 926.62,-3364.28 926.62,-3364.28 754.38,-3364.28 754.38,-3364.28 748.38,-3364.28 742.38,-3358.28 742.38,-3352.28 742.38,-3352.28 742.38,-3323.28 742.38,-3323.28 742.38,-3317.28 748.38,-3311.28 754.38,-3311.28"/>
<text text-anchor="middle" x="840.5" y="-3351.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useInitializeDeckBuilderFromLocation</text>
</g>
<g id="clust111" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useLocale</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M810.62,-3372.28C810.62,-3372.28 870.38,-3372.28 870.38,-3372.28 876.38,-3372.28 882.38,-3378.28 882.38,-3384.28 882.38,-3384.28 882.38,-3413.28 882.38,-3413.28 882.38,-3419.28 876.38,-3425.28 870.38,-3425.28 870.38,-3425.28 810.62,-3425.28 810.62,-3425.28 804.62,-3425.28 798.62,-3419.28 798.62,-3413.28 798.62,-3413.28 798.62,-3384.28 798.62,-3384.28 798.62,-3378.28 804.62,-3372.28 810.62,-3372.28"/>
<text text-anchor="middle" x="840.5" y="-3412.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useLocale</text>
</g>
<g id="clust112" class="cluster">
<title>cluster_src/client/infrastructure/layouts</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M414,-3039.28C414,-3039.28 516,-3039.28 516,-3039.28 522,-3039.28 528,-3045.28 528,-3051.28 528,-3051.28 528,-3176.28 528,-3176.28 528,-3182.28 522,-3188.28 516,-3188.28 516,-3188.28 414,-3188.28 414,-3188.28 408,-3188.28 402,-3182.28 402,-3176.28 402,-3176.28 402,-3051.28 402,-3051.28 402,-3045.28 408,-3039.28 414,-3039.28"/>
<text text-anchor="middle" x="465" y="-3175.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">layouts</text>
</g>
<g id="clust113" class="cluster">
<title>cluster_src/client/infrastructure/layouts/FullPageLayout</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M422,-3108.28C422,-3108.28 508,-3108.28 508,-3108.28 514,-3108.28 520,-3114.28 520,-3120.28 520,-3120.28 520,-3149.28 520,-3149.28 520,-3155.28 514,-3161.28 508,-3161.28 508,-3161.28 422,-3161.28 422,-3161.28 416,-3161.28 410,-3155.28 410,-3149.28 410,-3149.28 410,-3120.28 410,-3120.28 410,-3114.28 416,-3108.28 422,-3108.28"/>
<text text-anchor="middle" x="465" y="-3148.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">FullPageLayout</text>
</g>
<g id="clust114" class="cluster">
<title>cluster_src/client/infrastructure/layouts/RootLayout</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M430.62,-3047.28C430.62,-3047.28 499.38,-3047.28 499.38,-3047.28 505.38,-3047.28 511.38,-3053.28 511.38,-3059.28 511.38,-3059.28 511.38,-3088.28 511.38,-3088.28 511.38,-3094.28 505.38,-3100.28 499.38,-3100.28 499.38,-3100.28 430.62,-3100.28 430.62,-3100.28 424.62,-3100.28 418.62,-3094.28 418.62,-3088.28 418.62,-3088.28 418.62,-3059.28 418.62,-3059.28 418.62,-3053.28 424.62,-3047.28 430.62,-3047.28"/>
<text text-anchor="middle" x="465" y="-3087.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">RootLayout</text>
</g>
<g id="clust115" class="cluster">
<title>cluster_src/client/infrastructure/lib</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M995.25,-5209.28C995.25,-5209.28 1100,-5209.28 1100,-5209.28 1106,-5209.28 1112,-5215.28 1112,-5221.28 1112,-5221.28 1112,-5312.28 1112,-5312.28 1112,-5318.28 1106,-5324.28 1100,-5324.28 1100,-5324.28 995.25,-5324.28 995.25,-5324.28 989.25,-5324.28 983.25,-5318.28 983.25,-5312.28 983.25,-5312.28 983.25,-5221.28 983.25,-5221.28 983.25,-5215.28 989.25,-5209.28 995.25,-5209.28"/>
<text text-anchor="middle" x="1047.62" y="-5311.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">lib</text>
</g>
<g id="clust116" class="cluster">
<title>cluster_src/client/infrastructure/pages</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M216.25,-3198.28C216.25,-3198.28 542,-3198.28 542,-3198.28 548,-3198.28 554,-3204.28 554,-3210.28 554,-3210.28 554,-3745.28 554,-3745.28 554,-3751.28 548,-3757.28 542,-3757.28 542,-3757.28 216.25,-3757.28 216.25,-3757.28 210.25,-3757.28 204.25,-3751.28 204.25,-3745.28 204.25,-3745.28 204.25,-3210.28 204.25,-3210.28 204.25,-3204.28 210.25,-3198.28 216.25,-3198.28"/>
<text text-anchor="middle" x="379.12" y="-3744.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">pages</text>
</g>
<g id="clust117" class="cluster">
<title>cluster_src/client/infrastructure/pages/Auth</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M224.25,-3363.28C224.25,-3363.28 342.75,-3363.28 342.75,-3363.28 348.75,-3363.28 354.75,-3369.28 354.75,-3375.28 354.75,-3375.28 354.75,-3500.28 354.75,-3500.28 354.75,-3506.28 348.75,-3512.28 342.75,-3512.28 342.75,-3512.28 224.25,-3512.28 224.25,-3512.28 218.25,-3512.28 212.25,-3506.28 212.25,-3500.28 212.25,-3500.28 212.25,-3375.28 212.25,-3375.28 212.25,-3369.28 218.25,-3363.28 224.25,-3363.28"/>
<text text-anchor="middle" x="283.5" y="-3499.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Auth</text>
</g>
<g id="clust118" class="cluster">
<title>cluster_src/client/infrastructure/pages/Auth/AccessDeniedPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M232.25,-3432.28C232.25,-3432.28 334.75,-3432.28 334.75,-3432.28 340.75,-3432.28 346.75,-3438.28 346.75,-3444.28 346.75,-3444.28 346.75,-3473.28 346.75,-3473.28 346.75,-3479.28 340.75,-3485.28 334.75,-3485.28 334.75,-3485.28 232.25,-3485.28 232.25,-3485.28 226.25,-3485.28 220.25,-3479.28 220.25,-3473.28 220.25,-3473.28 220.25,-3444.28 220.25,-3444.28 220.25,-3438.28 226.25,-3432.28 232.25,-3432.28"/>
<text text-anchor="middle" x="283.5" y="-3472.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AccessDeniedPage</text>
</g>
<g id="clust119" class="cluster">
<title>cluster_src/client/infrastructure/pages/Auth/SignInPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M248.75,-3371.28C248.75,-3371.28 318.25,-3371.28 318.25,-3371.28 324.25,-3371.28 330.25,-3377.28 330.25,-3383.28 330.25,-3383.28 330.25,-3412.28 330.25,-3412.28 330.25,-3418.28 324.25,-3424.28 318.25,-3424.28 318.25,-3424.28 248.75,-3424.28 248.75,-3424.28 242.75,-3424.28 236.75,-3418.28 236.75,-3412.28 236.75,-3412.28 236.75,-3383.28 236.75,-3383.28 236.75,-3377.28 242.75,-3371.28 248.75,-3371.28"/>
<text text-anchor="middle" x="283.5" y="-3411.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignInPage</text>
</g>
<g id="clust120" class="cluster">
<title>cluster_src/client/infrastructure/pages/Catalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M227.25,-3520.28C227.25,-3520.28 339.75,-3520.28 339.75,-3520.28 345.75,-3520.28 351.75,-3526.28 351.75,-3532.28 351.75,-3532.28 351.75,-3718.28 351.75,-3718.28 351.75,-3724.28 345.75,-3730.28 339.75,-3730.28 339.75,-3730.28 227.25,-3730.28 227.25,-3730.28 221.25,-3730.28 215.25,-3724.28 215.25,-3718.28 215.25,-3718.28 215.25,-3532.28 215.25,-3532.28 215.25,-3526.28 221.25,-3520.28 227.25,-3520.28"/>
<text text-anchor="middle" x="283.5" y="-3717.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Catalog</text>
</g>
<g id="clust121" class="cluster">
<title>cluster_src/client/infrastructure/pages/Catalog/GameDetailsPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M235.25,-3650.28C235.25,-3650.28 331.75,-3650.28 331.75,-3650.28 337.75,-3650.28 343.75,-3656.28 343.75,-3662.28 343.75,-3662.28 343.75,-3691.28 343.75,-3691.28 343.75,-3697.28 337.75,-3703.28 331.75,-3703.28 331.75,-3703.28 235.25,-3703.28 235.25,-3703.28 229.25,-3703.28 223.25,-3697.28 223.25,-3691.28 223.25,-3691.28 223.25,-3662.28 223.25,-3662.28 223.25,-3656.28 229.25,-3650.28 235.25,-3650.28"/>
<text text-anchor="middle" x="283.5" y="-3690.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameDetailsPage</text>
</g>
<g id="clust122" class="cluster">
<title>cluster_src/client/infrastructure/pages/Catalog/GameListPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M242.38,-3528.28C242.38,-3528.28 324.62,-3528.28 324.62,-3528.28 330.62,-3528.28 336.62,-3534.28 336.62,-3540.28 336.62,-3540.28 336.62,-3569.28 336.62,-3569.28 336.62,-3575.28 330.62,-3581.28 324.62,-3581.28 324.62,-3581.28 242.38,-3581.28 242.38,-3581.28 236.38,-3581.28 230.38,-3575.28 230.38,-3569.28 230.38,-3569.28 230.38,-3540.28 230.38,-3540.28 230.38,-3534.28 236.38,-3528.28 242.38,-3528.28"/>
<text text-anchor="middle" x="283.5" y="-3568.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameListPage</text>
</g>
<g id="clust123" class="cluster">
<title>cluster_src/client/infrastructure/pages/Catalog/PlayGamePage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M240.5,-3589.28C240.5,-3589.28 326.5,-3589.28 326.5,-3589.28 332.5,-3589.28 338.5,-3595.28 338.5,-3601.28 338.5,-3601.28 338.5,-3630.28 338.5,-3630.28 338.5,-3636.28 332.5,-3642.28 326.5,-3642.28 326.5,-3642.28 240.5,-3642.28 240.5,-3642.28 234.5,-3642.28 228.5,-3636.28 228.5,-3630.28 228.5,-3630.28 228.5,-3601.28 228.5,-3601.28 228.5,-3595.28 234.5,-3589.28 240.5,-3589.28"/>
<text text-anchor="middle" x="283.5" y="-3629.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">PlayGamePage</text>
</g>
<g id="clust124" class="cluster">
<title>cluster_src/client/infrastructure/pages/Gaming</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M241.5,-3206.28C241.5,-3206.28 534,-3206.28 534,-3206.28 540,-3206.28 546,-3212.28 546,-3218.28 546,-3218.28 546,-3343.28 546,-3343.28 546,-3349.28 540,-3355.28 534,-3355.28 534,-3355.28 241.5,-3355.28 241.5,-3355.28 235.5,-3355.28 229.5,-3349.28 229.5,-3343.28 229.5,-3343.28 229.5,-3218.28 229.5,-3218.28 229.5,-3212.28 235.5,-3206.28 241.5,-3206.28"/>
<text text-anchor="middle" x="387.75" y="-3342.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Gaming</text>
</g>
<g id="clust125" class="cluster">
<title>cluster_src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M404,-3214.28C404,-3214.28 526,-3214.28 526,-3214.28 532,-3214.28 538,-3220.28 538,-3226.28 538,-3226.28 538,-3255.28 538,-3255.28 538,-3261.28 532,-3267.28 526,-3267.28 526,-3267.28 404,-3267.28 404,-3267.28 398,-3267.28 392,-3261.28 392,-3255.28 392,-3255.28 392,-3226.28 392,-3226.28 392,-3220.28 398,-3214.28 404,-3214.28"/>
<text text-anchor="middle" x="465" y="-3254.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ErrorLoadingMatchPage</text>
</g>
<g id="clust126" class="cluster">
<title>cluster_src/client/infrastructure/pages/Gaming/FinishedMatchPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M413.38,-3275.28C413.38,-3275.28 516.62,-3275.28 516.62,-3275.28 522.62,-3275.28 528.62,-3281.28 528.62,-3287.28 528.62,-3287.28 528.62,-3316.28 528.62,-3316.28 528.62,-3322.28 522.62,-3328.28 516.62,-3328.28 516.62,-3328.28 413.38,-3328.28 413.38,-3328.28 407.38,-3328.28 401.38,-3322.28 401.38,-3316.28 401.38,-3316.28 401.38,-3287.28 401.38,-3287.28 401.38,-3281.28 407.38,-3275.28 413.38,-3275.28"/>
<text text-anchor="middle" x="465" y="-3315.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">FinishedMatchPage</text>
</g>
<g id="clust127" class="cluster">
<title>cluster_src/client/infrastructure/pages/Gaming/MatchPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M249.5,-3275.28C249.5,-3275.28 317.5,-3275.28 317.5,-3275.28 323.5,-3275.28 329.5,-3281.28 329.5,-3287.28 329.5,-3287.28 329.5,-3316.28 329.5,-3316.28 329.5,-3322.28 323.5,-3328.28 317.5,-3328.28 317.5,-3328.28 249.5,-3328.28 249.5,-3328.28 243.5,-3328.28 237.5,-3322.28 237.5,-3316.28 237.5,-3316.28 237.5,-3287.28 237.5,-3287.28 237.5,-3281.28 243.5,-3275.28 249.5,-3275.28"/>
<text text-anchor="middle" x="283.5" y="-3315.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchPage</text>
</g>
<g id="clust128" class="cluster">
<title>cluster_src/client/infrastructure/providers</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M582.12,-3457.28C582.12,-3457.28 692.88,-3457.28 692.88,-3457.28 698.88,-3457.28 704.88,-3463.28 704.88,-3469.28 704.88,-3469.28 704.88,-3560.28 704.88,-3560.28 704.88,-3566.28 698.88,-3572.28 692.88,-3572.28 692.88,-3572.28 582.12,-3572.28 582.12,-3572.28 576.12,-3572.28 570.12,-3566.28 570.12,-3560.28 570.12,-3560.28 570.12,-3469.28 570.12,-3469.28 570.12,-3463.28 576.12,-3457.28 582.12,-3457.28"/>
<text text-anchor="middle" x="637.5" y="-3559.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">providers</text>
</g>
<g id="clust129" class="cluster">
<title>cluster_src/client/infrastructure/services</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M979,-3608.28C979,-3608.28 1116.25,-3608.28 1116.25,-3608.28 1122.25,-3608.28 1128.25,-3614.28 1128.25,-3620.28 1128.25,-3620.28 1128.25,-3745.28 1128.25,-3745.28 1128.25,-3751.28 1122.25,-3757.28 1116.25,-3757.28 1116.25,-3757.28 979,-3757.28 979,-3757.28 973,-3757.28 967,-3751.28 967,-3745.28 967,-3745.28 967,-3620.28 967,-3620.28 967,-3614.28 973,-3608.28 979,-3608.28"/>
<text text-anchor="middle" x="1047.62" y="-3744.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">services</text>
</g>
<g id="clust130" class="cluster">
<title>cluster_src/client/infrastructure/services/deckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M987,-3616.28C987,-3616.28 1108.25,-3616.28 1108.25,-3616.28 1114.25,-3616.28 1120.25,-3622.28 1120.25,-3628.28 1120.25,-3628.28 1120.25,-3657.28 1120.25,-3657.28 1120.25,-3663.28 1114.25,-3669.28 1108.25,-3669.28 1108.25,-3669.28 987,-3669.28 987,-3669.28 981,-3669.28 975,-3663.28 975,-3657.28 975,-3657.28 975,-3628.28 975,-3628.28 975,-3622.28 981,-3616.28 987,-3616.28"/>
<text text-anchor="middle" x="1047.62" y="-3656.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">deckDraft</text>
</g>
<g id="clust131" class="cluster">
<title>cluster_src/client/infrastructure/services/location</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M989.62,-3677.28C989.62,-3677.28 1105.62,-3677.28 1105.62,-3677.28 1111.62,-3677.28 1117.62,-3683.28 1117.62,-3689.28 1117.62,-3689.28 1117.62,-3718.28 1117.62,-3718.28 1117.62,-3724.28 1111.62,-3730.28 1105.62,-3730.28 1105.62,-3730.28 989.62,-3730.28 989.62,-3730.28 983.62,-3730.28 977.62,-3724.28 977.62,-3718.28 977.62,-3718.28 977.62,-3689.28 977.62,-3689.28 977.62,-3683.28 983.62,-3677.28 989.62,-3677.28"/>
<text text-anchor="middle" x="1047.62" y="-3717.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">location</text>
</g>
<g id="clust132" class="cluster">
<title>cluster_src/client/infrastructure/store</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M817.5,-3612.28C817.5,-3612.28 863.5,-3612.28 863.5,-3612.28 869.5,-3612.28 875.5,-3618.28 875.5,-3624.28 875.5,-3624.28 875.5,-3653.28 875.5,-3653.28 875.5,-3659.28 869.5,-3665.28 863.5,-3665.28 863.5,-3665.28 817.5,-3665.28 817.5,-3665.28 811.5,-3665.28 805.5,-3659.28 805.5,-3653.28 805.5,-3653.28 805.5,-3624.28 805.5,-3624.28 805.5,-3618.28 811.5,-3612.28 817.5,-3612.28"/>
<text text-anchor="middle" x="840.5" y="-3652.73" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">store</text>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/loading.tsx -->
<g id="node1" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/loading.tsx</title>
<g id="a_node1"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/[deckId]/loading.tsx" xlink:title="loading.tsx">
<path fill="#ccffcc" stroke="black" d="M118.08,-6128.53C118.08,-6128.53 70.17,-6128.53 70.17,-6128.53 67.08,-6128.53 64,-6125.45 64,-6122.36 64,-6122.36 64,-6116.2 64,-6116.2 64,-6113.11 67.08,-6110.03 70.17,-6110.03 70.17,-6110.03 118.08,-6110.03 118.08,-6110.03 121.17,-6110.03 124.25,-6113.11 124.25,-6116.2 124.25,-6116.2 124.25,-6122.36 124.25,-6122.36 124.25,-6125.45 121.17,-6128.53 118.08,-6128.53"/>
<text text-anchor="start" x="72" y="-6115.98" font-family="Helvetica,sans-Serif" font-size="9.00">loading.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx -->
<g id="node2" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx</title>
<g id="a_node2"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/[deckId]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-6159.53C114.96,-6159.53 73.29,-6159.53 73.29,-6159.53 70.21,-6159.53 67.12,-6156.45 67.12,-6153.36 67.12,-6153.36 67.12,-6147.2 67.12,-6147.2 67.12,-6144.11 70.21,-6141.03 73.29,-6141.03 73.29,-6141.03 114.96,-6141.03 114.96,-6141.03 118.04,-6141.03 121.12,-6144.11 121.12,-6147.2 121.12,-6147.2 121.12,-6153.36 121.12,-6153.36 121.12,-6156.45 118.04,-6159.53 114.96,-6159.53"/>
<text text-anchor="start" x="76.88" y="-6146.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx -->
<g id="node3" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx</title>
<g id="a_node3"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx" xlink:title="DeckBuilderCardsGrid.tsx">
<polygon fill="#6cbaff" stroke="black" points="525.88,-4413.53 404.12,-4413.53 404.12,-4395.03 525.88,-4395.03 525.88,-4413.53"/>
<text text-anchor="start" x="412.12" y="-4400.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderCardsGrid.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx -->
<g id="edge1" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.56,-6152.52C138.82,-6152.39 160.3,-6148.88 172.25,-6134.28 185,-6118.71 171.65,-4703.48 180.25,-4685.28 223.34,-4594.11 308.12,-4629.69 354.75,-4540.28 364.55,-4521.5 350.18,-4462.34 362.75,-4445.28 372.48,-4432.08 387.19,-4423.11 402.41,-4417.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="403.12,-4419.01 408.04,-4414.98 401.68,-4415.06 403.12,-4419.01"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx -->
<g id="node4" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx</title>
<g id="a_node4"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx" xlink:title="DeckBuilderPanel.tsx">
<polygon fill="#6cbaff" stroke="black" points="516.88,-4535.53 413.12,-4535.53 413.12,-4517.03 516.88,-4517.03 516.88,-4535.53"/>
<text text-anchor="start" x="421.12" y="-4522.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderPanel.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx -->
<g id="edge2" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.55,-6152.52C138.81,-6152.38 160.29,-6148.88 172.25,-6134.28 182.31,-6122 176.14,-5006.61 180.25,-4991.28 220.89,-4839.69 303.98,-4832.79 354.75,-4684.28 362.88,-4660.49 351.55,-4650.79 362.75,-4628.28 381.31,-4590.99 418.72,-4558.96 442.5,-4541.19"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="443.65,-4542.95 447.26,-4537.72 441.17,-4539.56 443.65,-4542.95"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx -->
<g id="node5" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx</title>
<g id="a_node5"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx" xlink:title="DeckBuildingFilters.tsx">
<polygon fill="#6cbaff" stroke="black" points="519.5,-4718.53 410.5,-4718.53 410.5,-4700.03 519.5,-4700.03 519.5,-4718.53"/>
<text text-anchor="start" x="418.5" y="-4705.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuildingFilters.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx -->
<g id="edge3" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.52,-6152.5C138.77,-6152.35 160.24,-6148.84 172.25,-6134.28 183.12,-6121.1 175.44,-5535.68 180.25,-5519.28 221.5,-5378.57 313.18,-5379.9 354.75,-5239.28 361.9,-5215.09 351.27,-4807.75 362.75,-4785.28 377.57,-4756.26 409.18,-4735.37 433.16,-4722.92"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="433.83,-4724.93 438.26,-4720.37 431.95,-4721.17 433.83,-4724.93"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx -->
<g id="node6" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx</title>
<g id="a_node6"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx" xlink:title="DeckDraftInitializer.tsx">
<polygon fill="#6cbaff" stroke="black" points="518,-4291.53 412,-4291.53 412,-4273.03 518,-4273.03 518,-4291.53"/>
<text text-anchor="start" x="420" y="-4278.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckDraftInitializer.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx -->
<g id="edge4" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.56,-6152.53C138.82,-6152.4 160.3,-6148.89 172.25,-6134.28 186.29,-6117.11 172.53,-4558.07 180.25,-4537.28 223.77,-4420.1 261.36,-4396.39 362.75,-4323.28 379.96,-4310.87 401.46,-4301.48 420.22,-4294.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="420.75,-4296.87 425.75,-4292.95 419.4,-4292.9 420.75,-4296.87"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx -->
<g id="node7" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx</title>
<g id="a_node7"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx" xlink:title="EditDeckInitializer.tsx">
<polygon fill="#6cbaff" stroke="black" points="516.12,-4657.53 413.88,-4657.53 413.88,-4639.03 516.12,-4639.03 516.12,-4657.53"/>
<text text-anchor="start" x="421.88" y="-4644.98" font-family="Helvetica,sans-Serif" font-size="9.00">EditDeckInitializer.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx -->
<g id="edge5" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.54,-6152.51C138.8,-6152.37 160.27,-6148.86 172.25,-6134.28 188.01,-6115.09 172.69,-5263.94 180.25,-5240.28 221.48,-5111.2 312.39,-5118.99 354.75,-4990.28 359.98,-4974.39 353.22,-4703.03 362.75,-4689.28 372.73,-4674.88 388.7,-4665.54 405.03,-4659.47"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="405.69,-4661.46 410.71,-4657.56 404.35,-4657.48 405.69,-4661.46"/>
</g>
<!-- src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="node106" class="node">
<title>src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<g id="a_node106"><a xlink:href="src/client/infrastructure/hooks/useLocale/useLocale.ts" xlink:title="useLocale.ts">
<polygon fill="#6cbaff" stroke="black" points="874.38,-3398.53 806.62,-3398.53 806.62,-3380.03 874.38,-3380.03 874.38,-3398.53"/>
<text text-anchor="start" x="814.62" y="-3385.98" font-family="Helvetica,sans-Serif" font-size="9.00">useLocale.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge146" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M526.31,-4400.35C537.14,-4396.85 547.2,-4391.2 554.25,-4382.28 567.23,-4365.84 548.04,-3643.67 562.25,-3628.28 587.46,-3600.99 703.27,-3640.31 728.75,-3613.28 742.7,-3598.48 722.64,-3446.9 734.38,-3430.28 748.79,-3409.87 774.94,-3399.62 797.55,-3394.47"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="797.91,-3396.54 803.37,-3393.28 797.07,-3392.43 797.91,-3396.54"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx -->
<g id="node109" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx</title>
<g id="a_node109"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx" xlink:title="DeckBuildingCard.tsx">
<polygon fill="#6cbaff" stroke="black" points="689.75,-4321.53 585.25,-4321.53 585.25,-4303.03 689.75,-4303.03 689.75,-4321.53"/>
<text text-anchor="start" x="593.25" y="-4308.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuildingCard.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx -->
<g id="edge140" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M525.99,-4398.39C536.44,-4394.99 546.45,-4389.89 554.25,-4382.28 563.82,-4372.94 553.97,-4363.78 562.25,-4353.28 571.76,-4341.22 585.86,-4332.17 599.22,-4325.7"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="600.04,-4327.63 604.64,-4323.24 598.3,-4323.8 600.04,-4327.63"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx -->
<g id="node110" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx</title>
<g id="a_node110"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx" xlink:title="TotalCardsInDeckCounter.tsx">
<polygon fill="#6cbaff" stroke="black" points="704.75,-4382.53 570.25,-4382.53 570.25,-4364.03 704.75,-4364.03 704.75,-4382.53"/>
<text text-anchor="start" x="578.25" y="-4369.98" font-family="Helvetica,sans-Serif" font-size="9.00">TotalCardsInDeckCounter.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx -->
<g id="edge141" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M519.48,-4394.56C536.85,-4391.4 556.28,-4387.87 574.24,-4384.6"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="574.41,-4386.7 579.94,-4383.56 573.66,-4382.57 574.41,-4386.7"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx -->
<g id="node111" class="node">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx</title>
<g id="a_node111"><a xlink:href="src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx" xlink:title="useCardsByGameId.tsx">
<polygon fill="#6cbaff" stroke="black" points="896.5,-3093.53 784.5,-3093.53 784.5,-3075.03 896.5,-3075.03 896.5,-3093.53"/>
<text text-anchor="start" x="792.5" y="-3080.98" font-family="Helvetica,sans-Serif" font-size="9.00">useCardsByGameId.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx -->
<g id="edge142" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M526.32,-4400.35C537.14,-4396.85 547.2,-4391.2 554.25,-4382.28 567.71,-4365.23 547.51,-3616.24 562.25,-3600.28 587.45,-3572.99 703.52,-3612.55 728.75,-3585.28 737.43,-3575.9 727.1,-3135.79 734.38,-3125.28 744.16,-3111.15 759.58,-3101.89 775.62,-3095.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="776.19,-3097.85 781.18,-3093.92 774.82,-3093.88 776.19,-3097.85"/>
</g>
<!-- src/client/infrastructure/hooks/useDebounce/index.ts -->
<g id="node112" class="node">
<title>src/client/infrastructure/hooks/useDebounce/index.ts</title>
<g id="a_node112"><a xlink:href="src/client/infrastructure/hooks/useDebounce/index.ts" xlink:title="index.ts">
<polygon fill="#6cbaff" stroke="black" points="867.5,-3154.53 813.5,-3154.53 813.5,-3136.03 867.5,-3136.03 867.5,-3154.53"/>
<text text-anchor="start" x="824.75" y="-3141.98" font-family="Helvetica,sans-Serif" font-size="9.00">index.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useDebounce/index.ts -->
<g id="edge143" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useDebounce/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M526.31,-4400.35C537.14,-4396.85 547.2,-4391.2 554.25,-4382.28 567.47,-4365.54 547.78,-3629.95 562.25,-3614.28 587.46,-3586.99 703.5,-3626.53 728.75,-3599.28 736.55,-3590.86 727.84,-3195.71 734.38,-3186.28 750.11,-3163.58 780.4,-3153.45 804.44,-3148.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="804.74,-3151.01 810.32,-3147.95 804.05,-3146.86 804.74,-3151.01"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="node113" class="node">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<g id="a_node113"><a xlink:href="src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts" xlink:title="useDeckBuilder.ts">
<polygon fill="#6cbaff" stroke="black" points="885.25,-3032.53 795.75,-3032.53 795.75,-3014.03 885.25,-3014.03 885.25,-3032.53"/>
<text text-anchor="start" x="803.75" y="-3019.98" font-family="Helvetica,sans-Serif" font-size="9.00">useDeckBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge144" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M526.32,-4400.35C537.14,-4396.85 547.21,-4391.2 554.25,-4382.28 567.95,-4364.93 547.06,-3602.35 562.25,-3586.28 587.73,-3559.34 703.26,-3602.21 728.75,-3575.28 738.51,-3564.97 726.3,-3075.96 734.38,-3064.28 746.41,-3046.88 767,-3036.86 786.79,-3031.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="787.3,-3033.13 792.57,-3029.58 786.23,-3029.07 787.3,-3033.13"/>
</g>
<!-- src/client/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="node114" class="node">
<title>src/client/infrastructure/hooks/useGameId/useGameId.ts</title>
<g id="a_node114"><a xlink:href="src/client/infrastructure/hooks/useGameId/useGameId.ts" xlink:title="useGameId.ts">
<polygon fill="#6cbaff" stroke="black" points="876.62,-3459.53 804.38,-3459.53 804.38,-3441.03 876.62,-3441.03 876.62,-3459.53"/>
<text text-anchor="start" x="812.38" y="-3446.98" font-family="Helvetica,sans-Serif" font-size="9.00">useGameId.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="edge145" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M526.31,-4400.35C537.14,-4396.84 547.2,-4391.2 554.25,-4382.28 566.99,-4366.15 548.3,-3657.38 562.25,-3642.28 587.46,-3614.99 703.22,-3654.27 728.75,-3627.28 741.28,-3614.04 722.07,-3476.73 734.38,-3463.28 749.36,-3446.9 773.6,-3443.08 795.03,-3443.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="794.88,-3445.71 800.98,-3443.93 795.1,-3441.51 794.88,-3445.71"/>
</g>
<!-- src/client/infrastructure/hooks/useResponsiveColumnCount.tsx -->
<g id="node115" class="node">
<title>src/client/infrastructure/hooks/useResponsiveColumnCount.tsx</title>
<g id="a_node115"><a xlink:href="src/client/infrastructure/hooks/useResponsiveColumnCount.tsx" xlink:title="useResponsiveColumnCount.tsx">
<polygon fill="#6cbaff" stroke="black" points="915.62,-3512.53 765.38,-3512.53 765.38,-3494.03 915.62,-3494.03 915.62,-3512.53"/>
<text text-anchor="start" x="773.38" y="-3499.98" font-family="Helvetica,sans-Serif" font-size="9.00">useResponsiveColumnCount.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useResponsiveColumnCount.tsx -->
<g id="edge147" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useResponsiveColumnCount.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M526.3,-4400.34C537.13,-4396.84 547.19,-4391.2 554.25,-4382.28 565.23,-4368.41 553.57,-3760.7 562.25,-3745.28 605.03,-3669.24 684.73,-3716.61 728.75,-3641.28 735.71,-3629.37 725.06,-3527.45 734.38,-3517.28 740.49,-3510.61 748.12,-3505.99 756.48,-3502.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="757.03,-3504.9 762.14,-3501.12 755.79,-3500.88 757.03,-3504.9"/>
</g>
<!-- src/client/infrastructure/lib/chunk.ts -->
<g id="node116" class="node">
<title>src/client/infrastructure/lib/chunk.ts</title>
<g id="a_node116"><a xlink:href="src/client/infrastructure/lib/chunk.ts" xlink:title="chunk.ts">
<polygon fill="#6cbaff" stroke="black" points="1074.62,-5266.53 1020.62,-5266.53 1020.62,-5248.03 1074.62,-5248.03 1074.62,-5266.53"/>
<text text-anchor="start" x="1030.75" y="-5253.98" font-family="Helvetica,sans-Serif" font-size="9.00">chunk.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/lib/chunk.ts -->
<g id="edge148" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/lib/chunk.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M509.42,-4413.92C526.32,-4419.99 544.05,-4429.75 554.25,-4445.28 563.49,-4459.36 550.98,-4735.77 562.25,-4748.28 612.19,-4803.76 668.29,-4726.5 728.75,-4770.28 902.93,-4896.42 861.18,-5005.65 938.62,-5206.28 944.47,-5221.42 936.83,-5230.55 949,-5241.28 965.83,-5256.12 990.92,-5259.98 1011.44,-5260.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1011.21,-5262.31 1017.19,-5260.13 1011.16,-5258.11 1011.21,-5262.31"/>
</g>
<!-- src/client/application/store/appStore.ts -->
<g id="node36" class="node">
<title>src/client/application/store/appStore.ts</title>
<g id="a_node36"><a xlink:href="src/client/application/store/appStore.ts" xlink:title="appStore.ts">
<polygon fill="#fb6969" stroke="black" points="1422.88,-2669.53 1360.38,-2669.53 1360.38,-2651.03 1422.88,-2651.03 1422.88,-2669.53"/>
<text text-anchor="start" x="1368.38" y="-2656.98" font-family="Helvetica,sans-Serif" font-size="9.00">appStore.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge153" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M478.68,-4535.87C499.11,-4552.2 538.61,-4587.63 554.25,-4628.28 569.33,-4667.49 550.8,-4965.86 562.25,-5006.28 603.43,-5151.59 610.56,-5212.8 734.38,-5299.28 882.22,-5402.54 989.45,-5440.16 1138.25,-5338.28 1312.12,-5219.24 1267.24,-5102.66 1309.75,-4896.28 1357.1,-4666.42 1387.12,-2877.87 1390.34,-2678.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1392.43,-2678.68 1390.43,-2672.65 1388.23,-2678.62 1392.43,-2678.68"/>
</g>
<!-- src/client/application/commands/clearDeckDraft/clearDeckDraft.ts -->
<g id="node39" class="node">
<title>src/client/application/commands/clearDeckDraft/clearDeckDraft.ts</title>
<g id="a_node39"><a xlink:href="src/client/application/commands/clearDeckDraft/clearDeckDraft.ts" xlink:title="clearDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1090.5,-1326.53 1004.75,-1326.53 1004.75,-1308.03 1090.5,-1308.03 1090.5,-1326.53"/>
<text text-anchor="start" x="1012.75" y="-1313.98" font-family="Helvetica,sans-Serif" font-size="9.00">clearDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/commands/clearDeckDraft/clearDeckDraft.ts -->
<g id="edge151" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/commands/clearDeckDraft/clearDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M517.2,-4524.83C531.36,-4521.76 545.28,-4515.71 554.25,-4504.28 568.72,-4485.85 554.36,-2839.34 562.25,-2817.28 601.77,-2706.76 662.84,-2709.41 728.75,-2612.28 835.99,-2454.24 889.34,-2421.8 938.62,-2237.28 944.93,-2213.69 935.36,-1378.54 949,-1358.28 959.82,-1342.21 978.27,-1332.43 996.23,-1326.47"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="996.39,-1328.62 1001.53,-1324.87 995.18,-1324.6 996.39,-1328.62"/>
</g>
<!-- src/client/application/queries/hasDeckDraft/hasDeckDraft.ts -->
<g id="node86" class="node">
<title>src/client/application/queries/hasDeckDraft/hasDeckDraft.ts</title>
<g id="a_node86"><a xlink:href="src/client/application/queries/hasDeckDraft/hasDeckDraft.ts" xlink:title="hasDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1264.25,-2154.53 1183.75,-2154.53 1183.75,-2136.03 1264.25,-2136.03 1264.25,-2154.53"/>
<text text-anchor="start" x="1191.75" y="-2141.98" font-family="Helvetica,sans-Serif" font-size="9.00">hasDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/queries/hasDeckDraft/hasDeckDraft.ts -->
<g id="edge152" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/queries/hasDeckDraft/hasDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M517.2,-4524.83C531.35,-4521.76 545.28,-4515.7 554.25,-4504.28 567.09,-4487.94 555.88,-3028.06 562.25,-3008.28 596.59,-2901.64 1062.16,-2260.31 1146.25,-2186.28 1159.26,-2174.83 1176.03,-2165.4 1190.57,-2158.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1191.05,-2160.58 1195.63,-2156.17 1189.3,-2156.76 1191.05,-2160.58"/>
</g>
<!-- src/client/infrastructure/builders/urlBuilder.ts -->
<g id="node100" class="node">
<title>src/client/infrastructure/builders/urlBuilder.ts</title>
<g id="a_node100"><a xlink:href="src/client/infrastructure/builders/urlBuilder.ts" xlink:title="urlBuilder.ts">
<polygon fill="#6cbaff" stroke="black" points="872.5,-5235.53 808.5,-5235.53 808.5,-5217.03 872.5,-5217.03 872.5,-5235.53"/>
<text text-anchor="start" x="816.5" y="-5222.98" font-family="Helvetica,sans-Serif" font-size="9.00">urlBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge154" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M478.52,-4535.93C498.72,-4552.36 537.89,-4587.91 554.25,-4628.28 566.04,-4657.37 545.17,-4742.95 562.25,-4769.28 607.57,-4839.16 675.6,-4791.15 728.75,-4855.28 820.19,-4965.6 836.14,-5149.44 838.92,-5207.86"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="836.81,-5207.77 839.15,-5213.68 841.01,-5207.6 836.81,-5207.77"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge161" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M517.19,-4524.82C531.34,-4521.75 545.26,-4515.7 554.25,-4504.28 573.06,-4480.38 541.45,-3431.47 562.25,-3409.28 593.2,-3376.26 727.65,-3380.89 797.3,-3385.73"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="797.12,-3387.82 803.26,-3386.16 797.43,-3383.63 797.12,-3387.82"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx -->
<g id="edge157" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M516.98,-4520.49C530,-4517.23 543.38,-4512.16 554.25,-4504.28 594.47,-4475.13 619.51,-4419.71 630.31,-4391.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="632.19,-4392.16 632.28,-4385.8 628.25,-4390.72 632.19,-4392.16"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge158" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M517.19,-4524.82C531.34,-4521.75 545.27,-4515.7 554.25,-4504.28 564.11,-4491.75 558,-3371.65 562.25,-3356.28 602.4,-3211.08 619.64,-3161.91 734.38,-3064.28 749.77,-3051.18 769.84,-3041.98 788.31,-3035.66"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="788.72,-3037.74 793.78,-3033.89 787.42,-3033.74 788.72,-3037.74"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="edge160" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M517.18,-4524.81C531.34,-4521.75 545.26,-4515.69 554.25,-4504.28 572.3,-4481.37 541.7,-3474.98 562.25,-3454.28 593.77,-3422.55 725.58,-3434.77 795.5,-3443.86"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="794.9,-3445.9 801.12,-3444.61 795.45,-3441.74 794.9,-3445.9"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx -->
<g id="node118" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx</title>
<g id="a_node118"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx" xlink:title="DeckCardRow.tsx">
<polygon fill="#6cbaff" stroke="black" points="681.88,-4535.53 593.12,-4535.53 593.12,-4517.03 681.88,-4517.03 681.88,-4535.53"/>
<text text-anchor="start" x="601.12" y="-4522.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckCardRow.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx -->
<g id="edge150" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M517.16,-4526.28C538.27,-4526.28 562.72,-4526.28 583.98,-4526.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="583.85,-4528.38 589.85,-4526.28 583.85,-4524.18 583.85,-4528.38"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx -->
<g id="node119" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx</title>
<g id="a_node119"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx" xlink:title="DeckCardDetailsDialog.tsx">
<polygon fill="#6cbaff" stroke="black" points="700.62,-4596.53 574.38,-4596.53 574.38,-4578.03 700.62,-4578.03 700.62,-4596.53"/>
<text text-anchor="start" x="582.38" y="-4583.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckCardDetailsDialog.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx -->
<g id="edge155" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M487.94,-4535.88C507.3,-4544.18 536.38,-4556.23 562.25,-4565.28 571.97,-4568.68 582.51,-4572.01 592.52,-4575.02"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="591.9,-4577.02 598.25,-4576.71 593.09,-4572.99 591.9,-4577.02"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx -->
<g id="node120" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx</title>
<g id="a_node120"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx" xlink:title="SaveDeckDialog.tsx">
<polygon fill="#6cbaff" stroke="black" points="686.75,-4657.53 588.25,-4657.53 588.25,-4639.03 686.75,-4639.03 686.75,-4657.53"/>
<text text-anchor="start" x="596.25" y="-4644.98" font-family="Helvetica,sans-Serif" font-size="9.00">SaveDeckDialog.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx -->
<g id="edge156" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M472.52,-4535.84C486.56,-4555.7 521.69,-4601.62 562.25,-4626.28 568.36,-4630 575.14,-4633.12 582.04,-4635.73"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="580.95,-4637.57 587.31,-4637.57 582.34,-4633.61 580.95,-4637.57"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckId/useDeckId.ts -->
<g id="node121" class="node">
<title>src/client/infrastructure/hooks/useDeckId/useDeckId.ts</title>
<g id="a_node121"><a xlink:href="src/client/infrastructure/hooks/useDeckId/useDeckId.ts" xlink:title="useDeckId.ts">
<polygon fill="#6cbaff" stroke="black" points="874.75,-3276.53 806.25,-3276.53 806.25,-3258.03 874.75,-3258.03 874.75,-3276.53"/>
<text text-anchor="start" x="814.25" y="-3263.98" font-family="Helvetica,sans-Serif" font-size="9.00">useDeckId.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckId/useDeckId.ts -->
<g id="edge159" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckId/useDeckId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M517.19,-4524.82C531.34,-4521.75 545.27,-4515.7 554.25,-4504.28 573.56,-4479.75 542.28,-3404.28 562.25,-3380.28 610.45,-3322.36 676.82,-3402.88 728.75,-3348.28 741.12,-3335.27 723.24,-3322.36 734.38,-3308.28 749.61,-3289.02 775.13,-3278.79 797.22,-3273.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="797.44,-3275.47 802.84,-3272.11 796.53,-3271.37 797.44,-3275.47"/>
</g>
<!-- src/client/infrastructure/providers/ToastProvider.tsx -->
<g id="node122" class="node">
<title>src/client/infrastructure/providers/ToastProvider.tsx</title>
<g id="a_node122"><a xlink:href="src/client/infrastructure/providers/ToastProvider.tsx" xlink:title="ToastProvider.tsx">
<polygon fill="#6cbaff" stroke="black" points="680,-3514.53 595,-3514.53 595,-3496.03 680,-3496.03 680,-3514.53"/>
<text text-anchor="start" x="603" y="-3501.98" font-family="Helvetica,sans-Serif" font-size="9.00">ToastProvider.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/providers/ToastProvider.tsx -->
<g id="edge162" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/providers/ToastProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M517.18,-4524.81C531.33,-4521.74 545.26,-4515.69 554.25,-4504.28 571.15,-4482.83 545.01,-3542.46 562.25,-3521.28 568.31,-3513.84 576.91,-3509.34 586.14,-3506.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="586.37,-3508.8 591.77,-3505.44 585.45,-3504.71 586.37,-3508.8"/>
</g>
<!-- src/client/infrastructure/store/store.ts -->
<g id="node123" class="node">
<title>src/client/infrastructure/store/store.ts</title>
<g id="a_node123"><a xlink:href="src/client/infrastructure/store/store.ts" xlink:title="store.ts">
<polygon fill="#6cbaff" stroke="black" points="867.5,-3638.53 813.5,-3638.53 813.5,-3620.03 867.5,-3620.03 867.5,-3638.53"/>
<text text-anchor="start" x="825.88" y="-3625.98" font-family="Helvetica,sans-Serif" font-size="9.00">store.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/store/store.ts -->
<g id="edge163" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/store/store.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M517.12,-4524.77C531.27,-4521.69 545.21,-4515.65 554.25,-4504.28 571.05,-4483.14 553.81,-4043.93 562.25,-4018.28 617.2,-3851.25 771.31,-3694.06 822.63,-3644.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="824.06,-3646.52 826.98,-3640.87 821.18,-3643.47 824.06,-3646.52"/>
</g>
<!-- src/client/domain/Catalog/Filter.ts -->
<g id="node68" class="node">
<title>src/client/domain/Catalog/Filter.ts</title>
<g id="a_node68"><a xlink:href="src/client/domain/Catalog/Filter.ts" xlink:title="Filter.ts">
<polygon fill="#fa9f36" stroke="black" points="1928.62,-490.53 1874.62,-490.53 1874.62,-472.03 1928.62,-472.03 1928.62,-490.53"/>
<text text-anchor="start" x="1887.38" y="-477.98" font-family="Helvetica,sans-Serif" font-size="9.00">Filter.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge168" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M484.65,-4718.92C505.98,-4731.11 539.82,-4754.31 554.25,-4785.28 561.9,-4801.7 549.66,-5424.26 562.25,-5437.28 892.42,-5778.93 1388.2,-5643.65 1609.62,-5223.28 1710.02,-5032.7 1691.33,-4965.84 1731.88,-4754.28 1813.39,-4328.93 1824.82,-4219.51 1852.12,-3787.28 1853.55,-3764.65 1851.49,-543.24 1860.12,-522.28 1864.21,-512.37 1871.98,-503.57 1879.62,-496.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1880.93,-498.33 1884.18,-492.87 1878.23,-495.11 1880.93,-498.33"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge170" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M519.96,-4707.18C533.14,-4703.94 545.86,-4697.96 554.25,-4687.28 565.31,-4673.22 555.72,-3415.94 562.25,-3399.28 602.26,-3297.21 687.19,-3318.73 728.75,-3217.28 735.2,-3201.54 724.52,-3078.15 734.38,-3064.28 746.58,-3047.1 767.1,-3037.12 786.78,-3031.32"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="787.25,-3033.37 792.51,-3029.79 786.17,-3029.31 787.25,-3033.37"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx -->
<g id="node124" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx</title>
<g id="a_node124"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx" xlink:title="FilterItem.tsx">
<polygon fill="#6cbaff" stroke="black" points="671,-4718.53 604,-4718.53 604,-4700.03 671,-4700.03 671,-4718.53"/>
<text text-anchor="start" x="612" y="-4705.98" font-family="Helvetica,sans-Serif" font-size="9.00">FilterItem.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx -->
<g id="edge169" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M519.95,-4709.28C544.13,-4709.28 572.21,-4709.28 594.75,-4709.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="594.7,-4711.38 600.7,-4709.28 594.7,-4707.18 594.7,-4711.38"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge176" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M509.47,-4291.88C526.39,-4297.95 544.11,-4307.71 554.25,-4323.28 567.14,-4343.07 558.25,-4725.01 562.25,-4748.28 603.25,-4986.96 559.72,-5097.51 734.38,-5265.28 805.98,-5334.06 850.69,-5313.37 949,-5327.28 990.64,-5333.17 1107.14,-5355.58 1138.25,-5327.28 1347.25,-5137.13 1386.87,-2904.73 1390.36,-2678.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1392.45,-2679.03 1390.44,-2673 1388.25,-2678.97 1392.45,-2679.03"/>
</g>
<!-- src/client/application/commands/loadDeckDraft/loadDeckDraft.ts -->
<g id="node50" class="node">
<title>src/client/application/commands/loadDeckDraft/loadDeckDraft.ts</title>
<g id="a_node50"><a xlink:href="src/client/application/commands/loadDeckDraft/loadDeckDraft.ts" xlink:title="loadDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1089.38,-1265.53 1005.88,-1265.53 1005.88,-1247.03 1089.38,-1247.03 1089.38,-1265.53"/>
<text text-anchor="start" x="1013.88" y="-1252.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/commands/loadDeckDraft/loadDeckDraft.ts -->
<g id="edge174" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/commands/loadDeckDraft/loadDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M477.17,-4272.6C497.21,-4254.39 538.67,-4212.75 554.25,-4167.28 566.79,-4130.7 553.07,-2812.84 562.25,-2775.28 649.32,-2419.16 850.1,-2397.04 938.62,-2041.28 943.62,-2021.22 937.44,-1314.41 949,-1297.28 960.01,-1280.96 978.9,-1271.12 997.14,-1265.19"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="997.38,-1267.31 1002.54,-1263.6 996.19,-1263.28 997.38,-1267.31"/>
</g>
<!-- src/client/application/queries/isCatalogLoading/isCatalogLoading.ts -->
<g id="node88" class="node">
<title>src/client/application/queries/isCatalogLoading/isCatalogLoading.ts</title>
<g id="a_node88"><a xlink:href="src/client/application/queries/isCatalogLoading/isCatalogLoading.ts" xlink:title="isCatalogLoading.ts">
<polygon fill="#fb6969" stroke="black" points="1272.88,-2398.53 1175.12,-2398.53 1175.12,-2380.03 1272.88,-2380.03 1272.88,-2398.53"/>
<text text-anchor="start" x="1183.12" y="-2385.98" font-family="Helvetica,sans-Serif" font-size="9.00">isCatalogLoading.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/queries/isCatalogLoading/isCatalogLoading.ts -->
<g id="edge175" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/queries/isCatalogLoading/isCatalogLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M477.16,-4272.6C497.21,-4254.39 538.66,-4212.75 554.25,-4167.28 566.29,-4132.17 544.86,-2864.07 562.25,-2831.28 698.63,-2574.08 1058.87,-2440.64 1181.46,-2401.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1181.89,-2403.7 1186.99,-2399.9 1180.63,-2399.7 1181.89,-2403.7"/>
</g>
<!-- src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="node34" class="node">
<title>src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<g id="a_node34"><a xlink:href="src/client/application/queries/getCatalogCardById/getCatalogCardById.ts" xlink:title="getCatalogCardById.ts">
<polygon fill="#fb6969" stroke="black" points="1278.12,-1544.53 1169.88,-1544.53 1169.88,-1526.03 1278.12,-1526.03 1278.12,-1544.53"/>
<text text-anchor="start" x="1177.88" y="-1531.98" font-family="Helvetica,sans-Serif" font-size="9.00">getCatalogCardById.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge178" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M509.99,-4658.02C526.77,-4664.09 544.25,-4673.82 554.25,-4689.28 564.24,-4704.72 549.17,-5338.36 562.25,-5351.28 605.34,-5393.85 1056.29,-5414 1138.25,-5327.28 1156.14,-5308.35 1132.83,-1598.61 1146.25,-1576.28 1153.79,-1563.74 1166.62,-1554.81 1179.65,-1548.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.32,-1550.57 1184.97,-1546.23 1178.63,-1546.72 1180.32,-1550.57"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge181" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M510,-4658.01C526.78,-4664.08 544.26,-4673.81 554.25,-4689.28 565.36,-4706.48 548.29,-5411.3 562.25,-5426.28 584.07,-5449.69 1111.22,-5444.4 1138.25,-5427.28 1270.94,-5343.24 1267.89,-5269.67 1309.75,-5118.28 1378.68,-4869.02 1389.58,-2889.94 1390.55,-2678.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1392.65,-2678.64 1390.57,-2672.63 1388.45,-2678.62 1392.65,-2678.64"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts -->
<g id="node51" class="node">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts</title>
<g id="a_node51"><a xlink:href="src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts" xlink:title="loadDeckIntoBuilder.ts">
<polygon fill="#fb6969" stroke="black" points="1101.38,-777.53 993.88,-777.53 993.88,-759.03 1101.38,-759.03 1101.38,-777.53"/>
<text text-anchor="start" x="1001.88" y="-764.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadDeckIntoBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts -->
<g id="edge177" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M482.5,-4638.75C503.72,-4625.43 539.57,-4599.03 554.25,-4565.28 563.86,-4543.2 556.89,-2854.76 562.25,-2831.28 648.87,-2452.13 851.28,-2420.27 938.62,-2041.28 946.31,-2007.93 929.94,-837.7 949,-809.28 957.86,-796.08 971.87,-787.12 986.55,-781.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="987.03,-783.1 991.91,-779.02 985.55,-779.17 987.03,-783.1"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/hasDeckDraft/hasDeckDraft.ts -->
<g id="edge179" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/hasDeckDraft/hasDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M509.99,-4658.01C526.78,-4664.08 544.26,-4673.81 554.25,-4689.28 565.07,-4706.04 548.07,-5393.26 562.25,-5407.28 607.76,-5452.28 1092.94,-5452.48 1138.25,-5407.28 1154.09,-5391.48 1134.72,-2205.45 1146.25,-2186.28 1153.79,-2173.74 1166.62,-2164.81 1179.65,-2158.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.32,-2160.57 1184.97,-2156.24 1178.63,-2156.72 1180.32,-2160.57"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/isCatalogLoading/isCatalogLoading.ts -->
<g id="edge180" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/isCatalogLoading/isCatalogLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M482.5,-4638.75C503.71,-4625.42 539.55,-4599.02 554.25,-4565.28 569.24,-4530.88 549.55,-3249.6 562.25,-3214.28 603.19,-3100.39 650.2,-3090.24 734.38,-3003.28 818.12,-2916.76 869.6,-2923.95 938.62,-2825.28 945.99,-2814.75 943.55,-2809.92 949,-2798.28 1022.19,-2642.02 1072.78,-2618.93 1138.25,-2459.28 1143.32,-2446.91 1137.9,-2440.72 1146.25,-2430.28 1156.11,-2417.95 1170.71,-2408.86 1184.53,-2402.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1185.11,-2404.45 1189.77,-2400.13 1183.43,-2400.6 1185.11,-2404.45"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/infrastructure/store/store.ts -->
<g id="edge182" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/infrastructure/store/store.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M482.44,-4638.72C503.58,-4625.37 539.35,-4598.94 554.25,-4565.28 574.81,-4518.84 545.72,-4156.3 562.25,-4108.28 602.28,-3992 667.05,-3993.66 728.75,-3887.28 778.31,-3801.82 818.81,-3689.97 833.54,-3647.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="835.51,-3647.79 835.45,-3641.44 831.53,-3646.44 835.51,-3647.79"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx -->
<g id="node8" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx</title>
<g id="a_node8"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M115.08,-6094.53C115.08,-6094.53 73.17,-6094.53 73.17,-6094.53 70.08,-6094.53 67,-6091.45 67,-6088.36 67,-6088.36 67,-6082.2 67,-6082.2 67,-6079.11 70.08,-6076.03 73.17,-6076.03 73.17,-6076.03 115.08,-6076.03 115.08,-6076.03 118.17,-6076.03 121.25,-6079.11 121.25,-6082.2 121.25,-6082.2 121.25,-6088.36 121.25,-6088.36 121.25,-6091.45 118.17,-6094.53 115.08,-6094.53"/>
<text text-anchor="start" x="75" y="-6081.98" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts -->
<g id="node9" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts</title>
<g id="a_node9"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts" xlink:title="DeckBuilderInitializer.ts">
<polygon fill="#6cbaff" stroke="black" points="520.62,-4352.53 409.38,-4352.53 409.38,-4334.03 520.62,-4334.03 520.62,-4352.53"/>
<text text-anchor="start" x="417.38" y="-4339.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderInitializer.ts</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts -->
<g id="edge6" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.56,-6087.52C138.82,-6087.39 160.3,-6083.88 172.25,-6069.28 184.61,-6054.18 174.64,-4682.97 180.25,-4664.28 222.96,-4522.01 248.09,-4478.72 362.75,-4384.28 378.36,-4371.42 398.54,-4362.16 416.77,-4355.73"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="417.1,-4357.84 422.12,-4353.94 415.76,-4353.85 417.1,-4357.84"/>
</g>
<!-- src/client/infrastructure/providers/ReduxProvider.tsx -->
<g id="node10" class="node">
<title>src/client/infrastructure/providers/ReduxProvider.tsx</title>
<g id="a_node10"><a xlink:href="src/client/infrastructure/providers/ReduxProvider.tsx" xlink:title="ReduxProvider.tsx">
<polygon fill="#6cbaff" stroke="black" points="682.62,-3545.53 592.38,-3545.53 592.38,-3527.03 682.62,-3527.03 682.62,-3545.53"/>
<text text-anchor="start" x="600.38" y="-3532.98" font-family="Helvetica,sans-Serif" font-size="9.00">ReduxProvider.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/infrastructure/providers/ReduxProvider.tsx -->
<g id="edge7" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/infrastructure/providers/ReduxProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.52,-6087.49C138.76,-6087.34 160.23,-6083.83 172.25,-6069.28 181.98,-6057.5 174.5,-5533.44 180.25,-5519.28 271.32,-5294.98 463.93,-5347.89 554.25,-5123.28 564.77,-5097.12 560.32,-4136.41 562.25,-4108.28 577.27,-3889.3 621,-3625.97 633.27,-3554.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="635.31,-3555.28 634.27,-3549.01 631.17,-3554.57 635.31,-3555.28"/>
</g>
<!-- src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts -->
<g id="node117" class="node">
<title>src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts</title>
<g id="a_node117"><a xlink:href="src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts" xlink:title="useInitializeDeckBuilderFromLocation.ts">
<polygon fill="#6cbaff" stroke="black" points="930.62,-3337.53 750.38,-3337.53 750.38,-3319.03 930.62,-3319.03 930.62,-3337.53"/>
<text text-anchor="start" x="758.38" y="-3324.98" font-family="Helvetica,sans-Serif" font-size="9.00">useInitializeDeckBuilderFromLocation.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts&#45;&gt;src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts -->
<g id="edge149" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts&#45;&gt;src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M520.86,-4340.94C533.72,-4337.65 546.04,-4331.7 554.25,-4321.28 570.33,-4300.88 545.79,-3406.37 562.25,-3386.28 604.51,-3334.71 679.78,-3321.5 741.23,-3320.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="741.18,-3322.58 747.17,-3320.43 741.15,-3318.38 741.18,-3322.58"/>
</g>
<!-- src/client/infrastructure/providers/ReduxProvider.tsx&#45;&gt;src/client/infrastructure/store/store.ts -->
<g id="edge240" class="edge">
<title>src/client/infrastructure/providers/ReduxProvider.tsx&#45;&gt;src/client/infrastructure/store/store.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M659.25,-3545.88C695.29,-3562.56 769.49,-3596.89 810.56,-3615.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="809.48,-3617.71 815.81,-3618.32 811.25,-3613.89 809.48,-3617.71"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx -->
<g id="node11" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx</title>
<g id="a_node11"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-6063.53C114.96,-6063.53 73.29,-6063.53 73.29,-6063.53 70.21,-6063.53 67.12,-6060.45 67.12,-6057.36 67.12,-6057.36 67.12,-6051.2 67.12,-6051.2 67.12,-6048.11 70.21,-6045.03 73.29,-6045.03 73.29,-6045.03 114.96,-6045.03 114.96,-6045.03 118.04,-6045.03 121.12,-6048.11 121.12,-6051.2 121.12,-6051.2 121.12,-6057.36 121.12,-6057.36 121.12,-6060.45 118.04,-6063.53 114.96,-6063.53"/>
<text text-anchor="start" x="76.88" y="-6050.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx -->
<g id="edge8" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.44,-6057.64C138.65,-6058.22 160.11,-6055.56 172.25,-6041.28 185.65,-6025.51 169.88,-4569.19 180.25,-4551.28 226.78,-4470.94 333.73,-4432.82 402.22,-4416.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="402.49,-4418.15 407.84,-4414.72 401.52,-4414.06 402.49,-4418.15"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx -->
<g id="edge9" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.44,-6057.64C138.65,-6058.22 160.11,-6055.55 172.25,-6041.28 184.57,-6026.8 170.3,-4688.48 180.25,-4672.28 229.11,-4592.71 336.63,-4554.63 404.38,-4537.9"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="404.56,-4540.01 409.91,-4536.57 403.58,-4535.93 404.56,-4540.01"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx -->
<g id="edge10" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.42,-6057.62C138.61,-6058.19 160.07,-6055.52 172.25,-6041.28 185.63,-6025.63 176.65,-5320.55 180.25,-5300.28 222.69,-5061.18 218.95,-4980.96 362.75,-4785.28 382.66,-4758.19 414.92,-4736.34 437.67,-4723.14"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="438.46,-4725.11 442.65,-4720.33 436.39,-4721.45 438.46,-4725.11"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx -->
<g id="edge11" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.44,-6057.64C138.65,-6058.22 160.12,-6055.56 172.25,-6041.28 187.46,-6023.38 165.66,-4368.69 180.25,-4350.28 232.49,-4284.36 336.34,-4275.35 402.87,-4277.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="402.5,-4279.24 408.57,-4277.35 402.65,-4275.04 402.5,-4279.24"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx -->
<g id="edge12" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.44,-6057.64C138.64,-6058.21 160.11,-6055.55 172.25,-6041.28 183.48,-6028.09 171.53,-4809.25 180.25,-4794.28 227.52,-4713.14 336.59,-4675.45 404.9,-4659.23"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="405.1,-4661.34 410.48,-4657.95 404.16,-4657.25 405.1,-4661.34"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/page.tsx -->
<g id="node12" class="node">
<title>app/[locale]/(connected)/games/[gameId]/page.tsx</title>
<g id="a_node12"><a xlink:href="app/[locale]/(connected)/games/[gameId]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-6029.53C114.96,-6029.53 73.29,-6029.53 73.29,-6029.53 70.21,-6029.53 67.12,-6026.45 67.12,-6023.36 67.12,-6023.36 67.12,-6017.2 67.12,-6017.2 67.12,-6014.11 70.21,-6011.03 73.29,-6011.03 73.29,-6011.03 114.96,-6011.03 114.96,-6011.03 118.04,-6011.03 121.12,-6014.11 121.12,-6017.2 121.12,-6017.2 121.12,-6023.36 121.12,-6023.36 121.12,-6026.45 118.04,-6029.53 114.96,-6029.53"/>
<text text-anchor="start" x="76.88" y="-6016.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx -->
<g id="node13" class="node">
<title>src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx</title>
<g id="a_node13"><a xlink:href="src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx" xlink:title="GameDetailsPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="335.75,-3676.53 231.25,-3676.53 231.25,-3658.03 335.75,-3658.03 335.75,-3676.53"/>
<text text-anchor="start" x="239.25" y="-3663.98" font-family="Helvetica,sans-Serif" font-size="9.00">GameDetailsPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx -->
<g id="edge13" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.49,-6023.27C138.71,-6023.61 160.18,-6020.67 172.25,-6006.28 179.87,-5997.19 179.27,-4310.1 180.25,-4298.28 200.51,-4053.78 262.03,-3760.99 278.44,-3685.66"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="280.49,-3686.12 279.72,-3679.81 276.39,-3685.22 280.49,-3686.12"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="node105" class="node">
<title>src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<g id="a_node105"><a xlink:href="src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx" xlink:title="ShiningCard.tsx">
<polygon fill="#6cbaff" stroke="black" points="881.12,-4168.53 799.88,-4168.53 799.88,-4150.03 881.12,-4150.03 881.12,-4168.53"/>
<text text-anchor="start" x="807.88" y="-4155.98" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningCard.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge232" class="edge">
<title>src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M336.2,-3659.45C343.43,-3656.18 350,-3651.62 354.75,-3645.28 369.21,-3625.98 345.54,-3228.18 362.75,-3211.28 423.48,-3151.66 493.41,-3151.76 554.25,-3211.28 561.48,-3218.35 560.55,-3565.31 562.25,-3575.28 602.3,-3810.76 678.39,-3852.79 728.75,-4086.28 731.15,-4097.43 726.71,-4128.84 734.38,-4137.28 748.46,-4152.79 770.43,-4158.93 790.66,-4160.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="790.34,-4163.01 796.47,-4161.34 790.64,-4158.82 790.34,-4163.01"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx -->
<g id="node108" class="node">
<title>src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx</title>
<g id="a_node108"><a xlink:href="src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx" xlink:title="PlayGameButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="514.25,-4875.53 415.75,-4875.53 415.75,-4857.03 514.25,-4857.03 514.25,-4875.53"/>
<text text-anchor="start" x="423.75" y="-4862.98" font-family="Helvetica,sans-Serif" font-size="9.00">PlayGameButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx -->
<g id="edge231" class="edge">
<title>src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M284.45,-3676.94C283.97,-3784.23 281.92,-4750.55 362.75,-4844.28 373.75,-4857.04 390.26,-4863.41 406.77,-4866.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="406.14,-4868.38 412.38,-4867.16 406.74,-4864.23 406.14,-4868.38"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/play/page.tsx -->
<g id="node14" class="node">
<title>app/[locale]/(connected)/games/[gameId]/play/page.tsx</title>
<g id="a_node14"><a xlink:href="app/[locale]/(connected)/games/[gameId]/play/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-5976.53C114.96,-5976.53 73.29,-5976.53 73.29,-5976.53 70.21,-5976.53 67.12,-5973.45 67.12,-5970.36 67.12,-5970.36 67.12,-5964.2 67.12,-5964.2 67.12,-5961.11 70.21,-5958.03 73.29,-5958.03 73.29,-5958.03 114.96,-5958.03 114.96,-5958.03 118.04,-5958.03 121.12,-5961.11 121.12,-5964.2 121.12,-5964.2 121.12,-5970.36 121.12,-5970.36 121.12,-5973.45 118.04,-5976.53 114.96,-5976.53"/>
<text text-anchor="start" x="76.88" y="-5963.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx -->
<g id="node15" class="node">
<title>src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx</title>
<g id="a_node15"><a xlink:href="src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx" xlink:title="PlayGamePage.tsx">
<polygon fill="#6cbaff" stroke="black" points="330.5,-3615.53 236.5,-3615.53 236.5,-3597.03 330.5,-3597.03 330.5,-3615.53"/>
<text text-anchor="start" x="244.5" y="-3602.98" font-family="Helvetica,sans-Serif" font-size="9.00">PlayGamePage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/play/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx -->
<g id="edge14" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/play/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.45,-5970.65C138.66,-5971.23 160.13,-5968.57 172.25,-5954.28 192.98,-5929.85 162.25,-3673.79 180.25,-3647.28 191.2,-3631.16 209.63,-3621.37 227.85,-3615.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="228.12,-3617.55 233.28,-3613.83 226.93,-3613.52 228.12,-3617.55"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx -->
<g id="node129" class="node">
<title>src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx</title>
<g id="a_node129"><a xlink:href="src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx" xlink:title="StartGameButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="514.62,-5032.53 415.38,-5032.53 415.38,-5014.03 514.62,-5014.03 514.62,-5032.53"/>
<text text-anchor="start" x="423.38" y="-5019.98" font-family="Helvetica,sans-Serif" font-size="9.00">StartGameButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx -->
<g id="edge234" class="edge">
<title>src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M315.01,-3615.96C329.77,-3622.36 346.13,-3632.37 354.75,-3647.28 364.16,-3663.56 350.9,-4986.67 362.75,-5001.28 373.36,-5014.37 389.77,-5020.82 406.3,-5023.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="405.7,-5025.78 411.93,-5024.55 406.29,-5021.63 405.7,-5025.78"/>
</g>
<!-- app/[locale]/(connected)/games/page.tsx -->
<g id="node16" class="node">
<title>app/[locale]/(connected)/games/page.tsx</title>
<g id="a_node16"><a xlink:href="app/[locale]/(connected)/games/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-5934.53C114.96,-5934.53 73.29,-5934.53 73.29,-5934.53 70.21,-5934.53 67.12,-5931.45 67.12,-5928.36 67.12,-5928.36 67.12,-5922.2 67.12,-5922.2 67.12,-5919.11 70.21,-5916.03 73.29,-5916.03 73.29,-5916.03 114.96,-5916.03 114.96,-5916.03 118.04,-5916.03 121.12,-5919.11 121.12,-5922.2 121.12,-5922.2 121.12,-5928.36 121.12,-5928.36 121.12,-5931.45 118.04,-5934.53 114.96,-5934.53"/>
<text text-anchor="start" x="76.88" y="-5921.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx -->
<g id="node17" class="node">
<title>src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx</title>
<g id="a_node17"><a xlink:href="src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx" xlink:title="GameListPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="328.62,-3554.53 238.38,-3554.53 238.38,-3536.03 328.62,-3536.03 328.62,-3554.53"/>
<text text-anchor="start" x="246.38" y="-3541.98" font-family="Helvetica,sans-Serif" font-size="9.00">GameListPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx -->
<g id="edge15" class="edge">
<title>app/[locale]/(connected)/games/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.29,-5916.36C139.15,-5908.74 161.45,-5895.87 172.25,-5876.28 187.61,-5848.43 162.38,-3612.59 180.25,-3586.28 191.52,-3569.68 210.73,-3559.8 229.48,-3553.92"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="229.92,-3555.98 235.12,-3552.32 228.77,-3551.94 229.92,-3555.98"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx -->
<g id="node107" class="node">
<title>src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx</title>
<g id="a_node107"><a xlink:href="src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx" xlink:title="GameDetailsButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="519.5,-4814.53 410.5,-4814.53 410.5,-4796.03 519.5,-4796.03 519.5,-4814.53"/>
<text text-anchor="start" x="418.5" y="-4801.98" font-family="Helvetica,sans-Serif" font-size="9.00">GameDetailsButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx&#45;&gt;src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx -->
<g id="edge233" class="edge">
<title>src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx&#45;&gt;src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M315,-3554.97C329.76,-3561.36 346.12,-3571.37 354.75,-3586.28 370.92,-3614.22 346.36,-4720.47 362.75,-4748.28 375.32,-4769.61 399.1,-4783.59 420.46,-4792.36"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="419.63,-4794.29 425.98,-4794.49 421.14,-4790.37 419.63,-4794.29"/>
</g>
<!-- app/[locale]/(connected)/matches/[matchId]/page.tsx -->
<g id="node18" class="node">
<title>app/[locale]/(connected)/matches/[matchId]/page.tsx</title>
<g id="a_node18"><a xlink:href="app/[locale]/(connected)/matches/[matchId]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-5846.53C114.96,-5846.53 73.29,-5846.53 73.29,-5846.53 70.21,-5846.53 67.12,-5843.45 67.12,-5840.36 67.12,-5840.36 67.12,-5834.2 67.12,-5834.2 67.12,-5831.11 70.21,-5828.03 73.29,-5828.03 73.29,-5828.03 114.96,-5828.03 114.96,-5828.03 118.04,-5828.03 121.12,-5831.11 121.12,-5834.2 121.12,-5834.2 121.12,-5840.36 121.12,-5840.36 121.12,-5843.45 118.04,-5846.53 114.96,-5846.53"/>
<text text-anchor="start" x="76.88" y="-5833.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx -->
<g id="node19" class="node">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx</title>
<g id="a_node19"><a xlink:href="src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx" xlink:title="MatchPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="321.5,-3301.53 245.5,-3301.53 245.5,-3283.03 321.5,-3283.03 321.5,-3301.53"/>
<text text-anchor="start" x="253.5" y="-3288.98" font-family="Helvetica,sans-Serif" font-size="9.00">MatchPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/matches/[matchId]/page.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx -->
<g id="edge16" class="edge">
<title>app/[locale]/(connected)/matches/[matchId]/page.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-5831.86C139.15,-5826.71 161.18,-5817.01 172.25,-5799.28 190.13,-5770.64 165.04,-3398.42 180.25,-3368.28 195.02,-3339.01 226.98,-3318.15 251.25,-3305.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="251.98,-3307.77 256.44,-3303.24 250.13,-3304 251.98,-3307.77"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx -->
<g id="node127" class="node">
<title>src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx</title>
<g id="a_node127"><a xlink:href="src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx" xlink:title="LeaveMatchButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="518,-4971.53 412,-4971.53 412,-4953.03 518,-4953.03 518,-4971.53"/>
<text text-anchor="start" x="420" y="-4958.98" font-family="Helvetica,sans-Serif" font-size="9.00">LeaveMatchButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx -->
<g id="edge235" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.27,-3301.95C315.27,-3314.82 343.35,-3339.36 354.75,-3368.28 362.58,-3388.14 351.93,-4886.88 362.75,-4905.28 375.3,-4926.62 399.08,-4940.6 420.44,-4949.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="419.61,-4951.3 425.97,-4951.5 421.13,-4947.38 419.61,-4951.3"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/Match/Match.tsx -->
<g id="node128" class="node">
<title>src/client/infrastructure/components/app/Gaming/Match/Match.tsx</title>
<g id="a_node128"><a xlink:href="src/client/infrastructure/components/app/Gaming/Match/Match.tsx" xlink:title="Match.tsx">
<polygon fill="#6cbaff" stroke="black" points="492.12,-5093.53 437.88,-5093.53 437.88,-5075.03 492.12,-5075.03 492.12,-5093.53"/>
<text text-anchor="start" x="445.88" y="-5080.98" font-family="Helvetica,sans-Serif" font-size="9.00">Match.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/Match/Match.tsx -->
<g id="edge236" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/Match/Match.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.27,-3301.95C315.28,-3314.82 343.35,-3339.36 354.75,-3368.28 363.38,-3390.17 347.94,-5044 362.75,-5062.28 378.27,-5081.45 406.21,-5086.39 428.81,-5086.85"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="428.79,-5088.95 434.78,-5086.83 428.77,-5084.75 428.79,-5088.95"/>
</g>
<!-- src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx -->
<g id="node131" class="node">
<title>src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx</title>
<g id="a_node131"><a xlink:href="src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx" xlink:title="MatchConsoleEvents.tsx">
<polygon fill="#6cbaff" stroke="black" points="523.25,-3964.53 406.75,-3964.53 406.75,-3946.03 523.25,-3946.03 523.25,-3964.53"/>
<text text-anchor="start" x="414.75" y="-3951.98" font-family="Helvetica,sans-Serif" font-size="9.00">MatchConsoleEvents.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx -->
<g id="edge237" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.23,-3301.96C315.19,-3314.86 343.22,-3339.42 354.75,-3368.28 360.57,-3382.86 352.81,-3921.13 362.75,-3933.28 371.57,-3944.06 384.31,-3950.34 397.81,-3953.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="397.01,-3955.81 403.32,-3955.04 397.9,-3951.71 397.01,-3955.81"/>
</g>
<!-- src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx -->
<g id="node142" class="node">
<title>src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx</title>
<g id="a_node142"><a xlink:href="src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx" xlink:title="ErrorLoadingMatchPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="530,-3240.53 400,-3240.53 400,-3222.03 530,-3222.03 530,-3240.53"/>
<text text-anchor="start" x="408" y="-3227.98" font-family="Helvetica,sans-Serif" font-size="9.00">ErrorLoadingMatchPage.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx -->
<g id="edge238" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M312.94,-3282.62C343.84,-3272.12 393.29,-3255.31 427.15,-3243.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="427.52,-3245.9 432.53,-3241.98 426.17,-3241.92 427.52,-3245.9"/>
</g>
<!-- src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx -->
<g id="node143" class="node">
<title>src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx</title>
<g id="a_node143"><a xlink:href="src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx" xlink:title="FinishedMatchPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="520.62,-3301.53 409.38,-3301.53 409.38,-3283.03 520.62,-3283.03 520.62,-3301.53"/>
<text text-anchor="start" x="417.38" y="-3288.98" font-family="Helvetica,sans-Serif" font-size="9.00">FinishedMatchPage.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx -->
<g id="edge239" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M321.69,-3292.28C344.39,-3292.28 374.01,-3292.28 400.34,-3292.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="400.11,-3294.38 406.11,-3292.28 400.11,-3290.18 400.11,-3294.38"/>
</g>
<!-- app/[locale]/(not&#45;connected)/layout.tsx -->
<g id="node20" class="node">
<title>app/[locale]/(not&#45;connected)/layout.tsx</title>
<g id="a_node20"><a xlink:href="app/[locale]/(not-connected)/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M115.08,-5647.53C115.08,-5647.53 73.17,-5647.53 73.17,-5647.53 70.08,-5647.53 67,-5644.45 67,-5641.36 67,-5641.36 67,-5635.2 67,-5635.2 67,-5632.11 70.08,-5629.03 73.17,-5629.03 73.17,-5629.03 115.08,-5629.03 115.08,-5629.03 118.17,-5629.03 121.25,-5632.11 121.25,-5635.2 121.25,-5635.2 121.25,-5641.36 121.25,-5641.36 121.25,-5644.45 118.17,-5647.53 115.08,-5647.53"/>
<text text-anchor="start" x="75" y="-5634.98" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx -->
<g id="node21" class="node">
<title>src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx</title>
<g id="a_node21"><a xlink:href="src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx" xlink:title="FullPageLayout.tsx">
<polygon fill="#6cbaff" stroke="black" points="512,-3134.53 418,-3134.53 418,-3116.03 512,-3116.03 512,-3134.53"/>
<text text-anchor="start" x="426" y="-3121.98" font-family="Helvetica,sans-Serif" font-size="9.00">FullPageLayout.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(not&#45;connected)/layout.tsx&#45;&gt;src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx -->
<g id="edge17" class="edge">
<title>app/[locale]/(not&#45;connected)/layout.tsx&#45;&gt;src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.45,-5641.65C138.66,-5642.23 160.13,-5639.57 172.25,-5625.28 193.39,-5600.36 163.22,-3300.17 180.25,-3272.28 230.15,-3190.54 341.77,-3152.2 409.18,-3135.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="409.27,-3138.03 414.64,-3134.61 408.31,-3133.94 409.27,-3138.03"/>
</g>
<!-- app/[locale]/(not&#45;connected)/signin/page.tsx -->
<g id="node22" class="node">
<title>app/[locale]/(not&#45;connected)/signin/page.tsx</title>
<g id="a_node22"><a xlink:href="app/[locale]/(not-connected)/signin/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-5681.53C114.96,-5681.53 73.29,-5681.53 73.29,-5681.53 70.21,-5681.53 67.12,-5678.45 67.12,-5675.36 67.12,-5675.36 67.12,-5669.2 67.12,-5669.2 67.12,-5666.11 70.21,-5663.03 73.29,-5663.03 73.29,-5663.03 114.96,-5663.03 114.96,-5663.03 118.04,-5663.03 121.12,-5666.11 121.12,-5669.2 121.12,-5669.2 121.12,-5675.36 121.12,-5675.36 121.12,-5678.45 118.04,-5681.53 114.96,-5681.53"/>
<text text-anchor="start" x="76.88" y="-5668.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx -->
<g id="node23" class="node">
<title>src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx</title>
<g id="a_node23"><a xlink:href="src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx" xlink:title="SignInPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="322.25,-3397.53 244.75,-3397.53 244.75,-3379.03 322.25,-3379.03 322.25,-3397.53"/>
<text text-anchor="start" x="252.75" y="-3384.98" font-family="Helvetica,sans-Serif" font-size="9.00">SignInPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(not&#45;connected)/signin/page.tsx&#45;&gt;src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx -->
<g id="edge18" class="edge">
<title>app/[locale]/(not&#45;connected)/signin/page.tsx&#45;&gt;src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.45,-5675.65C138.66,-5676.23 160.13,-5673.57 172.25,-5659.28 192.29,-5635.67 162.85,-3454.9 180.25,-3429.28 192.75,-3410.87 215.02,-3400.72 235.59,-3395.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="236,-3397.2 241.33,-3393.73 235,-3393.12 236,-3397.2"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx -->
<g id="node104" class="node">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx</title>
<g id="a_node104"><a xlink:href="src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx" xlink:title="SignInForm.tsx">
<polygon fill="#6cbaff" stroke="black" points="675.88,-4168.53 599.12,-4168.53 599.12,-4150.03 675.88,-4150.03 675.88,-4168.53"/>
<text text-anchor="start" x="607.12" y="-4155.98" font-family="Helvetica,sans-Serif" font-size="9.00">SignInForm.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx -->
<g id="edge229" class="edge">
<title>src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M315.01,-3397.96C329.78,-3404.35 346.14,-3414.36 354.75,-3429.28 366.51,-3449.66 346.07,-5106.68 362.75,-5123.28 423.07,-5183.32 493.78,-5183.17 554.25,-5123.28 562.45,-5115.16 559.29,-4303.44 562.25,-4292.28 574.37,-4246.61 605.74,-4199.91 623.58,-4175.86"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="625.09,-4177.34 627.04,-4171.29 621.74,-4174.81 625.09,-4177.34"/>
</g>
<!-- src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx -->
<g id="node137" class="node">
<title>src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx</title>
<g id="a_node137"><a xlink:href="src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx" xlink:title="Sparkles.tsx">
<polygon fill="#6cbaff" stroke="black" points="873.25,-4290.53 807.75,-4290.53 807.75,-4272.03 873.25,-4272.03 873.25,-4290.53"/>
<text text-anchor="start" x="815.75" y="-4277.98" font-family="Helvetica,sans-Serif" font-size="9.00">Sparkles.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx -->
<g id="edge230" class="edge">
<title>src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M307.75,-3398.03C322.53,-3405.08 341.33,-3415.74 354.75,-3429.28 480.52,-3556.18 468.15,-3621.73 554.25,-3778.28 634.21,-3923.65 685.81,-3948.03 728.75,-4108.28 733.1,-4124.5 723.55,-4246.45 734.38,-4259.28 749.87,-4277.65 776.41,-4283.1 798.95,-4283.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="798.62,-4286.08 804.65,-4284.07 798.68,-4281.88 798.62,-4286.08"/>
</g>
<!-- app/[locale]/access&#45;denied/page.tsx -->
<g id="node24" class="node">
<title>app/[locale]/access&#45;denied/page.tsx</title>
<g id="a_node24"><a xlink:href="app/[locale]/access-denied/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-5769.53C114.96,-5769.53 73.29,-5769.53 73.29,-5769.53 70.21,-5769.53 67.12,-5766.45 67.12,-5763.36 67.12,-5763.36 67.12,-5757.2 67.12,-5757.2 67.12,-5754.11 70.21,-5751.03 73.29,-5751.03 73.29,-5751.03 114.96,-5751.03 114.96,-5751.03 118.04,-5751.03 121.12,-5754.11 121.12,-5757.2 121.12,-5757.2 121.12,-5763.36 121.12,-5763.36 121.12,-5766.45 118.04,-5769.53 114.96,-5769.53"/>
<text text-anchor="start" x="76.88" y="-5756.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx -->
<g id="node25" class="node">
<title>src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx</title>
<g id="a_node25"><a xlink:href="src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx" xlink:title="AccessDeniedPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="338.75,-3458.53 228.25,-3458.53 228.25,-3440.03 338.75,-3440.03 338.75,-3458.53"/>
<text text-anchor="start" x="236.25" y="-3445.98" font-family="Helvetica,sans-Serif" font-size="9.00">AccessDeniedPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/access&#45;denied/page.tsx&#45;&gt;src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx -->
<g id="edge19" class="edge">
<title>app/[locale]/access&#45;denied/page.tsx&#45;&gt;src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.29,-5751.36C139.14,-5743.74 161.44,-5730.86 172.25,-5711.28 186.92,-5684.7 166.56,-3552.38 180.25,-3525.28 195.03,-3496.01 226.98,-3475.16 251.26,-3462.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="251.98,-3464.77 256.44,-3460.25 250.13,-3461 251.98,-3464.77"/>
</g>
<!-- app/[locale]/page.tsx -->
<g id="node26" class="node">
<title>app/[locale]/page.tsx</title>
<g id="a_node26"><a xlink:href="app/[locale]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-5613.53C114.96,-5613.53 73.29,-5613.53 73.29,-5613.53 70.21,-5613.53 67.12,-5610.45 67.12,-5607.36 67.12,-5607.36 67.12,-5601.2 67.12,-5601.2 67.12,-5598.11 70.21,-5595.03 73.29,-5595.03 73.29,-5595.03 114.96,-5595.03 114.96,-5595.03 118.04,-5595.03 121.12,-5598.11 121.12,-5601.2 121.12,-5601.2 121.12,-5607.36 121.12,-5607.36 121.12,-5610.45 118.04,-5613.53 114.96,-5613.53"/>
<text text-anchor="start" x="76.88" y="-5600.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx -->
<g id="node27" class="node">
<title>src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx</title>
<g id="a_node27"><a xlink:href="src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx" xlink:title="RedirectToGameList.tsx">
<polygon fill="#6cbaff" stroke="black" points="521.38,-3807.53 408.62,-3807.53 408.62,-3789.03 521.38,-3789.03 521.38,-3807.53"/>
<text text-anchor="start" x="416.62" y="-3794.98" font-family="Helvetica,sans-Serif" font-size="9.00">RedirectToGameList.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/page.tsx&#45;&gt;src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx -->
<g id="edge20" class="edge">
<title>app/[locale]/page.tsx&#45;&gt;src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.45,-5607.65C138.66,-5608.23 160.13,-5605.57 172.25,-5591.28 193.09,-5566.72 157.44,-3295.02 180.25,-3272.28 235.18,-3217.54 296.34,-3221.26 354.75,-3272.28 364.71,-3280.99 360.46,-3318.25 362.75,-3331.28 393.72,-3507.33 444.29,-3717.87 459.52,-3780.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="457.46,-3780.53 460.93,-3785.86 461.54,-3779.53 457.46,-3780.53"/>
</g>
<!-- app/globals.css -->
<g id="node28" class="node">
<title>app/globals.css</title>
<g id="a_node28"><a xlink:href="app/globals.css" xlink:title="globals.css">
<path fill="#ffffcc" stroke="black" d="M308.21,-5548.53C308.21,-5548.53 258.79,-5548.53 258.79,-5548.53 255.71,-5548.53 252.62,-5545.45 252.62,-5542.36 252.62,-5542.36 252.62,-5536.2 252.62,-5536.2 252.62,-5533.11 255.71,-5530.03 258.79,-5530.03 258.79,-5530.03 308.21,-5530.03 308.21,-5530.03 311.29,-5530.03 314.38,-5533.11 314.38,-5536.2 314.38,-5536.2 314.38,-5542.36 314.38,-5542.36 314.38,-5545.45 311.29,-5548.53 308.21,-5548.53"/>
<text text-anchor="start" x="260.62" y="-5535.98" font-family="Helvetica,sans-Serif" font-size="9.00">globals.css</text>
</a>
</g>
</g>
<!-- app/layout.tsx -->
<g id="node29" class="node">
<title>app/layout.tsx</title>
<g id="a_node29"><a xlink:href="app/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M115.08,-5548.53C115.08,-5548.53 73.17,-5548.53 73.17,-5548.53 70.08,-5548.53 67,-5545.45 67,-5542.36 67,-5542.36 67,-5536.2 67,-5536.2 67,-5533.11 70.08,-5530.03 73.17,-5530.03 73.17,-5530.03 115.08,-5530.03 115.08,-5530.03 118.17,-5530.03 121.25,-5533.11 121.25,-5536.2 121.25,-5536.2 121.25,-5542.36 121.25,-5542.36 121.25,-5545.45 118.17,-5548.53 115.08,-5548.53"/>
<text text-anchor="start" x="75" y="-5535.98" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;app/globals.css -->
<g id="edge21" class="edge">
<title>app/layout.tsx&#45;&gt;app/globals.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.57,-5539.28C153.29,-5539.28 206.72,-5539.28 243.25,-5539.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="243.12,-5541.38 249.12,-5539.28 243.12,-5537.18 243.12,-5541.38"/>
</g>
<!-- src/client/infrastructure/components/ui/Background/Background.tsx -->
<g id="node30" class="node">
<title>src/client/infrastructure/components/ui/Background/Background.tsx</title>
<g id="a_node30"><a xlink:href="src/client/infrastructure/components/ui/Background/Background.tsx" xlink:title="Background.tsx">
<polygon fill="#6cbaff" stroke="black" points="880,-4046.53 801,-4046.53 801,-4028.03 880,-4028.03 880,-4046.53"/>
<text text-anchor="start" x="809" y="-4033.98" font-family="Helvetica,sans-Serif" font-size="9.00">Background.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;src/client/infrastructure/components/ui/Background/Background.tsx -->
<g id="edge22" class="edge">
<title>app/layout.tsx&#45;&gt;src/client/infrastructure/components/ui/Background/Background.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M95.08,-5529.65C94.35,-5365.05 86.28,-3145.64 180.25,-3051.28 297.56,-2933.48 415.57,-2952.59 554.25,-3044.28 719.45,-3153.51 686.63,-3260.77 728.75,-3454.28 738.87,-3500.76 727.18,-3621.26 734.38,-3668.28 755.58,-3806.8 812.92,-3966.84 832.69,-4019.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="830.67,-4020.09 834.76,-4024.96 834.6,-4018.6 830.67,-4020.09"/>
</g>
<!-- src/client/infrastructure/layouts/RootLayout/RootLayout.tsx -->
<g id="node31" class="node">
<title>src/client/infrastructure/layouts/RootLayout/RootLayout.tsx</title>
<g id="a_node31"><a xlink:href="src/client/infrastructure/layouts/RootLayout/RootLayout.tsx" xlink:title="RootLayout.tsx">
<polygon fill="#6cbaff" stroke="black" points="503.38,-3073.53 426.62,-3073.53 426.62,-3055.03 503.38,-3055.03 503.38,-3073.53"/>
<text text-anchor="start" x="434.62" y="-3060.98" font-family="Helvetica,sans-Serif" font-size="9.00">RootLayout.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;src/client/infrastructure/layouts/RootLayout/RootLayout.tsx -->
<g id="edge23" class="edge">
<title>app/layout.tsx&#45;&gt;src/client/infrastructure/layouts/RootLayout/RootLayout.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M95.12,-5529.93C95.06,-5370.23 95.88,-3216.65 180.25,-3118.28 238.56,-3050.3 353.62,-3050.89 417.86,-3057.46"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="417.37,-3059.52 423.57,-3058.09 417.83,-3055.35 417.37,-3059.52"/>
</g>
<!-- src/client/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/infrastructure/providers/ToastProvider.tsx -->
<g id="edge227" class="edge">
<title>src/client/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/infrastructure/providers/ToastProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M503.51,-3071.89C522.07,-3077.74 542.89,-3087.88 554.25,-3105.28 565.91,-3123.15 548.67,-3472.82 562.25,-3489.28 568.35,-3496.68 576.98,-3501.17 586.21,-3503.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="585.53,-3505.8 591.84,-3505.06 586.45,-3501.7 585.53,-3505.8"/>
</g>
<!-- src/client/infrastructure/providers/ConvexClientProvider.tsx -->
<g id="node140" class="node">
<title>src/client/infrastructure/providers/ConvexClientProvider.tsx</title>
<g id="a_node140"><a xlink:href="src/client/infrastructure/providers/ConvexClientProvider.tsx" xlink:title="ConvexClientProvider.tsx">
<polygon fill="#6cbaff" stroke="black" points="696.88,-3483.53 578.12,-3483.53 578.12,-3465.03 696.88,-3465.03 696.88,-3483.53"/>
<text text-anchor="start" x="586.12" y="-3470.98" font-family="Helvetica,sans-Serif" font-size="9.00">ConvexClientProvider.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/infrastructure/providers/ConvexClientProvider.tsx -->
<g id="edge226" class="edge">
<title>src/client/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/infrastructure/providers/ConvexClientProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M503.5,-3073.33C521.36,-3079.49 541.54,-3089.49 554.25,-3105.28 599.06,-3160.97 627.16,-3389.03 634.55,-3455.82"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="632.46,-3456.04 635.19,-3461.78 636.63,-3455.59 632.46,-3456.04"/>
</g>
<!-- app/page.tsx -->
<g id="node32" class="node">
<title>app/page.tsx</title>
<g id="a_node32"><a xlink:href="app/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-5579.53C114.96,-5579.53 73.29,-5579.53 73.29,-5579.53 70.21,-5579.53 67.12,-5576.45 67.12,-5573.36 67.12,-5573.36 67.12,-5567.2 67.12,-5567.2 67.12,-5564.11 70.21,-5561.03 73.29,-5561.03 73.29,-5561.03 114.96,-5561.03 114.96,-5561.03 118.04,-5561.03 121.12,-5564.11 121.12,-5567.2 121.12,-5567.2 121.12,-5573.36 121.12,-5573.36 121.12,-5576.45 118.04,-5579.53 114.96,-5579.53"/>
<text text-anchor="start" x="76.88" y="-5566.98" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/page.tsx&#45;&gt;src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx -->
<g id="edge24" class="edge">
<title>app/page.tsx&#45;&gt;src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.57,-5572.53C138.83,-5572.41 160.31,-5568.9 172.25,-5554.28 182.71,-5541.47 168.54,-3184.95 180.25,-3173.28 235.18,-3118.53 298.54,-3119.85 354.75,-3173.28 367.49,-3185.39 360.44,-3313.86 362.75,-3331.28 386.29,-3508.48 441.93,-3718.23 458.96,-3780.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="456.92,-3780.71 460.54,-3785.93 460.97,-3779.59 456.92,-3780.71"/>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts -->
<g id="node33" class="node">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts</title>
<g id="a_node33"><a xlink:href="src/client/application/commands/addCardToDeck/addCardToDeck.ts" xlink:title="addCardToDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1093.5,-960.53 1001.75,-960.53 1001.75,-942.03 1093.5,-942.03 1093.5,-960.53"/>
<text text-anchor="start" x="1009.75" y="-947.98" font-family="Helvetica,sans-Serif" font-size="9.00">addCardToDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge25" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.25,-960.87C1110.29,-966.91 1128.05,-976.67 1138.25,-992.28 1154.08,-1016.52 1128.65,-1490.3 1146.25,-1513.28 1150.4,-1518.7 1155.79,-1522.84 1161.79,-1526.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1160.57,-1527.77 1166.9,-1528.29 1162.28,-1523.93 1160.57,-1527.77"/>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts -->
<g id="node35" class="node">
<title>src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts</title>
<g id="a_node35"><a xlink:href="src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts" xlink:title="addCardToDeckRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1287.12,-960.53 1160.88,-960.53 1160.88,-942.03 1287.12,-942.03 1287.12,-960.53"/>
<text text-anchor="start" x="1168.88" y="-947.98" font-family="Helvetica,sans-Serif" font-size="9.00">addCardToDeckRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts -->
<g id="edge26" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.98,-951.28C1111.57,-951.28 1132.14,-951.28 1151.62,-951.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1151.45,-953.38 1157.45,-951.28 1151.45,-949.18 1151.45,-953.38"/>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge27" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.81,-960.98C1110.71,-967.03 1128.23,-976.76 1138.25,-992.28 1151.15,-1012.26 1129.4,-2687.51 1146.25,-2704.28 1172,-2729.91 1274.54,-2713.24 1309.75,-2704.28 1332.11,-2698.59 1354.98,-2685.32 1370.66,-2674.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1371.51,-2676.8 1375.26,-2671.67 1369.12,-2673.35 1371.51,-2676.8"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="node37" class="node">
<title>src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<g id="a_node37"><a xlink:href="src/client/domain/DeckBuilder/DeckBuilder.ts" xlink:title="DeckBuilder.ts">
<polygon fill="#fa9f36" stroke="black" points="1428.88,-220.53 1354.38,-220.53 1354.38,-202.03 1428.88,-202.03 1428.88,-220.53"/>
<text text-anchor="start" x="1362.38" y="-207.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge28" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.93,-951.12C1110.43,-948.58 1127.62,-942.53 1138.25,-929.28 1150.94,-913.46 1141.3,-583.95 1146.25,-564.28 1187.23,-401.3 1184.79,-330.07 1317.75,-227.28 1325.76,-221.09 1335.66,-217.19 1345.56,-214.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1345.79,-216.85 1351.25,-213.59 1344.95,-212.74 1345.79,-216.85"/>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="node38" class="node">
<title>src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<g id="a_node38"><a xlink:href="src/client/domain/DeckBuilder/deckBuilderEvents.ts" xlink:title="deckBuilderEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1725.5,-220.53 1624,-220.53 1624,-202.03 1725.5,-202.03 1725.5,-220.53"/>
<text text-anchor="start" x="1632" y="-207.98" font-family="Helvetica,sans-Serif" font-size="9.00">deckBuilderEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge29" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.55,-951.25C1110.2,-948.77 1127.6,-942.72 1138.25,-929.28 1151.95,-911.99 1130.76,-150.98 1146.25,-135.28 1182.44,-98.61 1563.04,-131.26 1609.62,-153.28 1630.2,-163.01 1648.89,-181.55 1660.71,-195.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1658.81,-196.06 1664.29,-199.28 1662.01,-193.34 1658.81,-196.06"/>
</g>
<!-- src/client/application/queries/getCatalogCardById/getCatalogCardById.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge83" class="edge">
<title>src/client/application/queries/getCatalogCardById/getCatalogCardById.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1265.94,-1544.95C1282.46,-1551.07 1299.91,-1560.84 1309.75,-1576.28 1317.92,-1589.1 1316.62,-2108.13 1317.75,-2123.28 1333.09,-2328.06 1375.25,-2574.01 1387.34,-2642.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1385.25,-2642.27 1388.37,-2647.81 1389.38,-2641.53 1385.25,-2642.27"/>
</g>
<!-- src/client/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="node87" class="node">
<title>src/client/application/services/DeckDraftService/DeckDraftService.ts</title>
<g id="a_node87"><a xlink:href="src/client/application/services/DeckDraftService/DeckDraftService.ts" xlink:title="DeckDraftService.ts">
<polygon fill="#fb6969" stroke="black" points="1585.62,-2747.53 1489.38,-2747.53 1489.38,-2729.03 1585.62,-2729.03 1585.62,-2747.53"/>
<text text-anchor="start" x="1497.38" y="-2734.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckDraftService.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="edge99" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1406.86,-2669.88C1422.16,-2680.01 1447.36,-2696.15 1470.12,-2708.28 1481.79,-2714.49 1494.99,-2720.63 1506.53,-2725.72"/>
<polygon fill="none" stroke="#000000" stroke-opacity="0.200000" points="1505.38,-2727.51 1511.72,-2727.99 1507.06,-2723.66 1505.38,-2727.51"/>
</g>
<!-- src/client/application/services/LocationService.ts -->
<g id="node91" class="node">
<title>src/client/application/services/LocationService.ts</title>
<g id="a_node91"><a xlink:href="src/client/application/services/LocationService.ts" xlink:title="LocationService.ts">
<polygon fill="#fb6969" stroke="black" points="1583,-2800.53 1492,-2800.53 1492,-2782.03 1583,-2782.03 1583,-2800.53"/>
<text text-anchor="start" x="1500" y="-2787.98" font-family="Helvetica,sans-Serif" font-size="9.00">LocationService.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/application/services/LocationService.ts -->
<g id="edge100" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/application/services/LocationService.ts</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1396.08,-2670.01C1404.7,-2692.77 1429.42,-2749.62 1470.12,-2777.28 1474.55,-2780.28 1479.48,-2782.66 1484.62,-2784.54"/>
<polygon fill="none" stroke="#000000" stroke-opacity="0.200000" points="1483.91,-2786.52 1490.27,-2786.35 1485.19,-2782.52 1483.91,-2786.52"/>
</g>
<!-- src/client/domain/Catalog/catalogFiltersReducer.ts -->
<g id="node92" class="node">
<title>src/client/domain/Catalog/catalogFiltersReducer.ts</title>
<g id="a_node92"><a xlink:href="src/client/domain/Catalog/catalogFiltersReducer.ts" xlink:title="catalogFiltersReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1731.88,-415.53 1617.62,-415.53 1617.62,-397.03 1731.88,-397.03 1731.88,-415.53"/>
<text text-anchor="start" x="1625.62" y="-402.98" font-family="Helvetica,sans-Serif" font-size="9.00">catalogFiltersReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogFiltersReducer.ts -->
<g id="edge101" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogFiltersReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1393.99,-2650.76C1401.49,-2598.11 1438.29,-2337.23 1462.12,-2123.28 1540.15,-1422.78 1553.78,-1246.9 1609.62,-544.28 1610.7,-530.74 1609.09,-432.85 1617.62,-422.28 1617.75,-422.12 1617.88,-421.97 1618.01,-421.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1619.4,-423.39 1622.46,-417.82 1616.59,-420.26 1619.4,-423.39"/>
</g>
<!-- src/client/domain/Catalog/catalogReducer.ts -->
<g id="node93" class="node">
<title>src/client/domain/Catalog/catalogReducer.ts</title>
<g id="a_node93"><a xlink:href="src/client/domain/Catalog/catalogReducer.ts" xlink:title="catalogReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1582.25,-459.53 1492.75,-459.53 1492.75,-441.03 1582.25,-441.03 1582.25,-459.53"/>
<text text-anchor="start" x="1500.75" y="-446.98" font-family="Helvetica,sans-Serif" font-size="9.00">catalogReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogReducer.ts -->
<g id="edge102" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1394.3,-2650.79C1403.48,-2598.29 1447.76,-2338.08 1462.12,-2123.28 1462.89,-2111.8 1462.98,-475.3 1470.12,-466.28 1473.88,-461.53 1478.78,-458.05 1484.22,-455.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1484.77,-457.54 1489.66,-453.47 1483.3,-453.6 1484.77,-457.54"/>
</g>
<!-- src/client/domain/Catalog/catalogSearchReducer.ts -->
<g id="node94" class="node">
<title>src/client/domain/Catalog/catalogSearchReducer.ts</title>
<g id="a_node94"><a xlink:href="src/client/domain/Catalog/catalogSearchReducer.ts" xlink:title="catalogSearchReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1596.88,-490.53 1478.12,-490.53 1478.12,-472.03 1596.88,-472.03 1596.88,-490.53"/>
<text text-anchor="start" x="1486.12" y="-477.98" font-family="Helvetica,sans-Serif" font-size="9.00">catalogSearchReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogSearchReducer.ts -->
<g id="edge103" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogSearchReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1394.29,-2650.79C1403.43,-2598.29 1447.53,-2338.06 1462.12,-2123.28 1479.39,-1869.19 1452.31,-1231.33 1470.12,-977.28 1483.28,-789.74 1521.62,-564.86 1533.17,-499.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1535.21,-500.28 1534.2,-494 1531.08,-499.54 1535.21,-500.28"/>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="node95" class="node">
<title>src/client/domain/DeckBuilder/deckBuilderReducer.ts</title>
<g id="a_node95"><a xlink:href="src/client/domain/DeckBuilder/deckBuilderReducer.ts" xlink:title="deckBuilderReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1591.62,-242.53 1483.38,-242.53 1483.38,-224.03 1591.62,-224.03 1591.62,-242.53"/>
<text text-anchor="start" x="1491.38" y="-229.98" font-family="Helvetica,sans-Serif" font-size="9.00">deckBuilderReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="edge104" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1394.3,-2650.79C1403.49,-2598.29 1447.81,-2338.08 1462.12,-2123.28 1463.82,-2097.78 1458.67,-306.13 1470.12,-283.28 1477.98,-267.61 1493.33,-255.55 1507.29,-247.21"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1508.01,-249.21 1512.21,-244.44 1505.95,-245.55 1508.01,-249.21"/>
</g>
<!-- src/client/domain/GameSettings/gameSettingsReducer.ts -->
<g id="node96" class="node">
<title>src/client/domain/GameSettings/gameSettingsReducer.ts</title>
<g id="a_node96"><a xlink:href="src/client/domain/GameSettings/gameSettingsReducer.ts" xlink:title="gameSettingsReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1449.5,-326.53 1333.75,-326.53 1333.75,-308.03 1449.5,-308.03 1449.5,-326.53"/>
<text text-anchor="start" x="1341.75" y="-313.98" font-family="Helvetica,sans-Serif" font-size="9.00">gameSettingsReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/store/appStore.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsReducer.ts -->
<g id="edge105" class="edge">
<title>src/client/application/store/appStore.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1389.73,-2650.69C1379.34,-2597.67 1329.27,-2334.82 1313.75,-2117.28 1310.29,-2068.79 1311.71,-1290.85 1313.75,-1242.28 1329.14,-876.05 1378.66,-430.41 1389.51,-335.56"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1391.57,-336.04 1390.17,-329.84 1387.4,-335.56 1391.57,-336.04"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge121" class="edge">
<title>src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1429.09,-211.28C1476.19,-211.28 1558.77,-211.28 1614.83,-211.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1614.56,-213.38 1620.56,-211.28 1614.56,-209.18 1614.56,-213.38"/>
</g>
<!-- src/client/domain/Catalog/CatalogCard.ts -->
<g id="node49" class="node">
<title>src/client/domain/Catalog/CatalogCard.ts</title>
<g id="a_node49"><a xlink:href="src/client/domain/Catalog/CatalogCard.ts" xlink:title="CatalogCard.ts">
<polygon fill="#fa9f36" stroke="black" points="1835.12,-468.53 1757.62,-468.53 1757.62,-450.03 1835.12,-450.03 1835.12,-468.53"/>
<text text-anchor="start" x="1765.62" y="-455.98" font-family="Helvetica,sans-Serif" font-size="9.00">CatalogCard.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts -->
<g id="edge120" class="edge">
<title>src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1429.27,-213C1440.9,-215.19 1453.07,-219.39 1462.12,-227.28 1469.66,-233.84 1462.3,-242.08 1470.12,-248.28 1562.13,-321.15 1650.31,-198.89 1731.88,-283.28 1752.21,-304.32 1731.6,-388.21 1744.88,-414.28 1751.05,-426.41 1761.98,-436.78 1772.1,-444.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1770.69,-446.06 1776.78,-447.86 1773.14,-442.65 1770.69,-446.06"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="edge122" class="edge">
<title>src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1429.3,-216.89C1442.94,-218.98 1458.83,-221.4 1474.13,-223.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1473.81,-225.82 1480.06,-224.65 1474.44,-221.67 1473.81,-225.82"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="node98" class="node">
<title>src/client/domain/DeckBuilder/DeckBuilderCard.ts</title>
<g id="a_node98"><a xlink:href="src/client/domain/DeckBuilder/DeckBuilderCard.ts" xlink:title="DeckBuilderCard.ts">
<polygon fill="#fa9f36" stroke="black" points="1844.12,-248.53 1748.62,-248.53 1748.62,-230.03 1844.12,-230.03 1844.12,-248.53"/>
<text text-anchor="start" x="1756.62" y="-235.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderCard.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderEvents.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="edge124" class="edge">
<title>src/client/domain/DeckBuilder/deckBuilderEvents.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1717.25,-221C1726.25,-223.1 1735.87,-225.35 1745.16,-227.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1744.68,-229.57 1751,-228.89 1745.63,-225.48 1744.68,-229.57"/>
</g>
<!-- src/client/application/commands/clearDeckDraft/clearDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge30" class="edge">
<title>src/client/application/commands/clearDeckDraft/clearDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1090.99,-1326.01C1108.73,-1331.97 1127.66,-1341.9 1138.25,-1358.28 1149.03,-1374.96 1132.17,-2774.28 1146.25,-2788.28 1159.13,-2801.09 1293.84,-2797.05 1309.75,-2788.28 1352.75,-2764.58 1376.06,-2707.33 1385.52,-2678.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1387.46,-2678.91 1387.22,-2672.56 1383.45,-2677.68 1387.46,-2678.91"/>
</g>
<!-- src/client/application/commands/filterCatalog/filterCatalog.ts -->
<g id="node40" class="node">
<title>src/client/application/commands/filterCatalog/filterCatalog.ts</title>
<g id="a_node40"><a xlink:href="src/client/application/commands/filterCatalog/filterCatalog.ts" xlink:title="filterCatalog.ts">
<polygon fill="#fb6969" stroke="black" points="1261.25,-1265.53 1186.75,-1265.53 1186.75,-1247.03 1261.25,-1247.03 1261.25,-1265.53"/>
<text text-anchor="start" x="1194.75" y="-1252.98" font-family="Helvetica,sans-Serif" font-size="9.00">filterCatalog.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge32" class="edge">
<title>src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1244.38,-1265.96C1264.92,-1277.43 1296.21,-1298.65 1309.75,-1327.28 1319.2,-1347.27 1316.15,-2101.23 1317.75,-2123.28 1332.62,-2328.09 1375.11,-2574.02 1387.31,-2642.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1385.22,-2642.28 1388.35,-2647.81 1389.35,-2641.54 1385.22,-2642.28"/>
</g>
<!-- src/client/application/commands/filterCatalog/filterCatalogRequest.ts -->
<g id="node41" class="node">
<title>src/client/application/commands/filterCatalog/filterCatalogRequest.ts</title>
<g id="a_node41"><a xlink:href="src/client/application/commands/filterCatalog/filterCatalogRequest.ts" xlink:title="filterCatalogRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1446.12,-1265.53 1337.12,-1265.53 1337.12,-1247.03 1446.12,-1247.03 1446.12,-1265.53"/>
<text text-anchor="start" x="1345.12" y="-1252.98" font-family="Helvetica,sans-Serif" font-size="9.00">filterCatalogRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalogRequest.ts -->
<g id="edge31" class="edge">
<title>src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalogRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1261.35,-1256.28C1280.85,-1256.28 1305.44,-1256.28 1327.92,-1256.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1327.72,-1258.38 1333.72,-1256.28 1327.72,-1254.18 1327.72,-1258.38"/>
</g>
<!-- src/client/domain/Catalog/CatalogFilters.ts -->
<g id="node42" class="node">
<title>src/client/domain/Catalog/CatalogFilters.ts</title>
<g id="a_node42"><a xlink:href="src/client/domain/Catalog/CatalogFilters.ts" xlink:title="CatalogFilters.ts">
<polygon fill="#fa9f36" stroke="black" points="1578.5,-404.53 1496.5,-404.53 1496.5,-386.03 1578.5,-386.03 1578.5,-404.53"/>
<text text-anchor="start" x="1504.5" y="-391.98" font-family="Helvetica,sans-Serif" font-size="9.00">CatalogFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/domain/Catalog/CatalogFilters.ts -->
<g id="edge33" class="edge">
<title>src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/domain/Catalog/CatalogFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1240.82,-1246.56C1261.17,-1233.03 1295.56,-1206.41 1309.75,-1173.28 1322.89,-1142.58 1303.95,-602.69 1317.75,-572.28 1355.51,-489.05 1454.91,-432.94 1505.69,-408.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1506.41,-410.74 1510.95,-406.3 1504.63,-406.94 1506.41,-410.74"/>
</g>
<!-- src/client/domain/Catalog/catalogFilterEvents.ts -->
<g id="node67" class="node">
<title>src/client/domain/Catalog/catalogFilterEvents.ts</title>
<g id="a_node67"><a xlink:href="src/client/domain/Catalog/catalogFilterEvents.ts" xlink:title="catalogFilterEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1847.88,-408.53 1744.88,-408.53 1744.88,-390.03 1847.88,-390.03 1847.88,-408.53"/>
<text text-anchor="start" x="1752.88" y="-395.98" font-family="Helvetica,sans-Serif" font-size="9.00">catalogFilterEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts -->
<g id="edge110" class="edge">
<title>src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1578.91,-392.49C1618.06,-390.25 1679.09,-387.97 1731.88,-391.28 1733.16,-391.36 1734.45,-391.45 1735.76,-391.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1735.31,-393.62 1741.46,-392.04 1735.67,-389.43 1735.31,-393.62"/>
</g>
<!-- src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge112" class="edge">
<title>src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1578.87,-388.45C1653.06,-376.77 1807.58,-356.75 1852.12,-384.28 1879.87,-401.43 1892.28,-439.79 1897.4,-462.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1895.33,-463.18 1898.57,-468.65 1899.45,-462.35 1895.33,-463.18"/>
</g>
<!-- src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFiltersReducer.ts -->
<g id="edge111" class="edge">
<title>src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFiltersReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1578.71,-398.55C1588.05,-399.31 1598.22,-400.14 1608.33,-400.96"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1608.01,-403.04 1614.16,-401.43 1608.35,-398.85 1608.01,-403.04"/>
</g>
<!-- src/client/application/commands/hideCardDetails/hideCardDetails.ts -->
<g id="node43" class="node">
<title>src/client/application/commands/hideCardDetails/hideCardDetails.ts</title>
<g id="a_node43"><a xlink:href="src/client/application/commands/hideCardDetails/hideCardDetails.ts" xlink:title="hideCardDetails.ts">
<polygon fill="#fb6969" stroke="black" points="1093.5,-655.53 1001.75,-655.53 1001.75,-637.03 1093.5,-637.03 1093.5,-655.53"/>
<text text-anchor="start" x="1009.75" y="-642.98" font-family="Helvetica,sans-Serif" font-size="9.00">hideCardDetails.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge34" class="edge">
<title>src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.81,-655.98C1110.72,-662.03 1128.23,-671.75 1138.25,-687.28 1152.74,-709.74 1129.27,-2590.65 1146.25,-2611.28 1159.22,-2627.04 1285.75,-2646.18 1351.45,-2655.16"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1351.07,-2657.23 1357.3,-2655.95 1351.64,-2653.07 1351.07,-2657.23"/>
</g>
<!-- src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge35" class="edge">
<title>src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1049.2,-636.54C1052.92,-577.1 1075.75,-263.83 1146.25,-207.28 1206.95,-158.59 1306.09,-181.72 1357.45,-198.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1356.65,-200.69 1363,-200.65 1358.01,-196.72 1356.65,-200.69"/>
</g>
<!-- src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge36" class="edge">
<title>src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1053.59,-636.59C1065.78,-614.03 1099.58,-558.24 1146.25,-536.28 1192.87,-514.35 1573.21,-556.72 1609.62,-520.28 1618.94,-510.96 1613.13,-295.67 1617.62,-283.28 1625.71,-261 1643.49,-240.18 1656.89,-226.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1658.05,-228.53 1660.89,-222.84 1655.12,-225.52 1658.05,-228.53"/>
</g>
<!-- src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts -->
<g id="node44" class="node">
<title>src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts</title>
<g id="a_node44"><a xlink:href="src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts" xlink:title="initializeDeckBuilderFromLocation.ts">
<polygon fill="#fb6969" stroke="black" points="1130.25,-1204.53 965,-1204.53 965,-1186.03 1130.25,-1186.03 1130.25,-1204.53"/>
<text text-anchor="start" x="973" y="-1191.98" font-family="Helvetica,sans-Serif" font-size="9.00">initializeDeckBuilderFromLocation.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge39" class="edge">
<title>src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.8,-1204.98C1110.71,-1211.04 1128.22,-1220.76 1138.25,-1236.28 1149.73,-1254.06 1131.24,-2745.35 1146.25,-2760.28 1159.13,-2773.09 1293.36,-2768.11 1309.75,-2760.28 1345.64,-2743.15 1370.74,-2701.44 1382.69,-2677.66"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1384.46,-2678.82 1385.18,-2672.51 1380.68,-2677 1384.46,-2678.82"/>
</g>
<!-- src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalog.ts -->
<g id="edge37" class="edge">
<title>src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalog.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1076.26,-1204.95C1106.14,-1215.4 1153.91,-1232.11 1186.78,-1243.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1186,-1245.56 1192.35,-1245.56 1187.38,-1241.6 1186,-1245.56"/>
</g>
<!-- src/client/application/commands/search/search.ts -->
<g id="node45" class="node">
<title>src/client/application/commands/search/search.ts</title>
<g id="a_node45"><a xlink:href="src/client/application/commands/search/search.ts" xlink:title="search.ts">
<polygon fill="#fb6969" stroke="black" points="1251,-1356.53 1197,-1356.53 1197,-1338.03 1251,-1338.03 1251,-1356.53"/>
<text text-anchor="start" x="1205.62" y="-1343.98" font-family="Helvetica,sans-Serif" font-size="9.00">search.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/search/search.ts -->
<g id="edge38" class="edge">
<title>src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/search/search.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1090.96,-1204.99C1108.25,-1211.12 1126.83,-1220.89 1138.25,-1236.28 1154.02,-1257.53 1132.3,-1272.79 1146.25,-1295.28 1156.82,-1312.33 1175.28,-1325.1 1191.45,-1333.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1190.16,-1335.42 1196.46,-1336.25 1192.05,-1331.67 1190.16,-1335.42"/>
</g>
<!-- src/client/application/commands/search/search.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge61" class="edge">
<title>src/client/application/commands/search/search.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1236.3,-1356.76C1255.72,-1373.86 1294.77,-1412.07 1309.75,-1454.28 1322.18,-1489.31 1315.02,-2086.21 1317.75,-2123.28 1332.81,-2328.08 1375.17,-2574.02 1387.32,-2642.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1385.23,-2642.28 1388.36,-2647.81 1389.36,-2641.53 1385.23,-2642.28"/>
</g>
<!-- src/client/application/commands/search/searchRequest.ts -->
<g id="node59" class="node">
<title>src/client/application/commands/search/searchRequest.ts</title>
<g id="a_node59"><a xlink:href="src/client/application/commands/search/searchRequest.ts" xlink:title="searchRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1435.25,-1356.53 1348,-1356.53 1348,-1338.03 1435.25,-1338.03 1435.25,-1356.53"/>
<text text-anchor="start" x="1356" y="-1343.98" font-family="Helvetica,sans-Serif" font-size="9.00">searchRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/search/search.ts&#45;&gt;src/client/application/commands/search/searchRequest.ts -->
<g id="edge60" class="edge">
<title>src/client/application/commands/search/search.ts&#45;&gt;src/client/application/commands/search/searchRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1251.24,-1347.28C1274.72,-1347.28 1309.81,-1347.28 1338.91,-1347.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1338.71,-1349.38 1344.71,-1347.28 1338.71,-1345.18 1338.71,-1349.38"/>
</g>
<!-- src/client/domain/Catalog/catalogSearchEvents.ts -->
<g id="node60" class="node">
<title>src/client/domain/Catalog/catalogSearchEvents.ts</title>
<g id="a_node60"><a xlink:href="src/client/domain/Catalog/catalogSearchEvents.ts" xlink:title="catalogSearchEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1730.75,-490.53 1618.75,-490.53 1618.75,-472.03 1730.75,-472.03 1730.75,-490.53"/>
<text text-anchor="start" x="1626.75" y="-477.98" font-family="Helvetica,sans-Serif" font-size="9.00">catalogSearchEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/search/search.ts&#45;&gt;src/client/domain/Catalog/catalogSearchEvents.ts -->
<g id="edge62" class="edge">
<title>src/client/application/commands/search/search.ts&#45;&gt;src/client/domain/Catalog/catalogSearchEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1251.2,-1339.03C1271.14,-1331.22 1297.23,-1317.35 1309.75,-1295.28 1319.28,-1278.49 1305.37,-615.09 1317.75,-600.28 1402.48,-498.95 1492.65,-605.63 1609.62,-544.28 1631.05,-533.05 1650.03,-512.32 1661.68,-497.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1663.23,-499.15 1665.23,-493.12 1659.9,-496.59 1663.23,-499.15"/>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCards.ts -->
<g id="node46" class="node">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCards.ts</title>
<g id="a_node46"><a xlink:href="src/client/application/commands/loadCatalogCards/loadCatalogCards.ts" xlink:title="loadCatalogCards.ts">
<polygon fill="#fb6969" stroke="black" points="1097.62,-1021.53 997.62,-1021.53 997.62,-1003.03 1097.62,-1003.03 1097.62,-1021.53"/>
<text text-anchor="start" x="1005.62" y="-1008.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadCatalogCards.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge41" class="edge">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.8,-1021.98C1110.71,-1028.03 1128.23,-1037.76 1138.25,-1053.28 1150.79,-1072.71 1129.86,-2701.97 1146.25,-2718.28 1172,-2743.91 1275.16,-2729.4 1309.75,-2718.28 1335.71,-2709.93 1360.4,-2689.83 1375.52,-2675.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1376.74,-2677.39 1379.59,-2671.71 1373.82,-2674.37 1376.74,-2677.39"/>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts -->
<g id="node47" class="node">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts</title>
<g id="a_node47"><a xlink:href="src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts" xlink:title="loadCatalogCardsRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1291.25,-1021.53 1156.75,-1021.53 1156.75,-1003.03 1291.25,-1003.03 1291.25,-1021.53"/>
<text text-anchor="start" x="1164.75" y="-1008.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadCatalogCardsRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts -->
<g id="edge40" class="edge">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1097.66,-1012.28C1113.19,-1012.28 1130.73,-1012.28 1147.66,-1012.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1147.41,-1014.38 1153.41,-1012.28 1147.41,-1010.18 1147.41,-1014.38"/>
</g>
<!-- src/client/domain/Catalog/catalogEvents.ts -->
<g id="node48" class="node">
<title>src/client/domain/Catalog/catalogEvents.ts</title>
<g id="a_node48"><a xlink:href="src/client/domain/Catalog/catalogEvents.ts" xlink:title="catalogEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1716.12,-446.53 1633.38,-446.53 1633.38,-428.03 1716.12,-428.03 1716.12,-446.53"/>
<text text-anchor="start" x="1641.38" y="-433.98" font-family="Helvetica,sans-Serif" font-size="9.00">catalogEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/domain/Catalog/catalogEvents.ts -->
<g id="edge42" class="edge">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/domain/Catalog/catalogEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1097.76,-1011.55C1113.08,-1008.7 1128.5,-1002.59 1138.25,-990.28 1151.95,-972.99 1132.31,-213.37 1146.25,-196.28 1211.34,-116.48 1543.6,-112.25 1609.62,-191.28 1626.02,-210.9 1601.87,-401.14 1617.62,-421.28 1619.81,-424.07 1622.45,-426.4 1625.37,-428.34"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1624.16,-430.08 1630.45,-431 1626.11,-426.36 1624.16,-430.08"/>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts -->
<g id="edge43" class="edge">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1281.11,-1002.6C1291.06,-999.55 1301.02,-995.53 1309.75,-990.28 1548.31,-846.63 1739.62,-550.55 1785.09,-476.4"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1786.79,-477.64 1788.12,-471.43 1783.2,-475.46 1786.79,-477.64"/>
</g>
<!-- src/client/domain/Catalog/catalogEvents.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts -->
<g id="edge113" class="edge">
<title>src/client/domain/Catalog/catalogEvents.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1716.58,-444.79C1726.84,-446.68 1737.93,-448.72 1748.46,-450.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1747.89,-452.68 1754.17,-451.7 1748.64,-448.55 1747.89,-452.68"/>
</g>
<!-- src/client/domain/CardData/CardData..ts -->
<g id="node70" class="node">
<title>src/client/domain/CardData/CardData..ts</title>
<g id="a_node70"><a xlink:href="src/client/domain/CardData/CardData..ts" xlink:title="CardData..ts">
<polygon fill="#fa9f36" stroke="black" points="1935.12,-551.53 1868.12,-551.53 1868.12,-533.03 1935.12,-533.03 1935.12,-551.53"/>
<text text-anchor="start" x="1876.12" y="-538.98" font-family="Helvetica,sans-Serif" font-size="9.00">CardData..ts</text>
</a>
</g>
</g>
<!-- src/client/domain/Catalog/CatalogCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge109" class="edge">
<title>src/client/domain/Catalog/CatalogCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1805.62,-468.79C1817.01,-481.54 1838.59,-504.43 1860.12,-520.28 1863.99,-523.13 1868.27,-525.85 1872.56,-528.34"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1871.28,-530.04 1877.54,-531.11 1873.31,-526.36 1871.28,-530.04"/>
</g>
<!-- src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge44" class="edge">
<title>src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1089.82,-1264.85C1107.78,-1270.82 1127.22,-1280.78 1138.25,-1297.28 1151.6,-1317.25 1131.44,-1494.37 1146.25,-1513.28 1150.3,-1518.46 1155.49,-1522.47 1161.25,-1525.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1160.32,-1527.46 1166.65,-1528.02 1162.05,-1523.63 1160.32,-1527.46"/>
</g>
<!-- src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge45" class="edge">
<title>src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1089.59,-1264.54C1107.73,-1270.46 1127.39,-1280.47 1138.25,-1297.28 1149.38,-1314.51 1131.71,-2759.81 1146.25,-2774.28 1159.13,-2787.09 1293.61,-2782.62 1309.75,-2774.28 1349.07,-2753.97 1373.41,-2704.74 1384.16,-2678.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1385.99,-2679.19 1386.2,-2672.83 1382.08,-2677.67 1385.99,-2679.19"/>
</g>
<!-- src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge46" class="edge">
<title>src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1089.8,-1256.73C1107.52,-1254.6 1126.77,-1248.7 1138.25,-1234.28 1147.71,-1222.39 1142.56,-702.02 1146.25,-687.28 1184.76,-533.51 1268.83,-523.43 1309.75,-370.28 1314.75,-351.58 1303.75,-209.65 1317.75,-196.28 1359.58,-156.33 1530.29,-182.76 1619.25,-199.9"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1618.6,-201.91 1624.89,-201 1619.4,-197.79 1618.6,-201.91"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge48" class="edge">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.75,-778.01C1110.65,-784.07 1128.18,-793.79 1138.25,-809.28 1148.91,-825.68 1134.39,-1497.73 1146.25,-1513.28 1150.39,-1518.71 1155.77,-1522.86 1161.77,-1526.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1160.54,-1527.78 1166.88,-1528.3 1162.25,-1523.94 1160.54,-1527.78"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge49" class="edge">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.81,-777.98C1110.71,-784.03 1128.23,-793.75 1138.25,-809.28 1152.2,-830.91 1128.01,-2644.12 1146.25,-2662.28 1160.39,-2676.36 1285.75,-2668.66 1351.21,-2663.6"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1351.19,-2665.7 1357.01,-2663.14 1350.86,-2661.52 1351.19,-2665.7"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge50" class="edge">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1101.58,-765.84C1115.29,-762.64 1128.82,-756.76 1138.25,-746.28 1155.94,-726.6 1132.86,-710.11 1146.25,-687.28 1194.49,-605 1228.72,-592.49 1317.75,-558.28 1319.65,-557.55 1608.23,-533.76 1609.62,-532.28 1619.13,-522.22 1612.92,-296.3 1617.62,-283.28 1625.69,-261 1643.48,-240.18 1656.88,-226.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1658.04,-228.52 1660.88,-222.84 1655.11,-225.51 1658.04,-228.52"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts -->
<g id="node52" class="node">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts</title>
<g id="a_node52"><a xlink:href="src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts" xlink:title="loadDeckIntoBuilderRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1295.38,-777.53 1152.62,-777.53 1152.62,-759.03 1295.38,-759.03 1295.38,-777.53"/>
<text text-anchor="start" x="1160.62" y="-764.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadDeckIntoBuilderRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts -->
<g id="edge47" class="edge">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1101.42,-768.28C1114.7,-768.28 1129.25,-768.28 1143.52,-768.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1143.33,-770.38 1149.33,-768.28 1143.33,-766.18 1143.33,-770.38"/>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettings.ts -->
<g id="node53" class="node">
<title>src/client/application/commands/loadGameSettings/loadGameSettings.ts</title>
<g id="a_node53"><a xlink:href="src/client/application/commands/loadGameSettings/loadGameSettings.ts" xlink:title="loadGameSettings.ts">
<polygon fill="#fb6969" stroke="black" points="1098,-1082.53 997.25,-1082.53 997.25,-1064.03 1098,-1064.03 1098,-1082.53"/>
<text text-anchor="start" x="1005.25" y="-1069.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadGameSettings.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge52" class="edge">
<title>src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.8,-1082.98C1110.71,-1089.03 1128.23,-1098.76 1138.25,-1114.28 1150.44,-1133.16 1130.32,-2716.43 1146.25,-2732.28 1172,-2757.91 1275.79,-2745.21 1309.75,-2732.28 1338.8,-2721.22 1364.07,-2694.4 1378.32,-2676.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1379.82,-2678.3 1381.86,-2672.28 1376.51,-2675.71 1379.82,-2678.3"/>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts -->
<g id="node54" class="node">
<title>src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts</title>
<g id="a_node54"><a xlink:href="src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts" xlink:title="loadGameSettingsRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1291.62,-1082.53 1156.38,-1082.53 1156.38,-1064.03 1291.62,-1064.03 1291.62,-1082.53"/>
<text text-anchor="start" x="1164.38" y="-1069.98" font-family="Helvetica,sans-Serif" font-size="9.00">loadGameSettingsRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts -->
<g id="edge51" class="edge">
<title>src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1098.13,-1073.28C1113.48,-1073.28 1130.75,-1073.28 1147.45,-1073.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1147.09,-1075.38 1153.09,-1073.28 1147.09,-1071.18 1147.09,-1075.38"/>
</g>
<!-- src/client/domain/GameSettings/gameSettingsEvents.ts -->
<g id="node55" class="node">
<title>src/client/domain/GameSettings/gameSettingsEvents.ts</title>
<g id="a_node55"><a xlink:href="src/client/domain/GameSettings/gameSettingsEvents.ts" xlink:title="gameSettingsEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1592,-312.53 1483,-312.53 1483,-294.03 1592,-294.03 1592,-312.53"/>
<text text-anchor="start" x="1491" y="-299.98" font-family="Helvetica,sans-Serif" font-size="9.00">gameSettingsEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsEvents.ts -->
<g id="edge53" class="edge">
<title>src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1098.19,-1072.43C1113.35,-1069.53 1128.56,-1063.43 1138.25,-1051.28 1146.84,-1040.51 1143.13,-568.7 1146.25,-555.28 1186.71,-381.08 1166.73,-287.07 1317.75,-191.28 1371.94,-156.91 1414.79,-147.96 1462.12,-191.28 1476.94,-204.84 1457.66,-265.54 1470.12,-281.28 1472.46,-284.23 1475.19,-286.78 1478.2,-288.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1476.87,-290.64 1483.08,-291.99 1479.07,-287.06 1476.87,-290.64"/>
</g>
<!-- src/client/domain/GameSettings/GameSettings.ts -->
<g id="node99" class="node">
<title>src/client/domain/GameSettings/GameSettings.ts</title>
<g id="a_node99"><a xlink:href="src/client/domain/GameSettings/GameSettings.ts" xlink:title="GameSettings.ts">
<polygon fill="#fa9f36" stroke="black" points="1716.12,-319.53 1633.38,-319.53 1633.38,-301.03 1716.12,-301.03 1716.12,-319.53"/>
<text text-anchor="start" x="1641.38" y="-306.98" font-family="Helvetica,sans-Serif" font-size="9.00">GameSettings.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/GameSettings/gameSettingsEvents.ts&#45;&gt;src/client/domain/GameSettings/GameSettings.ts -->
<g id="edge127" class="edge">
<title>src/client/domain/GameSettings/gameSettingsEvents.ts&#45;&gt;src/client/domain/GameSettings/GameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1592.34,-306.07C1602.83,-306.61 1613.77,-307.18 1624.11,-307.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1623.89,-309.8 1629.99,-308.02 1624.11,-305.61 1623.89,-309.8"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts -->
<g id="node56" class="node">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts</title>
<g id="a_node56"><a xlink:href="src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts" xlink:title="removeCardFromDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1106.62,-1143.53 988.62,-1143.53 988.62,-1125.03 1106.62,-1125.03 1106.62,-1143.53"/>
<text text-anchor="start" x="996.62" y="-1130.98" font-family="Helvetica,sans-Serif" font-size="9.00">removeCardFromDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge54" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.19,-1143.91C1110.22,-1149.96 1127.99,-1159.71 1138.25,-1175.28 1148.59,-1190.96 1134.77,-1498.42 1146.25,-1513.28 1150.42,-1518.68 1155.82,-1522.82 1161.83,-1525.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1160.62,-1527.73 1166.95,-1528.25 1162.32,-1523.9 1160.62,-1527.73"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge56" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.8,-1143.98C1110.71,-1150.03 1128.22,-1159.76 1138.25,-1175.28 1150.09,-1193.61 1130.78,-2730.89 1146.25,-2746.28 1159.13,-2759.09 1293.07,-2753.49 1309.75,-2746.28 1342.14,-2732.28 1367.54,-2698.22 1380.7,-2677.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1382.38,-2678.7 1383.71,-2672.48 1378.79,-2676.51 1382.38,-2678.7"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge57" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1107.04,-1131.32C1119.08,-1127.9 1130.45,-1122.04 1138.25,-1112.28 1153,-1093.84 1135.75,-708.43 1146.25,-687.28 1187.41,-604.37 1268.37,-641.08 1309.75,-558.28 1317.97,-541.83 1306.05,-241.47 1317.75,-227.28 1324.57,-219 1334.61,-214.41 1345.06,-211.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1345.36,-214.06 1350.9,-210.94 1344.63,-209.92 1345.36,-214.06"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge58" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1107.07,-1131.34C1119.11,-1127.92 1130.47,-1122.06 1138.25,-1112.28 1147.82,-1100.26 1143,-574.29 1146.25,-559.28 1186.53,-373.43 1158.6,-271.37 1317.75,-167.28 1372.03,-131.78 1547.67,-148.07 1609.62,-167.28 1627.74,-172.9 1645.27,-185.59 1657.42,-195.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1655.75,-197.24 1661.64,-199.64 1658.53,-194.09 1655.75,-197.24"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts -->
<g id="node57" class="node">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts</title>
<g id="a_node57"><a xlink:href="src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts" xlink:title="removeCardFromDeckRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1300.25,-1143.53 1147.75,-1143.53 1147.75,-1125.03 1300.25,-1125.03 1300.25,-1143.53"/>
<text text-anchor="start" x="1155.75" y="-1130.98" font-family="Helvetica,sans-Serif" font-size="9.00">removeCardFromDeckRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts -->
<g id="edge55" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1106.69,-1134.28C1116.91,-1134.28 1127.73,-1134.28 1138.49,-1134.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1138.48,-1136.38 1144.48,-1134.28 1138.48,-1132.18 1138.48,-1136.38"/>
</g>
<!-- src/client/application/commands/saveDeckDraft/saveDeckDraft.ts -->
<g id="node58" class="node">
<title>src/client/application/commands/saveDeckDraft/saveDeckDraft.ts</title>
<g id="a_node58"><a xlink:href="src/client/application/commands/saveDeckDraft/saveDeckDraft.ts" xlink:title="saveDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1090.12,-1387.53 1005.12,-1387.53 1005.12,-1369.03 1090.12,-1369.03 1090.12,-1387.53"/>
<text text-anchor="start" x="1013.12" y="-1374.98" font-family="Helvetica,sans-Serif" font-size="9.00">saveDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/saveDeckDraft/saveDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge59" class="edge">
<title>src/client/application/commands/saveDeckDraft/saveDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1049.43,-1387.96C1056.47,-1473.31 1108.33,-2106.67 1138.25,-2621.28 1138.83,-2631.33 1138.98,-2795.32 1146.25,-2802.28 1172.5,-2827.4 1278.34,-2820.54 1309.75,-2802.28 1356.21,-2775.28 1378.19,-2710.5 1386.49,-2678.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1388.52,-2679.14 1387.91,-2672.81 1384.44,-2678.14 1388.52,-2679.14"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts -->
<g id="node61" class="node">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts</title>
<g id="a_node61"><a xlink:href="src/client/application/commands/showCardDetails/showCardDetails.ts" xlink:title="showCardDetails.ts">
<polygon fill="#fb6969" stroke="black" points="1095.38,-716.53 999.88,-716.53 999.88,-698.03 1095.38,-698.03 1095.38,-716.53"/>
<text text-anchor="start" x="1007.88" y="-703.98" font-family="Helvetica,sans-Serif" font-size="9.00">showCardDetails.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts -->
<g id="edge64" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/queries/getCatalogCardById/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.76,-717.01C1110.66,-723.07 1128.18,-732.78 1138.25,-748.28 1149.83,-766.1 1133.37,-1496.38 1146.25,-1513.28 1150.38,-1518.71 1155.77,-1522.86 1161.76,-1526.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1160.54,-1527.78 1166.87,-1528.31 1162.25,-1523.95 1160.54,-1527.78"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge65" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.81,-716.98C1110.71,-723.03 1128.23,-732.75 1138.25,-748.28 1152.56,-770.46 1128.01,-2629.21 1146.25,-2648.28 1160.06,-2662.72 1285.57,-2662.42 1351.14,-2661.24"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1350.99,-2663.35 1356.94,-2661.13 1350.91,-2659.15 1350.99,-2663.35"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge66" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1095.84,-706.84C1111.76,-704.14 1128.05,-698.05 1138.25,-685.28 1154.33,-665.13 1128.24,-239.73 1146.25,-221.28 1172.83,-194.06 1281.93,-200.05 1345,-206.09"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1344.77,-208.18 1350.95,-206.68 1345.19,-204 1344.77,-208.18"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge67" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1095.86,-706.86C1111.79,-704.16 1128.08,-698.07 1138.25,-685.28 1148.66,-672.19 1134.35,-95.03 1146.25,-83.28 1189.94,-40.17 1517.02,-40.18 1609.62,-97.28 1644.57,-118.83 1662.14,-166.8 1669.45,-193.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1667.39,-193.55 1670.93,-198.83 1671.45,-192.49 1667.39,-193.55"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetailsRequest.ts -->
<g id="node62" class="node">
<title>src/client/application/commands/showCardDetails/showCardDetailsRequest.ts</title>
<g id="a_node62"><a xlink:href="src/client/application/commands/showCardDetails/showCardDetailsRequest.ts" xlink:title="showCardDetailsRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1289,-716.53 1159,-716.53 1159,-698.03 1289,-698.03 1289,-716.53"/>
<text text-anchor="start" x="1167" y="-703.98" font-family="Helvetica,sans-Serif" font-size="9.00">showCardDetailsRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/commands/showCardDetails/showCardDetailsRequest.ts -->
<g id="edge63" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/commands/showCardDetails/showCardDetailsRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1095.81,-707.28C1112.46,-707.28 1131.58,-707.28 1149.86,-707.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1149.72,-709.38 1155.72,-707.28 1149.72,-705.18 1149.72,-709.38"/>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts -->
<g id="node63" class="node">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts</title>
<g id="a_node63"><a xlink:href="src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts" xlink:title="switchDeckBuilderView.ts">
<polygon fill="#fb6969" stroke="black" points="1107.75,-838.53 987.5,-838.53 987.5,-820.03 1107.75,-820.03 1107.75,-838.53"/>
<text text-anchor="start" x="995.5" y="-825.98" font-family="Helvetica,sans-Serif" font-size="9.00">switchDeckBuilderView.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge69" class="edge">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.81,-838.98C1110.71,-845.03 1128.23,-854.75 1138.25,-870.28 1151.85,-891.36 1128.47,-2658.58 1146.25,-2676.28 1174.18,-2704.09 1289.81,-2683.23 1351.47,-2669.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1351.82,-2671.68 1357.21,-2668.32 1350.9,-2667.59 1351.82,-2671.68"/>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge70" class="edge">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1108,-826.08C1119.69,-822.62 1130.67,-816.81 1138.25,-807.28 1158.04,-782.41 1124.74,-258.68 1146.25,-235.28 1152.89,-228.06 1276.21,-218.82 1345.07,-214.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1345.13,-216.3 1350.98,-213.8 1344.85,-212.11 1345.13,-216.3"/>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge71" class="edge">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1108.02,-826.09C1119.71,-822.63 1130.68,-816.82 1138.25,-807.28 1150.27,-792.14 1132.5,-124.87 1146.25,-111.28 1182.86,-75.08 1564.45,-86.58 1609.62,-111.28 1641.42,-128.66 1659.77,-169.28 1668.16,-193.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1666.14,-193.64 1670.03,-198.67 1670.13,-192.32 1666.14,-193.64"/>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts -->
<g id="node64" class="node">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts</title>
<g id="a_node64"><a xlink:href="src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts" xlink:title="switchDeckBuilderViewRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1301.75,-838.53 1146.25,-838.53 1146.25,-820.03 1301.75,-820.03 1301.75,-838.53"/>
<text text-anchor="start" x="1154.25" y="-825.98" font-family="Helvetica,sans-Serif" font-size="9.00">switchDeckBuilderViewRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts -->
<g id="edge68" class="edge">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1108.15,-829.28C1117.44,-829.28 1127.2,-829.28 1136.94,-829.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1136.84,-831.38 1142.84,-829.28 1136.84,-827.18 1136.84,-831.38"/>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts -->
<g id="node65" class="node">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts</title>
<g id="a_node65"><a xlink:href="src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts" xlink:title="updateAvailableFilters.ts">
<polygon fill="#fb6969" stroke="black" points="1105.88,-899.53 989.38,-899.53 989.38,-881.03 1105.88,-881.03 1105.88,-899.53"/>
<text text-anchor="start" x="997.38" y="-886.98" font-family="Helvetica,sans-Serif" font-size="9.00">updateAvailableFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge73" class="edge">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1093.81,-899.98C1110.71,-906.03 1128.23,-915.75 1138.25,-931.28 1151.5,-951.81 1128.94,-2673.05 1146.25,-2690.28 1172,-2715.91 1273.99,-2696.7 1309.75,-2690.28 1327.91,-2687.02 1347.32,-2679.95 1362.57,-2673.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1363.15,-2675.53 1367.81,-2671.21 1361.47,-2671.68 1363.15,-2675.53"/>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts -->
<g id="node66" class="node">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts</title>
<g id="a_node66"><a xlink:href="src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts" xlink:title="updateAvailableFiltersRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1299.5,-899.53 1148.5,-899.53 1148.5,-881.03 1299.5,-881.03 1299.5,-899.53"/>
<text text-anchor="start" x="1156.5" y="-886.98" font-family="Helvetica,sans-Serif" font-size="9.00">updateAvailableFiltersRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts -->
<g id="edge72" class="edge">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1106.21,-890.28C1116.77,-890.28 1128,-890.28 1139.15,-890.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1139.04,-892.38 1145.04,-890.28 1139.04,-888.18 1139.04,-892.38"/>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts -->
<g id="edge74" class="edge">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1106.17,-887.63C1118.55,-884.24 1130.29,-878.32 1138.25,-868.28 1151.25,-851.89 1131.38,-129.99 1146.25,-115.28 1332.82,69.32 1580.24,22.94 1731.88,-191.28 1755.23,-224.27 1733.94,-242.37 1744.88,-281.28 1755.3,-318.4 1774.71,-359.25 1786.19,-381.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1784.3,-382.74 1788.91,-387.11 1788.03,-380.82 1784.3,-382.74"/>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge75" class="edge">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1273.94,-880.61C1285.93,-877.42 1298.53,-873.36 1309.75,-868.28 1522.62,-771.97 1547.5,-698.31 1744.88,-573.28 1790.42,-544.43 1844.91,-512.91 1875.88,-495.26"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1876.78,-497.17 1880.96,-492.38 1874.71,-493.51 1876.78,-497.17"/>
</g>
<!-- src/client/domain/Catalog/catalogFilterEvents.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge114" class="edge">
<title>src/client/domain/Catalog/catalogFilterEvents.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1818.62,-408.93C1829.18,-414.21 1841.86,-421.3 1852.12,-429.28 1865.43,-439.62 1878.33,-453.72 1887.48,-464.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1885.83,-465.93 1891.26,-469.24 1889.09,-463.27 1885.83,-465.93"/>
</g>
<!-- src/client/application/queries/applyFilterToCard/applyFilterToCard.ts -->
<g id="node69" class="node">
<title>src/client/application/queries/applyFilterToCard/applyFilterToCard.ts</title>
<g id="a_node69"><a xlink:href="src/client/application/queries/applyFilterToCard/applyFilterToCard.ts" xlink:title="applyFilterToCard.ts">
<polygon fill="#fb6969" stroke="black" points="1440.5,-2093.53 1342.75,-2093.53 1342.75,-2075.03 1440.5,-2075.03 1440.5,-2093.53"/>
<text text-anchor="start" x="1350.75" y="-2080.98" font-family="Helvetica,sans-Serif" font-size="9.00">applyFilterToCard.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/applyFilterToCard/applyFilterToCard.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge77" class="edge">
<title>src/client/application/queries/applyFilterToCard/applyFilterToCard.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1396.05,-2074.82C1429.76,-1981.38 1704.04,-1213.86 1852.12,-572.28 1857.19,-550.35 1849.95,-542.36 1860.12,-522.28 1865.04,-512.58 1873.09,-503.72 1880.72,-496.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1882.04,-498.38 1885.21,-492.87 1879.3,-495.19 1882.04,-498.38"/>
</g>
<!-- src/client/application/queries/applyFilterToCard/applyFilterToCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge76" class="edge">
<title>src/client/application/queries/applyFilterToCard/applyFilterToCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1416.28,-2074.62C1495.59,-2040.8 1752.61,-1919.14 1852.12,-1725.28 1907.53,-1617.35 1901.99,-702.4 1900.8,-560.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1902.9,-560.85 1900.74,-554.87 1898.7,-560.89 1902.9,-560.85"/>
</g>
<!-- src/client/application/queries/findDeckCardById/findDeckCardById.ts -->
<g id="node71" class="node">
<title>src/client/application/queries/findDeckCardById/findDeckCardById.ts</title>
<g id="a_node71"><a xlink:href="src/client/application/queries/findDeckCardById/findDeckCardById.ts" xlink:title="findDeckCardById.ts">
<polygon fill="#fb6969" stroke="black" points="1273.62,-1483.53 1174.38,-1483.53 1174.38,-1465.03 1273.62,-1465.03 1273.62,-1483.53"/>
<text text-anchor="start" x="1182.38" y="-1470.98" font-family="Helvetica,sans-Serif" font-size="9.00">findDeckCardById.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/findDeckCardById/findDeckCardById.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge78" class="edge">
<title>src/client/application/queries/findDeckCardById/findDeckCardById.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1265.95,-1483.94C1282.47,-1490.06 1299.92,-1499.84 1309.75,-1515.28 1318.82,-1529.53 1316.5,-2106.44 1317.75,-2123.28 1332.94,-2328.07 1375.2,-2574.01 1387.33,-2642.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1385.24,-2642.28 1388.36,-2647.81 1389.37,-2641.53 1385.24,-2642.28"/>
</g>
<!-- src/client/application/queries/getActiveFilters/getActiveFilters.ts -->
<g id="node72" class="node">
<title>src/client/application/queries/getActiveFilters/getActiveFilters.ts</title>
<g id="a_node72"><a xlink:href="src/client/application/queries/getActiveFilters/getActiveFilters.ts" xlink:title="getActiveFilters.ts">
<polygon fill="#fb6969" stroke="black" points="1267.62,-1666.53 1180.38,-1666.53 1180.38,-1648.03 1267.62,-1648.03 1267.62,-1666.53"/>
<text text-anchor="start" x="1188.38" y="-1653.98" font-family="Helvetica,sans-Serif" font-size="9.00">getActiveFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getActiveFilters/getActiveFilters.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge79" class="edge">
<title>src/client/application/queries/getActiveFilters/getActiveFilters.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1265.91,-1666.97C1282.42,-1673.09 1299.88,-1682.86 1309.75,-1698.28 1316.12,-1708.22 1316.84,-2111.51 1317.75,-2123.28 1333.52,-2328.03 1375.37,-2574 1387.37,-2642.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1385.27,-2642.27 1388.39,-2647.81 1389.41,-2641.53 1385.27,-2642.27"/>
</g>
<!-- src/client/application/queries/getAvailableFilters/getAvailableFilters.ts -->
<g id="node73" class="node">
<title>src/client/application/queries/getAvailableFilters/getAvailableFilters.ts</title>
<g id="a_node73"><a xlink:href="src/client/application/queries/getAvailableFilters/getAvailableFilters.ts" xlink:title="getAvailableFilters.ts">
<polygon fill="#fb6969" stroke="black" points="1274.38,-1605.53 1173.62,-1605.53 1173.62,-1587.03 1274.38,-1587.03 1274.38,-1605.53"/>
<text text-anchor="start" x="1181.62" y="-1592.98" font-family="Helvetica,sans-Serif" font-size="9.00">getAvailableFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getAvailableFilters/getAvailableFilters.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge80" class="edge">
<title>src/client/application/queries/getAvailableFilters/getAvailableFilters.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1265.93,-1605.96C1282.44,-1612.08 1299.9,-1621.85 1309.75,-1637.28 1317.02,-1648.66 1316.73,-2109.82 1317.75,-2123.28 1333.27,-2328.05 1375.3,-2574.01 1387.35,-2642.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1385.26,-2642.27 1388.38,-2647.81 1389.39,-2641.53 1385.26,-2642.27"/>
</g>
<!-- src/client/application/queries/getCardDetails/getCardDetails.ts -->
<g id="node74" class="node">
<title>src/client/application/queries/getCardDetails/getCardDetails.ts</title>
<g id="a_node74"><a xlink:href="src/client/application/queries/getCardDetails/getCardDetails.ts" xlink:title="getCardDetails.ts">
<polygon fill="#fb6969" stroke="black" points="1267.25,-1727.53 1180.75,-1727.53 1180.75,-1709.03 1267.25,-1709.03 1267.25,-1727.53"/>
<text text-anchor="start" x="1188.75" y="-1714.98" font-family="Helvetica,sans-Serif" font-size="9.00">getCardDetails.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getCardDetails/getCardDetails.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge81" class="edge">
<title>src/client/application/queries/getCardDetails/getCardDetails.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1265.89,-1727.99C1282.4,-1734.11 1299.85,-1743.88 1309.75,-1759.28 1320.68,-1776.3 1316.17,-2103.12 1317.75,-2123.28 1333.84,-2328 1375.47,-2573.99 1387.39,-2642.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1385.29,-2642.26 1388.4,-2647.81 1389.43,-2641.53 1385.29,-2642.26"/>
</g>
<!-- src/client/application/queries/getCardsInDeck/getCardsInDeck.ts -->
<g id="node75" class="node">
<title>src/client/application/queries/getCardsInDeck/getCardsInDeck.ts</title>
<g id="a_node75"><a xlink:href="src/client/application/queries/getCardsInDeck/getCardsInDeck.ts" xlink:title="getCardsInDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1269.5,-1788.53 1178.5,-1788.53 1178.5,-1770.03 1269.5,-1770.03 1269.5,-1788.53"/>
<text text-anchor="start" x="1186.5" y="-1775.98" font-family="Helvetica,sans-Serif" font-size="9.00">getCardsInDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getCardsInDeck/getCardsInDeck.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge82" class="edge">
<title>src/client/application/queries/getCardsInDeck/getCardsInDeck.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1265.85,-1789.01C1282.36,-1795.13 1299.82,-1804.9 1309.75,-1820.28 1318.88,-1834.43 1316.39,-2106.5 1317.75,-2123.28 1334.29,-2327.97 1375.6,-2573.98 1387.42,-2642.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1385.32,-2642.26 1388.42,-2647.81 1389.46,-2641.53 1385.32,-2642.26"/>
</g>
<!-- src/client/application/queries/getCatalogCards/getCatalogCards.ts -->
<g id="node76" class="node">
<title>src/client/application/queries/getCatalogCards/getCatalogCards.ts</title>
<g id="a_node76"><a xlink:href="src/client/application/queries/getCatalogCards/getCatalogCards.ts" xlink:title="getCatalogCards.ts">
<polygon fill="#fb6969" stroke="black" points="1271.38,-1849.53 1176.62,-1849.53 1176.62,-1831.03 1271.38,-1831.03 1271.38,-1849.53"/>
<text text-anchor="start" x="1184.62" y="-1836.98" font-family="Helvetica,sans-Serif" font-size="9.00">getCatalogCards.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getCatalogCards/getCatalogCards.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge84" class="edge">
<title>src/client/application/queries/getCatalogCards/getCatalogCards.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1265.36,-1849.87C1281.99,-1855.99 1299.68,-1865.8 1309.75,-1881.28 1317.08,-1892.56 1316.62,-2109.88 1317.75,-2123.28 1334.97,-2327.91 1375.8,-2573.97 1387.46,-2642.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1385.37,-2642.25 1388.45,-2647.8 1389.5,-2641.53 1385.37,-2642.25"/>
</g>
<!-- src/client/application/queries/getCatalogError/getCatalogError.ts -->
<g id="node77" class="node">
<title>src/client/application/queries/getCatalogError/getCatalogError.ts</title>
<g id="a_node77"><a xlink:href="src/client/application/queries/getCatalogError/getCatalogError.ts" xlink:title="getCatalogError.ts">
<polygon fill="#fb6969" stroke="black" points="1268.75,-2032.53 1179.25,-2032.53 1179.25,-2014.03 1268.75,-2014.03 1268.75,-2032.53"/>
<text text-anchor="start" x="1187.25" y="-2019.98" font-family="Helvetica,sans-Serif" font-size="9.00">getCatalogError.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getCatalogError/getCatalogError.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge85" class="edge">
<title>src/client/application/queries/getCatalogError/getCatalogError.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1263.33,-2032.88C1280.17,-2039.08 1298.62,-2048.93 1309.75,-2064.28 1317.52,-2074.99 1315.97,-2110.17 1317.75,-2123.28 1345.35,-2326.77 1378.86,-2573.63 1388.14,-2641.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1386.04,-2642.08 1388.92,-2647.75 1390.2,-2641.52 1386.04,-2642.08"/>
</g>
<!-- src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts -->
<g id="node78" class="node">
<title>src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts</title>
<g id="a_node78"><a xlink:href="src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts" xlink:title="getDeckBuilderView.ts">
<polygon fill="#fb6969" stroke="black" points="1277.75,-1910.53 1170.25,-1910.53 1170.25,-1892.03 1277.75,-1892.03 1277.75,-1910.53"/>
<text text-anchor="start" x="1178.25" y="-1897.98" font-family="Helvetica,sans-Serif" font-size="9.00">getDeckBuilderView.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge86" class="edge">
<title>src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1265.28,-1910.93C1281.89,-1917.06 1299.59,-1926.85 1309.75,-1942.28 1320.82,-1959.1 1315.95,-2103.23 1317.75,-2123.28 1336.11,-2327.81 1376.14,-2573.94 1387.54,-2642.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1385.44,-2642.23 1388.5,-2647.8 1389.58,-2641.53 1385.44,-2642.23"/>
</g>
<!-- src/client/application/queries/getDeckName/getDeckName.ts -->
<g id="node79" class="node">
<title>src/client/application/queries/getDeckName/getDeckName.ts</title>
<g id="a_node79"><a xlink:href="src/client/application/queries/getDeckName/getDeckName.ts" xlink:title="getDeckName.ts">
<polygon fill="#fb6969" stroke="black" points="1265.75,-1971.53 1182.25,-1971.53 1182.25,-1953.03 1265.75,-1953.03 1265.75,-1971.53"/>
<text text-anchor="start" x="1190.25" y="-1958.98" font-family="Helvetica,sans-Serif" font-size="9.00">getDeckName.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getDeckName/getDeckName.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge87" class="edge">
<title>src/client/application/queries/getDeckName/getDeckName.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1264.68,-1971.87C1281.39,-1978.01 1299.33,-1987.83 1309.75,-2003.28 1317.22,-2014.36 1316.41,-2109.99 1317.75,-2123.28 1338.4,-2327.59 1376.81,-2573.87 1387.69,-2642.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1385.59,-2642.19 1388.61,-2647.79 1389.73,-2641.53 1385.59,-2642.19"/>
</g>
<!-- src/client/application/queries/getFilteredCards/getFilteredCards.ts -->
<g id="node80" class="node">
<title>src/client/application/queries/getFilteredCards/getFilteredCards.ts</title>
<g id="a_node80"><a xlink:href="src/client/application/queries/getFilteredCards/getFilteredCards.ts" xlink:title="getFilteredCards.ts">
<polygon fill="#fb6969" stroke="black" points="1270.62,-2093.53 1177.38,-2093.53 1177.38,-2075.03 1270.62,-2075.03 1270.62,-2093.53"/>
<text text-anchor="start" x="1185.38" y="-2080.98" font-family="Helvetica,sans-Serif" font-size="9.00">getFilteredCards.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getFilteredCards/getFilteredCards.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge89" class="edge">
<title>src/client/application/queries/getFilteredCards/getFilteredCards.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1263.22,-2093.96C1280.05,-2100.17 1298.5,-2110.02 1309.75,-2125.28 1372.27,-2210.12 1387.33,-2557.24 1390.09,-2641.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1387.99,-2641.7 1390.27,-2647.63 1392.19,-2641.57 1387.99,-2641.7"/>
</g>
<!-- src/client/application/queries/getFilteredCards/getFilteredCards.ts&#45;&gt;src/client/application/queries/applyFilterToCard/applyFilterToCard.ts -->
<g id="edge88" class="edge">
<title>src/client/application/queries/getFilteredCards/getFilteredCards.ts&#45;&gt;src/client/application/queries/applyFilterToCard/applyFilterToCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1270.7,-2084.28C1290.13,-2084.28 1313,-2084.28 1333.53,-2084.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1333.45,-2086.38 1339.45,-2084.28 1333.45,-2082.18 1333.45,-2086.38"/>
</g>
<!-- src/client/application/queries/getGameSettings/getGameSettings.ts -->
<g id="node81" class="node">
<title>src/client/application/queries/getGameSettings/getGameSettings.ts</title>
<g id="a_node81"><a xlink:href="src/client/application/queries/getGameSettings/getGameSettings.ts" xlink:title="getGameSettings.ts">
<polygon fill="#fb6969" stroke="black" points="1271.75,-2215.53 1176.25,-2215.53 1176.25,-2197.03 1271.75,-2197.03 1271.75,-2215.53"/>
<text text-anchor="start" x="1184.25" y="-2202.98" font-family="Helvetica,sans-Serif" font-size="9.00">getGameSettings.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getGameSettings/getGameSettings.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge90" class="edge">
<title>src/client/application/queries/getGameSettings/getGameSettings.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1265.21,-2215.98C1281.8,-2222.12 1299.52,-2231.91 1309.75,-2247.28 1318.81,-2260.9 1315.13,-2378.13 1317.75,-2394.28 1333.22,-2489.63 1369.57,-2599.89 1384.28,-2642.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1382.26,-2642.95 1386.23,-2647.92 1386.23,-2641.57 1382.26,-2642.95"/>
</g>
<!-- src/client/application/queries/getGameSettingsError/getGameSettingsError.ts -->
<g id="node82" class="node">
<title>src/client/application/queries/getGameSettingsError/getGameSettingsError.ts</title>
<g id="a_node82"><a xlink:href="src/client/application/queries/getGameSettingsError/getGameSettingsError.ts" xlink:title="getGameSettingsError.ts">
<polygon fill="#fb6969" stroke="black" points="1281.5,-2276.53 1166.5,-2276.53 1166.5,-2258.03 1281.5,-2258.03 1281.5,-2276.53"/>
<text text-anchor="start" x="1174.5" y="-2263.98" font-family="Helvetica,sans-Serif" font-size="9.00">getGameSettingsError.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getGameSettingsError/getGameSettingsError.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge91" class="edge">
<title>src/client/application/queries/getGameSettingsError/getGameSettingsError.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1264.5,-2277C1281.16,-2283.17 1299.13,-2292.97 1309.75,-2308.28 1320.69,-2324.05 1314.31,-2375.4 1317.75,-2394.28 1335.05,-2489.32 1370.34,-2599.76 1384.52,-2642.31"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1382.5,-2642.87 1386.4,-2647.89 1386.48,-2641.53 1382.5,-2642.87"/>
</g>
<!-- src/client/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts -->
<g id="node83" class="node">
<title>src/client/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts</title>
<g id="a_node83"><a xlink:href="src/client/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts" xlink:title="getMaxCardsInDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1278.12,-2337.53 1169.88,-2337.53 1169.88,-2319.03 1278.12,-2319.03 1278.12,-2337.53"/>
<text text-anchor="start" x="1177.88" y="-2324.98" font-family="Helvetica,sans-Serif" font-size="9.00">getMaxCardsInDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge92" class="edge">
<title>src/client/application/queries/getMaxCardsInDeck/getMaxCardsInDeck.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1260.7,-2337.95C1277.77,-2344.27 1297.18,-2354.22 1309.75,-2369.28 1312.03,-2372.01 1368.49,-2578.95 1385.69,-2642.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1383.62,-2642.54 1387.23,-2647.78 1387.68,-2641.44 1383.62,-2642.54"/>
</g>
<!-- src/client/application/queries/getSearchTerm/getSearchTerm.ts -->
<g id="node84" class="node">
<title>src/client/application/queries/getSearchTerm/getSearchTerm.ts</title>
<g id="a_node84"><a xlink:href="src/client/application/queries/getSearchTerm/getSearchTerm.ts" xlink:title="getSearchTerm.ts">
<polygon fill="#fb6969" stroke="black" points="1267.62,-2459.53 1180.38,-2459.53 1180.38,-2441.03 1267.62,-2441.03 1267.62,-2459.53"/>
<text text-anchor="start" x="1188.38" y="-2446.98" font-family="Helvetica,sans-Serif" font-size="9.00">getSearchTerm.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getSearchTerm/getSearchTerm.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge93" class="edge">
<title>src/client/application/queries/getSearchTerm/getSearchTerm.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1257.13,-2459.97C1274.46,-2466.53 1295.22,-2476.69 1309.75,-2491.28 1353.81,-2535.52 1377.26,-2608.43 1386.24,-2642.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1384.12,-2642.34 1387.65,-2647.63 1388.19,-2641.29 1384.12,-2642.34"/>
</g>
<!-- src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts -->
<g id="node85" class="node">
<title>src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts</title>
<g id="a_node85"><a xlink:href="src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts" xlink:title="getTotalCardsInDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1279.25,-2520.53 1168.75,-2520.53 1168.75,-2502.03 1279.25,-2502.03 1279.25,-2520.53"/>
<text text-anchor="start" x="1176.75" y="-2507.98" font-family="Helvetica,sans-Serif" font-size="9.00">getTotalCardsInDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge94" class="edge">
<title>src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1253.28,-2521.01C1270.75,-2527.93 1292.97,-2538.45 1309.75,-2552.28 1341.63,-2578.55 1367.96,-2619.88 1381.27,-2643.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1379.31,-2643.86 1384.08,-2648.06 1382.98,-2641.8 1379.31,-2643.86"/>
</g>
<!-- src/client/application/queries/hasDeckDraft/hasDeckDraft.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="edge95" class="edge">
<title>src/client/application/queries/hasDeckDraft/hasDeckDraft.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1259.52,-2154.92C1276.71,-2161.3 1296.57,-2171.32 1309.75,-2186.28 1450.41,-2346.01 1415.58,-2432.59 1462.12,-2640.28 1465.45,-2655.13 1461.14,-2696 1470.12,-2708.28 1475.54,-2715.68 1483.21,-2721.3 1491.43,-2725.54"/>
<polygon fill="none" stroke="#000000" stroke-opacity="0.200000" points="1490.1,-2727.24 1496.42,-2727.89 1491.88,-2723.44 1490.1,-2727.24"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckDraft.ts -->
<g id="node90" class="node">
<title>src/client/domain/DeckBuilder/DeckDraft.ts</title>
<g id="a_node90"><a xlink:href="src/client/domain/DeckBuilder/DeckDraft.ts" xlink:title="DeckDraft.ts">
<polygon fill="#fa9f36" stroke="black" points="1424.38,-251.53 1358.88,-251.53 1358.88,-233.03 1424.38,-233.03 1424.38,-251.53"/>
<text text-anchor="start" x="1366.88" y="-238.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/application/services/DeckDraftService/DeckDraftService.ts&#45;&gt;src/client/domain/DeckBuilder/DeckDraft.ts -->
<g id="edge98" class="edge">
<title>src/client/application/services/DeckDraftService/DeckDraftService.ts&#45;&gt;src/client/domain/DeckBuilder/DeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1536.22,-2728.66C1531.31,-2562.02 1464.22,-286.82 1462.12,-283.28 1454.93,-271.11 1442.61,-262.17 1430.32,-255.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1431.36,-253.96 1425.04,-253.28 1429.55,-257.75 1431.36,-253.96"/>
</g>
<!-- src/client/application/queries/isCatalogLoading/isCatalogLoading.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge96" class="edge">
<title>src/client/application/queries/isCatalogLoading/isCatalogLoading.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1259.43,-2399C1276.59,-2405.4 1296.46,-2415.41 1309.75,-2430.28 1366.4,-2493.69 1383.94,-2599.22 1388.87,-2641.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1386.78,-2641.91 1389.51,-2647.65 1390.96,-2641.46 1386.78,-2641.91"/>
</g>
<!-- src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts -->
<g id="node89" class="node">
<title>src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts</title>
<g id="a_node89"><a xlink:href="src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts" xlink:title="isGameSettingsLoading.ts">
<polygon fill="#fb6969" stroke="black" points="1285.62,-2581.53 1162.38,-2581.53 1162.38,-2563.03 1285.62,-2563.03 1285.62,-2581.53"/>
<text text-anchor="start" x="1170.38" y="-2568.98" font-family="Helvetica,sans-Serif" font-size="9.00">isGameSettingsLoading.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge97" class="edge">
<title>src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1243.04,-2581.87C1272.46,-2597.5 1330.87,-2628.53 1364.72,-2646.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1363.4,-2648.19 1369.68,-2649.15 1365.37,-2644.48 1363.4,-2648.19"/>
</g>
<!-- src/client/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts -->
<g id="edge115" class="edge">
<title>src/client/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1732.06,-402.99C1733.22,-402.92 1734.38,-402.85 1735.54,-402.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1735.52,-404.89 1741.39,-402.44 1735.28,-400.69 1735.52,-404.89"/>
</g>
<!-- src/client/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge116" class="edge">
<title>src/client/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1732.2,-415.68C1775.41,-423.39 1831.07,-434.58 1852.12,-444.28 1863.81,-449.67 1875.33,-458.28 1884.21,-465.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1882.56,-467.18 1888.45,-469.59 1885.35,-464.04 1882.56,-467.18"/>
</g>
<!-- src/client/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/domain/Catalog/catalogEvents.ts -->
<g id="edge117" class="edge">
<title>src/client/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/domain/Catalog/catalogEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1582.41,-446.06C1595.83,-444.77 1610.66,-443.34 1624.41,-442.02"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1624.48,-444.13 1630.25,-441.46 1624.08,-439.95 1624.48,-444.13"/>
</g>
<!-- src/client/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts -->
<g id="edge118" class="edge">
<title>src/client/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1582.66,-451.83C1628.88,-453.45 1701.18,-455.98 1748.42,-457.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1748.13,-459.73 1754.2,-457.84 1748.28,-455.53 1748.13,-459.73"/>
</g>
<!-- src/client/domain/Catalog/catalogSearchReducer.ts&#45;&gt;src/client/domain/Catalog/catalogSearchEvents.ts -->
<g id="edge119" class="edge">
<title>src/client/domain/Catalog/catalogSearchReducer.ts&#45;&gt;src/client/domain/Catalog/catalogSearchEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1597.04,-481.28C1601.27,-481.28 1605.56,-481.28 1609.83,-481.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1609.51,-483.38 1615.51,-481.28 1609.51,-479.18 1609.51,-483.38"/>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge126" class="edge">
<title>src/client/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1591.95,-224.59C1599.49,-223.36 1607.27,-222.09 1614.9,-220.85"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1615.19,-222.93 1620.78,-219.9 1614.52,-218.79 1615.19,-222.93"/>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="edge125" class="edge">
<title>src/client/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1592.04,-234.53C1635.23,-235.54 1695.8,-236.95 1739.58,-237.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1739.31,-240.07 1745.35,-238.11 1739.4,-235.87 1739.31,-240.07"/>
</g>
<!-- src/client/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsEvents.ts -->
<g id="edge129" class="edge">
<title>src/client/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1449.9,-311.71C1457.76,-310.94 1465.86,-310.15 1473.81,-309.38"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1473.91,-311.48 1479.68,-308.81 1473.51,-307.3 1473.91,-311.48"/>
</g>
<!-- src/client/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/domain/GameSettings/GameSettings.ts -->
<g id="edge128" class="edge">
<title>src/client/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/domain/GameSettings/GameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1449.8,-319.62C1493.59,-320.91 1555.48,-321.65 1609.62,-318.28 1614.38,-317.99 1619.33,-317.57 1624.27,-317.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1624.36,-319.18 1630.1,-316.45 1623.91,-315 1624.36,-319.18"/>
</g>
<!-- src/client/application/subscribers/subscribeToDeckDraft.ts -->
<g id="node97" class="node">
<title>src/client/application/subscribers/subscribeToDeckDraft.ts</title>
<g id="a_node97"><a xlink:href="src/client/application/subscribers/subscribeToDeckDraft.ts" xlink:title="subscribeToDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="898,-2011.53 783,-2011.53 783,-1993.03 898,-1993.03 898,-2011.53"/>
<text text-anchor="start" x="791" y="-1998.98" font-family="Helvetica,sans-Serif" font-size="9.00">subscribeToDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge107" class="edge">
<title>src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M841.44,-2011.68C841.52,-2088.17 852.91,-2611.06 1146.25,-2814.28 1205.98,-2855.66 1247.56,-2851.87 1309.75,-2814.28 1359.6,-2784.16 1380.24,-2712.01 1387.39,-2678.26"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1389.37,-2679.09 1388.47,-2672.8 1385.25,-2678.27 1389.37,-2679.09"/>
</g>
<!-- src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/application/commands/saveDeckDraft/saveDeckDraft.ts -->
<g id="edge106" class="edge">
<title>src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/application/commands/saveDeckDraft/saveDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M844.56,-1992.98C867.3,-1923.79 1010.02,-1489.64 1040.78,-1396.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1042.72,-1396.87 1042.6,-1390.52 1038.73,-1395.56 1042.72,-1396.87"/>
</g>
<!-- src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="edge108" class="edge">
<title>src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M841.81,-1992.58C845.31,-1884.64 878.71,-907.26 949,-626.28 1002.09,-414.04 968.93,-303.42 1146.25,-175.28 1260.18,-92.95 1332.19,-105.65 1462.12,-159.28 1490.12,-170.83 1513.12,-198.59 1525.81,-216.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1523.79,-217.4 1528.9,-221.19 1527.27,-215.04 1523.79,-217.4"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilderCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge123" class="edge">
<title>src/client/domain/DeckBuilder/DeckBuilderCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1803.42,-248.99C1815.33,-268.71 1841.57,-315.21 1852.12,-358.28 1856.41,-375.79 1850.53,-505.02 1860.12,-520.28 1861.84,-523.02 1864.03,-525.44 1866.49,-527.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1864.95,-529.05 1871.05,-530.82 1867.39,-525.63 1864.95,-529.05"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx -->
<g id="node101" class="node">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx</title>
<g id="a_node101"><a xlink:href="src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx" xlink:title="SignInButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="506,-4137.53 424,-4137.53 424,-4119.03 506,-4119.03 506,-4137.53"/>
<text text-anchor="start" x="432" y="-4124.98" font-family="Helvetica,sans-Serif" font-size="9.00">SignInButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx -->
<g id="node102" class="node">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx</title>
<g id="a_node102"><a xlink:href="src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx" xlink:title="useSignInForm.tsx">
<polygon fill="#6cbaff" stroke="black" points="683.38,-4137.53 591.62,-4137.53 591.62,-4119.03 683.38,-4119.03 683.38,-4137.53"/>
<text text-anchor="start" x="599.62" y="-4124.98" font-family="Helvetica,sans-Serif" font-size="9.00">useSignInForm.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx -->
<g id="edge130" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M506.41,-4128.28C529.12,-4128.28 557.78,-4128.28 582.41,-4128.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="582.25,-4130.38 588.25,-4128.28 582.25,-4126.18 582.25,-4130.38"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx -->
<g id="node103" class="node">
<title>src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx</title>
<g id="a_node103"><a xlink:href="src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx" xlink:title="ShiningButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="884.12,-4107.53 796.88,-4107.53 796.88,-4089.03 884.12,-4089.03 884.12,-4107.53"/>
<text text-anchor="start" x="804.88" y="-4094.98" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx -->
<g id="edge131" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M480.89,-4118.55C499.18,-4107.24 531.54,-4089.24 562.25,-4082.28 639.69,-4064.73 732.23,-4076.38 788.4,-4086.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="787.83,-4088.96 794.12,-4088.03 788.63,-4084.83 787.83,-4088.96"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge134" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M683.77,-4125.19C700.34,-4126.59 717.71,-4131.53 728.75,-4144.28 735.15,-4151.68 733.65,-4310.52 734.38,-4320.28 760.88,-4679.1 823.15,-5114.7 836.82,-5208.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="834.72,-5208.31 837.67,-5213.94 838.88,-5207.7 834.72,-5208.31"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge135" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M643.57,-4118.54C659.9,-4086.52 711.13,-3981.25 728.75,-3887.28 731.09,-3874.8 727.15,-3440.72 734.38,-3430.28 748.6,-3409.74 774.75,-3399.49 797.41,-3394.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="797.78,-3396.44 803.25,-3393.19 796.95,-3392.32 797.78,-3396.44"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css -->
<g id="node133" class="node">
<title>src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css</title>
<g id="a_node133"><a xlink:href="src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css" xlink:title="ShiningButton.css">
<polygon fill="#6cbaff" stroke="black" points="1092.38,-4107.53 1002.88,-4107.53 1002.88,-4089.03 1092.38,-4089.03 1092.38,-4107.53"/>
<text text-anchor="start" x="1010.88" y="-4094.98" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningButton.css</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css -->
<g id="edge189" class="edge">
<title>src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M884.5,-4098.28C916.31,-4098.28 959.83,-4098.28 993.77,-4098.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="993.59,-4100.38 999.59,-4098.28 993.59,-4096.18 993.59,-4100.38"/>
</g>
<!-- src/client/infrastructure/lib/utils.ts -->
<g id="node134" class="node">
<title>src/client/infrastructure/lib/utils.ts</title>
<g id="a_node134"><a xlink:href="src/client/infrastructure/lib/utils.ts" xlink:title="utils.ts">
<polygon fill="#6cbaff" stroke="black" points="1074.62,-5235.53 1020.62,-5235.53 1020.62,-5217.03 1074.62,-5217.03 1074.62,-5235.53"/>
<text text-anchor="start" x="1034.88" y="-5222.98" font-family="Helvetica,sans-Serif" font-size="9.00">utils.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts -->
<g id="edge190" class="edge">
<title>src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M884.57,-4106.02C904.17,-4111.83 925.81,-4121.9 938.62,-4139.28 946.93,-4150.54 1031.77,-5065.48 1044.92,-5207.77"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1042.82,-5207.86 1045.46,-5213.64 1047,-5207.48 1042.82,-5207.86"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx -->
<g id="edge132" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M598.72,-4152.41C574.12,-4147.94 541.78,-4142.06 515.19,-4137.22"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="515.66,-4135.18 509.38,-4136.17 514.91,-4139.31 515.66,-4135.18"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge133" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M676.31,-4159.28C708.7,-4159.28 755.56,-4159.28 790.77,-4159.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="790.43,-4161.38 796.43,-4159.28 790.43,-4157.18 790.43,-4161.38"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts -->
<g id="edge192" class="edge">
<title>src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M881.56,-4166.2C901.9,-4171.86 925.11,-4182.05 938.62,-4200.28 1001.21,-4284.67 1039.8,-5076.27 1045.8,-5207.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1043.7,-5207.79 1046.07,-5213.69 1047.89,-5207.6 1043.7,-5207.79"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css -->
<g id="node135" class="node">
<title>src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css</title>
<g id="a_node135"><a xlink:href="src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css" xlink:title="ShiningCard.css">
<polygon fill="#6cbaff" stroke="black" points="1089.38,-4168.53 1005.88,-4168.53 1005.88,-4150.03 1089.38,-4150.03 1089.38,-4168.53"/>
<text text-anchor="start" x="1013.88" y="-4155.98" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningCard.css</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css -->
<g id="edge191" class="edge">
<title>src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M881.54,-4159.28C914.53,-4159.28 961.49,-4159.28 996.9,-4159.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="996.61,-4161.38 1002.61,-4159.28 996.61,-4157.18 996.61,-4161.38"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge136" class="edge">
<title>src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M493.18,-4815.03C511.34,-4822.21 535.28,-4833.02 554.25,-4846.28 558.37,-4849.16 558.03,-4851.54 562.25,-4854.28 628.73,-4897.47 673.31,-4861.62 728.75,-4918.28 812.01,-5003.37 833.17,-5155.46 838.11,-5207.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="836,-5207.95 838.61,-5213.75 840.19,-5207.59 836,-5207.95"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge137" class="edge">
<title>src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M493.79,-4795.69C514.54,-4786.88 541.44,-4771.63 554.25,-4748.28 563.07,-4732.21 550.18,-3442.08 562.25,-3428.28 569.93,-3419.5 721.96,-3401.96 797.56,-3393.73"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="797.63,-3395.84 803.37,-3393.1 797.18,-3391.66 797.63,-3395.84"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge138" class="edge">
<title>src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M485.53,-4875.86C532.13,-4899.45 651.67,-4964.34 728.75,-5045.28 777.96,-5096.96 816.1,-5174.05 831.76,-5208.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="829.81,-5209.3 834.17,-5213.92 833.64,-5207.59 829.81,-5209.3"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge139" class="edge">
<title>src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M514.39,-4865.36C529.48,-4862.49 544.66,-4856.42 554.25,-4844.28 566.93,-4828.24 558.76,-4128.43 562.25,-4108.28 600.98,-3884.55 688.01,-3848.66 728.75,-3625.28 730.69,-3614.62 728.13,-3439.14 734.38,-3430.28 748.77,-3409.85 774.92,-3399.6 797.54,-3394.46"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="797.9,-3396.53 803.36,-3393.28 797.06,-3392.42 797.9,-3396.53"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge166" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M683.98,-4321.91C701,-4327.95 718.64,-4337.69 728.75,-4353.28 742.53,-4374.54 720.08,-5244.36 734.38,-5265.28 818.62,-5388.56 897.3,-5389.28 1046.62,-5389.28 1046.62,-5389.28 1046.62,-5389.28 1675.75,-5389.28 1701.35,-5389.28 1715.6,-5396.04 1731.88,-5376.28 1896.43,-5176.51 1900.55,-877.05 1900.62,-560.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1902.72,-560.79 1900.63,-554.79 1898.52,-560.79 1902.72,-560.79"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge167" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M644.26,-4302.8C657.6,-4281.6 692.63,-4229.91 734.38,-4200.28 752.19,-4187.64 774.42,-4178.23 793.85,-4171.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="794.34,-4173.69 799.39,-4169.83 793.04,-4169.69 794.34,-4173.69"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge183" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M705.21,-4367.88C714.34,-4364.3 722.66,-4359 728.75,-4351.28 739.82,-4337.25 724.26,-3079.02 734.38,-3064.28 746.35,-3046.84 766.94,-3036.81 786.73,-3031.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="787.24,-3033.09 792.51,-3029.54 786.18,-3029.03 787.24,-3033.09"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="edge184" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M705.2,-4367.87C714.33,-4364.29 722.65,-4359 728.75,-4351.28 736.4,-4341.61 726.18,-3472.5 734.38,-3463.28 749.25,-3446.56 773.75,-3442.8 795.36,-3443.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="794.96,-3445.54 801.07,-3443.78 795.2,-3441.34 794.96,-3445.54"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx -->
<g id="node126" class="node">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx</title>
<g id="a_node126"><a xlink:href="src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx" xlink:title="useGameSettingsByGameId.tsx">
<polygon fill="#6cbaff" stroke="black" points="913,-3546.53 768,-3546.53 768,-3528.03 913,-3528.03 913,-3546.53"/>
<text text-anchor="start" x="776" y="-3533.98" font-family="Helvetica,sans-Serif" font-size="9.00">useGameSettingsByGameId.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx -->
<g id="edge185" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M705.19,-4367.86C714.32,-4364.28 722.64,-4358.99 728.75,-4351.28 741.54,-4335.12 724.69,-3627.48 734.38,-3609.28 749.31,-3581.21 780.62,-3561.96 805.2,-3550.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="805.87,-3552.48 810.49,-3548.12 804.15,-3548.65 805.87,-3552.48"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge198" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M896.88,-3083.57C912.51,-3080.54 928.11,-3074.33 938.62,-3062.28 956.08,-3042.28 931,-2962.79 949,-2943.28 1059.26,-2823.76 1184.8,-2986.35 1309.75,-2882.28 1373.5,-2829.19 1387,-2721.43 1389.86,-2678.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1391.94,-2678.85 1390.18,-2672.74 1387.75,-2678.62 1391.94,-2678.85"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/commands/loadCatalogCards/loadCatalogCards.ts -->
<g id="edge194" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/commands/loadCatalogCards/loadCatalogCards.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M896.99,-3084.18C912.82,-3081.22 928.5,-3074.89 938.62,-3062.28 956.1,-3040.52 933.49,-1076.48 949,-1053.28 958.35,-1039.29 973.5,-1030.06 989.1,-1023.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="989.47,-1026.08 994.42,-1022.09 988.06,-1022.12 989.47,-1026.08"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/getCatalogError/getCatalogError.ts -->
<g id="edge195" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/getCatalogError/getCatalogError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M896.76,-3083.99C912.58,-3081.02 928.31,-3074.73 938.62,-3062.28 959.33,-3037.28 928.3,-2795.29 949,-2770.28 1004,-2703.84 1084.03,-2794.37 1138.25,-2727.28 1149.83,-2712.96 1136.68,-2080.02 1146.25,-2064.28 1153.92,-2051.67 1166.91,-2042.71 1180.05,-2036.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.77,-2038.43 1185.43,-2034.11 1179.09,-2034.58 1180.77,-2038.43"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/getFilteredCards/getFilteredCards.ts -->
<g id="edge196" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/getFilteredCards/getFilteredCards.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M896.92,-3083.86C912.65,-3080.87 928.29,-3074.6 938.62,-3062.28 954.14,-3043.79 935.01,-2864.95 949,-2845.28 1001.54,-2771.42 1087.02,-2844.05 1138.25,-2769.28 1158.47,-2739.76 1127.66,-2155.85 1146.25,-2125.28 1153.92,-2112.67 1166.92,-2103.71 1180.06,-2097.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.77,-2099.43 1185.44,-2095.11 1179.1,-2095.58 1180.77,-2099.43"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/isCatalogLoading/isCatalogLoading.ts -->
<g id="edge197" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/isCatalogLoading/isCatalogLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M896.79,-3083.76C912.52,-3080.76 928.19,-3074.51 938.62,-3062.28 961.89,-3035 928.89,-2930.96 949,-2901.28 1001.25,-2824.18 1087.68,-2889.5 1138.25,-2811.28 1161.24,-2775.72 1124.1,-2466.37 1146.25,-2430.28 1153.97,-2417.7 1166.98,-2408.75 1180.12,-2402.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.83,-2404.47 1185.49,-2400.15 1179.15,-2400.62 1180.83,-2404.47"/>
</g>
<!-- src/client/infrastructure/hooks/useDebounce/useDebounce.ts -->
<g id="node138" class="node">
<title>src/client/infrastructure/hooks/useDebounce/useDebounce.ts</title>
<g id="a_node138"><a xlink:href="src/client/infrastructure/hooks/useDebounce/useDebounce.ts" xlink:title="useDebounce.ts">
<polygon fill="#6cbaff" stroke="black" points="1089,-3154.53 1006.25,-3154.53 1006.25,-3136.03 1089,-3136.03 1089,-3154.53"/>
<text text-anchor="start" x="1014.25" y="-3141.98" font-family="Helvetica,sans-Serif" font-size="9.00">useDebounce.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/hooks/useDebounce/index.ts&#45;&gt;src/client/infrastructure/hooks/useDebounce/useDebounce.ts -->
<g id="edge199" class="edge">
<title>src/client/infrastructure/hooks/useDebounce/index.ts&#45;&gt;src/client/infrastructure/hooks/useDebounce/useDebounce.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M867.9,-3145.28C900.87,-3145.28 957.9,-3145.28 998.67,-3145.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1004.83,-3147.38 998.83,-3145.28 1004.83,-3143.18 1004.83,-3147.38"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/addCardToDeck/addCardToDeck.ts -->
<g id="edge200" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/addCardToDeck/addCardToDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M847.23,-3013.83C865.14,-2983.65 920.03,-2886.32 938.62,-2797.28 943.75,-2772.74 935.06,-1013.12 949,-992.28 959.14,-977.11 976.1,-967.54 993.06,-961.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="993.41,-963.6 998.47,-959.76 992.12,-959.61 993.41,-963.6"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge215" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M846.46,-3013.81C859.52,-2989.98 897.44,-2927.55 949,-2901.28 1020.75,-2864.73 1245.46,-2915.76 1309.75,-2867.28 1371.5,-2820.71 1386.14,-2720.14 1389.58,-2678.7"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1391.67,-2678.87 1390,-2672.74 1387.48,-2678.57 1391.67,-2678.87"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalog.ts -->
<g id="edge201" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalog.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M847.52,-3013.61C865.34,-2984.43 917.84,-2894.13 938.62,-2811.28 956.72,-2739.15 941.02,-2718.22 949,-2644.28 1008.22,-2095.69 1071.73,-1965.04 1138.25,-1417.28 1140.67,-1397.35 1137.4,-1345.3 1146.25,-1327.28 1158.01,-1303.33 1181.57,-1283.47 1199.5,-1270.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1200.49,-1272.78 1204.29,-1267.67 1198.14,-1269.3 1200.49,-1272.78"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/hideCardDetails/hideCardDetails.ts -->
<g id="edge202" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/hideCardDetails/hideCardDetails.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M842.79,-3013.63C852.73,-2938.92 917.16,-2445.02 938.62,-2041.28 939.62,-2022.5 938.53,-702.9 949,-687.28 959.16,-672.12 976.12,-662.56 993.07,-656.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="993.43,-658.62 998.49,-654.77 992.13,-654.62 993.43,-658.62"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/search/search.ts -->
<g id="edge204" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/search/search.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M847.5,-3013.61C865.25,-2984.41 917.6,-2894.08 938.62,-2811.28 942.82,-2794.77 937.02,-2670.4 949,-2658.28 978.66,-2628.28 1109.52,-2674.17 1138.25,-2643.28 1149.5,-2631.19 1141.14,-1469.99 1146.25,-1454.28 1158.21,-1417.53 1187.84,-1382.65 1206.6,-1363.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1207.88,-1364.89 1210.6,-1359.14 1204.89,-1361.94 1207.88,-1364.89"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts -->
<g id="edge203" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M847.22,-3013.83C865.13,-2983.65 920.01,-2886.32 938.62,-2797.28 943.24,-2775.23 936.47,-1194 949,-1175.28 957.8,-1162.13 971.72,-1153.19 986.31,-1147.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="986.77,-1149.19 991.64,-1145.1 985.28,-1145.26 986.77,-1149.19"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/showCardDetails/showCardDetails.ts -->
<g id="edge205" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/showCardDetails/showCardDetails.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M842.79,-3013.63C852.72,-2938.92 917.08,-2445.02 938.62,-2041.28 939.58,-2023.35 939,-763.2 949,-748.28 958.76,-733.72 974.8,-724.32 991.09,-718.25"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="991.71,-720.25 996.72,-716.34 990.37,-716.28 991.71,-720.25"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts -->
<g id="edge206" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M847.23,-3013.83C865.15,-2983.65 920.05,-2886.33 938.62,-2797.28 944.09,-2771.08 934.13,-892.53 949,-870.28 957.79,-857.13 971.71,-848.19 986.31,-842.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="986.76,-844.18 991.63,-840.09 985.27,-840.25 986.76,-844.18"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts -->
<g id="edge207" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M847.23,-3013.83C865.15,-2983.65 920.04,-2886.33 938.62,-2797.28 943.92,-2771.91 934.59,-952.83 949,-931.28 957.79,-918.13 971.71,-909.19 986.31,-903.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="986.76,-905.18 991.63,-901.09 985.27,-901.26 986.76,-905.18"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge216" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M885.55,-3030.58C905.26,-3036.28 926.7,-3046.39 938.62,-3064.28 956.06,-3090.43 926.72,-5305.11 949,-5327.28 1008.61,-5386.62 1066.52,-5371.2 1138.25,-5327.28 1633.17,-5024.24 1577.49,-4723.7 1731.88,-4164.28 1838.7,-3777.19 1837.88,-2762.59 1852.12,-2361.28 1853.03,-2335.76 1850.37,-545.89 1860.12,-522.28 1864.22,-512.37 1871.99,-503.57 1879.63,-496.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1880.93,-498.34 1884.19,-492.88 1878.24,-495.12 1880.93,-498.34"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getActiveFilters/getActiveFilters.ts -->
<g id="edge208" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getActiveFilters/getActiveFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M847.47,-3013.6C865.15,-2984.38 917.32,-2894 938.62,-2811.28 942.49,-2796.28 938.07,-2683.26 949,-2672.28 978.76,-2642.38 1109.49,-2688.15 1138.25,-2657.28 1156.41,-2637.79 1132.46,-1721.07 1146.25,-1698.28 1153.89,-1685.65 1166.88,-1676.69 1180.03,-1670.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.75,-1672.41 1185.41,-1668.09 1179.07,-1668.56 1180.75,-1672.41"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getCardDetails/getCardDetails.ts -->
<g id="edge209" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getCardDetails/getCardDetails.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M847.44,-3013.59C865.03,-2984.35 916.98,-2893.91 938.62,-2811.28 945.69,-2784.32 929.25,-2705.95 949,-2686.28 978.89,-2656.51 1109.49,-2702.14 1138.25,-2671.28 1155.52,-2652.75 1133.13,-1780.95 1146.25,-1759.28 1153.9,-1746.65 1166.89,-1737.69 1180.03,-1731.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.75,-1733.42 1185.41,-1729.09 1179.07,-1729.57 1180.75,-1733.42"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getCardsInDeck/getCardsInDeck.ts -->
<g id="edge210" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getCardsInDeck/getCardsInDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M847.4,-3013.58C864.88,-2984.31 916.55,-2893.8 938.62,-2811.28 945.03,-2787.35 931.36,-2717.67 949,-2700.28 979.04,-2670.66 1109.48,-2716.14 1138.25,-2685.28 1154.64,-2667.71 1133.8,-1840.83 1146.25,-1820.28 1153.9,-1807.66 1166.89,-1798.69 1180.03,-1792.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.75,-1794.42 1185.41,-1790.1 1179.07,-1790.57 1180.75,-1794.42"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts -->
<g id="edge211" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getDeckBuilderView/getDeckBuilderView.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M847.35,-3013.57C864.69,-2984.26 916,-2893.65 938.62,-2811.28 944.37,-2790.38 933.46,-2729.4 949,-2714.28 979.24,-2684.86 1109.46,-2730.12 1138.25,-2699.28 1152.6,-2683.91 1135.34,-1960.26 1146.25,-1942.28 1153.91,-1929.66 1166.9,-1920.7 1180.04,-1914.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.76,-1916.42 1185.42,-1912.1 1179.08,-1912.57 1180.76,-1916.42"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getDeckName/getDeckName.ts -->
<g id="edge212" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getDeckName/getDeckName.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M847.28,-3013.55C864.43,-2984.19 915.27,-2893.45 938.62,-2811.28 943.71,-2793.4 935.56,-2741.12 949,-2728.28 979.5,-2699.13 1109.45,-2744.11 1138.25,-2713.28 1151.72,-2698.87 1136.01,-2020.14 1146.25,-2003.28 1153.91,-1990.66 1166.91,-1981.7 1180.05,-1975.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.77,-1977.43 1185.43,-1973.1 1179.09,-1973.58 1180.77,-1977.43"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getSearchTerm/getSearchTerm.ts -->
<g id="edge213" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getSearchTerm/getSearchTerm.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M846.48,-3013.59C866.48,-2974.79 940.19,-2832.46 949,-2826.28 1018.67,-2777.42 1081.99,-2861.12 1138.25,-2797.28 1149.49,-2784.52 1137.32,-2505.75 1146.25,-2491.28 1154.01,-2478.72 1167.02,-2469.77 1180.16,-2463.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.87,-2465.49 1185.53,-2461.17 1179.19,-2461.65 1180.87,-2465.49"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts -->
<g id="edge214" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getTotalCardsInDeck/getTotalCardsInDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M844.74,-3013.53C854.91,-2984.62 889.08,-2898.9 949,-2859.28 1020.29,-2812.15 1082.47,-2890.02 1138.25,-2825.28 1158.06,-2802.29 1130.27,-2578.08 1146.25,-2552.28 1154.03,-2539.73 1167.05,-2530.79 1180.18,-2524.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.89,-2526.51 1185.55,-2522.18 1179.21,-2522.66 1180.89,-2526.51"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="edge217" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M853.5,-3013.56C873.89,-2996.41 915.38,-2958.76 938.62,-2918.28 946.49,-2904.58 937.29,-2894.89 949,-2884.28 1012.56,-2826.66 1065.99,-2892.52 1138.25,-2846.28 1247.16,-2776.59 1269.31,-2734.09 1309.75,-2611.28 1321.7,-2575 1307.74,-1273.14 1317.75,-1236.28 1414.07,-881.51 1622.46,-871.24 1731.88,-520.28 1753.37,-451.32 1727.66,-428.43 1744.88,-358.28 1754.17,-320.4 1774.12,-279.11 1785.97,-256.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1787.69,-257.74 1788.66,-251.46 1783.98,-255.77 1787.69,-257.74"/>
</g>
<!-- src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge225" class="edge">
<title>src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M923.75,-3318.62C929.37,-3315.4 934.45,-3311.35 938.62,-3306.28 963.29,-3276.33 923.21,-2986.26 949,-2957.28 1002.87,-2896.76 1247.69,-2956.37 1309.75,-2904.28 1379.65,-2845.61 1389.48,-2724.98 1390.61,-2678.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1392.71,-2678.96 1390.69,-2672.93 1388.51,-2678.9 1392.71,-2678.96"/>
</g>
<!-- src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts -->
<g id="edge223" class="edge">
<title>src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M923.89,-3318.74C929.48,-3315.49 934.51,-3311.4 938.62,-3306.28 956.63,-3283.86 933.03,-1260.19 949,-1236.28 957.79,-1223.13 971.7,-1214.18 986.3,-1208.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="986.76,-1210.18 991.62,-1206.09 985.27,-1206.25 986.76,-1210.18"/>
</g>
<!-- src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/queries/getAvailableFilters/getAvailableFilters.ts -->
<g id="edge224" class="edge">
<title>src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/queries/getAvailableFilters/getAvailableFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M923.8,-3318.66C929.41,-3315.43 934.47,-3311.37 938.62,-3306.28 956.12,-3284.84 936.82,-2833.13 949,-2808.28 996.62,-2711.17 1091.26,-2752.7 1138.25,-2655.28 1150.53,-2629.81 1131.61,-1661.48 1146.25,-1637.28 1153.89,-1624.65 1166.88,-1615.69 1180.02,-1609.43"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.74,-1611.41 1185.4,-1607.09 1179.07,-1607.56 1180.74,-1611.41"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge164" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M682.08,-4535.26C699.67,-4541.25 718.25,-4551.11 728.75,-4567.28 740.29,-4585.06 721.57,-5313.39 734.38,-5330.28 821.39,-5445.1 902.56,-5417.28 1046.62,-5417.28 1046.62,-5417.28 1046.62,-5417.28 1675.75,-5417.28 1992.28,-5417.28 1822.63,-5042.43 1852.12,-4727.28 1893.67,-4283.33 1900.13,-843.12 1900.6,-560.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1902.7,-561.03 1900.61,-555.03 1898.5,-561.03 1902.7,-561.03"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge165" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M649.82,-4516.73C670.16,-4498.76 712.3,-4457.6 728.75,-4412.28 732.77,-4401.21 727.6,-4209.92 734.38,-4200.28 747.4,-4181.76 770.09,-4171.6 791.06,-4166.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="791.32,-4168.14 796.66,-4164.69 790.34,-4164.05 791.32,-4168.14"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge173" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M700.67,-4583.51C711.56,-4579.97 721.66,-4574.27 728.75,-4565.28 741.66,-4548.91 722.58,-3081.47 734.38,-3064.28 746.35,-3046.83 766.93,-3036.81 786.73,-3031.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="787.24,-3033.09 792.51,-3029.53 786.17,-3029.03 787.24,-3033.09"/>
</g>
<!-- src/client/infrastructure/store/store.ts&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge245" class="edge">
<title>src/client/infrastructure/store/store.ts&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M867.72,-3622.93C890.85,-3615.81 923.2,-3601.75 938.62,-3576.28 955.63,-3548.19 926.95,-3009.61 949,-2985.28 977.43,-2953.91 1096.27,-2968.78 1138.25,-2963.28 1214.63,-2953.28 1251.65,-2988.85 1309.75,-2938.28 1349.99,-2903.26 1379.1,-2735.2 1387.87,-2678.79"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1389.94,-2679.13 1388.77,-2672.88 1385.79,-2678.5 1389.94,-2679.13"/>
</g>
<!-- src/client/infrastructure/store/store.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="edge244" class="edge">
<title>src/client/infrastructure/store/store.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M867.69,-3622.92C890.8,-3615.77 923.14,-3601.71 938.62,-3576.28 964.7,-3533.47 921.62,-3167.27 949,-3125.28 1045.71,-2976.97 1171.25,-3066.58 1309.75,-2956.28 1400.24,-2884.22 1371.52,-2811.76 1470.12,-2751.28 1473.48,-2749.22 1477.1,-2747.48 1480.85,-2746.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1481.33,-2748.06 1486.35,-2744.16 1480,-2744.08 1481.33,-2748.06"/>
</g>
<!-- src/client/infrastructure/store/store.ts&#45;&gt;src/client/application/subscribers/subscribeToDeckDraft.ts -->
<g id="edge246" class="edge">
<title>src/client/infrastructure/store/store.ts&#45;&gt;src/client/application/subscribers/subscribeToDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M813.25,-3625.61C783.61,-3619.85 739.4,-3605.45 731.56,-3570.28 726.8,-3548.89 730.04,-2803.14 731.56,-2781.28 752.85,-2475.3 820.64,-2106.58 836.95,-2020.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="839.01,-2021.16 838.07,-2014.88 834.88,-2020.38 839.01,-2021.16"/>
</g>
<!-- src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts -->
<g id="node144" class="node">
<title>src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts</title>
<g id="a_node144"><a xlink:href="src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts" xlink:title="browserDeckDraftService.ts">
<polygon fill="#6cbaff" stroke="black" points="1112.25,-3642.53 983,-3642.53 983,-3624.03 1112.25,-3624.03 1112.25,-3642.53"/>
<text text-anchor="start" x="991" y="-3629.98" font-family="Helvetica,sans-Serif" font-size="9.00">browserDeckDraftService.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/store/store.ts&#45;&gt;src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts -->
<g id="edge247" class="edge">
<title>src/client/infrastructure/store/store.ts&#45;&gt;src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M867.9,-3629.8C894.64,-3630.32 937.22,-3631.15 974.11,-3631.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="973.8,-3633.96 979.84,-3631.98 973.88,-3629.76 973.8,-3633.96"/>
</g>
<!-- src/client/infrastructure/services/location/browserLocationService.ts -->
<g id="node145" class="node">
<title>src/client/infrastructure/services/location/browserLocationService.ts</title>
<g id="a_node145"><a xlink:href="src/client/infrastructure/services/location/browserLocationService.ts" xlink:title="browserLocationService.ts">
<polygon fill="#6cbaff" stroke="black" points="1109.62,-3703.53 985.62,-3703.53 985.62,-3685.03 1109.62,-3685.03 1109.62,-3703.53"/>
<text text-anchor="start" x="993.62" y="-3690.98" font-family="Helvetica,sans-Serif" font-size="9.00">browserLocationService.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/store/store.ts&#45;&gt;src/client/infrastructure/services/location/browserLocationService.ts -->
<g id="edge248" class="edge">
<title>src/client/infrastructure/store/store.ts&#45;&gt;src/client/infrastructure/services/location/browserLocationService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M862.56,-3638.86C883.97,-3648.33 918.29,-3662.74 949,-3672.28 961.34,-3676.11 974.74,-3679.57 987.55,-3682.56"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="987.04,-3684.59 993.35,-3683.87 987.97,-3680.5 987.04,-3684.59"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge171" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M638.9,-4718.92C641.85,-4785.45 662.11,-5176.33 734.38,-5265.28 827.15,-5379.48 899.49,-5375.28 1046.62,-5375.28 1046.62,-5375.28 1046.62,-5375.28 1225,-5375.28 1709.85,-5375.28 1643.28,-4885.97 1731.88,-4409.28 1827.55,-3894.47 1824.6,-3760.18 1852.12,-3237.28 1853.12,-3218.45 1852.94,-539.71 1860.12,-522.28 1864.21,-512.37 1871.98,-503.57 1879.62,-496.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1880.93,-498.33 1884.18,-492.87 1878.23,-495.11 1880.93,-498.33"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx -->
<g id="node125" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx</title>
<g id="a_node125"><a xlink:href="src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx" xlink:title="DeckBuildingSkeletonCard.tsx">
<polygon fill="#6cbaff" stroke="black" points="535.25,-4474.53 394.75,-4474.53 394.75,-4456.03 535.25,-4456.03 535.25,-4474.53"/>
<text text-anchor="start" x="402.75" y="-4461.98" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuildingSkeletonCard.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge172" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M535.46,-4457.65C542.69,-4454.22 549.22,-4449.57 554.25,-4443.28 565.89,-4428.75 548.93,-4121.29 562.25,-4108.28 588.72,-4082.43 700.2,-4084.74 728.75,-4108.28 738.88,-4116.63 725.16,-4127.93 734.38,-4137.28 748.9,-4152.02 770.6,-4158.12 790.51,-4160.29"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="790.33,-4162.38 796.48,-4160.78 790.68,-4158.2 790.33,-4162.38"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/store/appStore.ts -->
<g id="edge222" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/store/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M913.17,-3539.49C922.89,-3536.66 931.83,-3531.9 938.62,-3524.28 959.08,-3501.35 928.53,-2994.2 949,-2971.28 977.26,-2939.64 1096.25,-2952.24 1138.25,-2946.28 1214.58,-2935.45 1251.28,-2970.52 1309.75,-2920.28 1383.56,-2856.86 1390.96,-2727.31 1391.03,-2679"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1393.13,-2678.98 1390.98,-2673 1388.93,-2679.02 1393.13,-2678.98"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/commands/loadGameSettings/loadGameSettings.ts -->
<g id="edge218" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/commands/loadGameSettings/loadGameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M913.27,-3539.57C922.97,-3536.74 931.89,-3531.95 938.62,-3524.28 960.72,-3499.14 930.41,-1142.12 949,-1114.28 958.26,-1100.42 973.21,-1091.23 988.66,-1085.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="988.97,-1087.27 993.9,-1083.26 987.55,-1083.31 988.97,-1087.27"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/getGameSettings/getGameSettings.ts -->
<g id="edge219" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/getGameSettings/getGameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M913.2,-3539.5C922.91,-3536.68 931.84,-3531.91 938.62,-3524.28 963.36,-3496.46 925.55,-2883.19 949,-2854.28 1003.33,-2787.29 1083.96,-2878.31 1138.25,-2811.28 1157.97,-2786.93 1129.95,-2274.04 1146.25,-2247.28 1153.93,-2234.67 1166.93,-2225.72 1180.07,-2219.46"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.79,-2221.44 1185.45,-2217.12 1179.11,-2217.59 1180.79,-2221.44"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/getGameSettingsError/getGameSettingsError.ts -->
<g id="edge220" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/getGameSettingsError/getGameSettingsError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M913.18,-3539.49C922.9,-3536.67 931.84,-3531.9 938.62,-3524.28 960.8,-3499.38 929.78,-2951.52 949,-2924.28 1000.8,-2850.88 1086.54,-2926.75 1138.25,-2853.28 1155.68,-2828.52 1130.49,-2334.14 1146.25,-2308.28 1153.93,-2295.68 1166.93,-2286.72 1180.07,-2280.46"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1180.79,-2282.44 1185.45,-2278.12 1179.11,-2278.59 1180.79,-2282.44"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts -->
<g id="edge221" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M913.18,-3539.49C922.89,-3536.66 931.83,-3531.9 938.62,-3524.28 959.59,-3500.76 928.57,-2981.26 949,-2957.28 1004.42,-2892.23 1076.06,-2981.9 1138.25,-2923.28 1187.82,-2876.56 1214.31,-2656.17 1221.18,-2590.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1223.25,-2591.06 1221.77,-2584.87 1219.07,-2590.63 1223.25,-2591.06"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/Match/Match.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge186" class="edge">
<title>src/client/infrastructure/components/app/Gaming/Match/Match.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M491.38,-5093.93C556.99,-5118.87 732.24,-5185.5 806.02,-5213.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="804.9,-5215.37 811.26,-5215.54 806.4,-5211.45 804.9,-5215.37"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge187" class="edge">
<title>src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M508.49,-5032.95C562.58,-5046.54 657.53,-5074.71 728.75,-5119.28 769.24,-5144.62 807.16,-5186.71 826.37,-5209.82"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="824.59,-5210.96 830.01,-5214.27 827.84,-5208.3 824.59,-5210.96"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge188" class="edge">
<title>src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M514.89,-5022.3C529.83,-5019.4 544.79,-5013.33 554.25,-5001.28 567.52,-4984.38 548.67,-3470.93 562.25,-3454.28 610.06,-3395.69 655.23,-3436.95 728.75,-3419.28 751.63,-3413.78 776.97,-3406.97 797.64,-3401.23"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="798.06,-3403.29 803.27,-3399.65 796.93,-3399.25 798.06,-3403.29"/>
</g>
<!-- src/client/infrastructure/components/debug/JsonObjectViewer/JsonObjectViewer.tsx -->
<g id="node130" class="node">
<title>src/client/infrastructure/components/debug/JsonObjectViewer/JsonObjectViewer.tsx</title>
<g id="a_node130"><a xlink:href="src/client/infrastructure/components/debug/JsonObjectViewer/JsonObjectViewer.tsx" xlink:title="JsonObjectViewer.tsx">
<polygon fill="#6cbaff" stroke="black" points="516.5,-3903.53 413.5,-3903.53 413.5,-3885.03 516.5,-3885.03 516.5,-3903.53"/>
<text text-anchor="start" x="421.5" y="-3890.98" font-family="Helvetica,sans-Serif" font-size="9.00">JsonObjectViewer.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/debug/MatchMakingConsoleEvents/MatchMakingConsoleEvents.tsx -->
<g id="node132" class="node">
<title>src/client/infrastructure/components/debug/MatchMakingConsoleEvents/MatchMakingConsoleEvents.tsx</title>
<g id="a_node132"><a xlink:href="src/client/infrastructure/components/debug/MatchMakingConsoleEvents/MatchMakingConsoleEvents.tsx" xlink:title="MatchMakingConsoleEvents.tsx">
<polygon fill="#6cbaff" stroke="black" points="538.25,-4025.53 391.75,-4025.53 391.75,-4007.03 538.25,-4007.03 538.25,-4025.53"/>
<text text-anchor="start" x="399.75" y="-4012.98" font-family="Helvetica,sans-Serif" font-size="9.00">MatchMakingConsoleEvents.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/SkeletonHelper/SkeletonHelper.tsx -->
<g id="node136" class="node">
<title>src/client/infrastructure/components/ui/SkeletonHelper/SkeletonHelper.tsx</title>
<g id="a_node136"><a xlink:href="src/client/infrastructure/components/ui/SkeletonHelper/SkeletonHelper.tsx" xlink:title="SkeletonHelper.tsx">
<polygon fill="#6cbaff" stroke="black" points="886.75,-4229.53 794.25,-4229.53 794.25,-4211.03 886.75,-4211.03 886.75,-4229.53"/>
<text text-anchor="start" x="802.25" y="-4216.98" font-family="Helvetica,sans-Serif" font-size="9.00">SkeletonHelper.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts -->
<g id="edge193" class="edge">
<title>src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M843.6,-4290.94C863.31,-4381.74 1015.88,-5084.63 1042.63,-5207.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1040.55,-5208.2 1043.88,-5213.62 1044.66,-5207.31 1040.55,-5208.2"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckById/useDeckById.tsx -->
<g id="node139" class="node">
<title>src/client/infrastructure/hooks/useDeckById/useDeckById.tsx</title>
<g id="a_node139"><a xlink:href="src/client/infrastructure/hooks/useDeckById/useDeckById.tsx" xlink:title="useDeckById.tsx">
<polygon fill="#6cbaff" stroke="black" points="882.25,-3215.53 798.75,-3215.53 798.75,-3197.03 882.25,-3197.03 882.25,-3215.53"/>
<text text-anchor="start" x="806.75" y="-3202.98" font-family="Helvetica,sans-Serif" font-size="9.00">useDeckById.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/lib/groupByDataProperty.ts -->
<g id="node141" class="node">
<title>src/client/infrastructure/lib/groupByDataProperty.ts</title>
<g id="a_node141"><a xlink:href="src/client/infrastructure/lib/groupByDataProperty.ts" xlink:title="groupByDataProperty.ts">
<polygon fill="#6cbaff" stroke="black" points="1104,-5297.53 991.25,-5297.53 991.25,-5279.03 1104,-5279.03 1104,-5297.53"/>
<text text-anchor="start" x="999.25" y="-5284.98" font-family="Helvetica,sans-Serif" font-size="9.00">groupByDataProperty.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/lib/groupByDataProperty.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge228" class="edge">
<title>src/client/infrastructure/lib/groupByDataProperty.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1104.49,-5288.37C1116.92,-5285.85 1129.1,-5281.01 1138.25,-5272.28 1504.84,-4922.36 883.83,-4160.28 1390.62,-4160.28 1390.62,-4160.28 1390.62,-4160.28 1675.75,-4160.28 1864.12,-4160.28 1844.24,-962.48 1852.12,-774.28 1853.3,-746.29 1849.07,-548.02 1860.12,-522.28 1864.36,-512.43 1872.14,-503.64 1879.77,-496.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1881.07,-498.4 1884.3,-492.93 1878.36,-495.19 1881.07,-498.4"/>
</g>
<!-- src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts -->
<g id="edge241" class="edge">
<title>src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/application/services/DeckDraftService/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1055.9,-3623.63C1107.38,-3555.16 1415.79,-3141.3 1462.12,-2993.28 1466.14,-2980.44 1461.41,-2761.52 1470.12,-2751.28 1473.14,-2747.74 1476.81,-2744.98 1480.86,-2742.85"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1481.5,-2744.86 1486.25,-2740.64 1479.9,-2740.98 1481.5,-2744.86"/>
</g>
<!-- src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/domain/DeckBuilder/DeckDraft.ts -->
<g id="edge242" class="edge">
<title>src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/domain/DeckBuilder/DeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1050.34,-3623.72C1059.15,-3575.04 1100.61,-3349.88 1146.25,-3168.28 1209.14,-2918.06 1272.6,-2866.59 1309.75,-2611.28 1312.08,-2595.28 1309.56,-297.22 1317.75,-283.28 1325.11,-270.74 1337.88,-261.73 1350.69,-255.42"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1351.26,-257.46 1355.86,-253.07 1349.53,-253.63 1351.26,-257.46"/>
</g>
<!-- src/client/infrastructure/services/location/browserLocationService.ts&#45;&gt;src/client/application/services/LocationService.ts -->
<g id="edge243" class="edge">
<title>src/client/infrastructure/services/location/browserLocationService.ts&#45;&gt;src/client/application/services/LocationService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1109.82,-3686.1C1119.94,-3682.89 1129.85,-3678.44 1138.25,-3672.28 1365.81,-3505.47 1359.84,-3389.24 1462.12,-3126.28 1507.17,-3010.48 1528.18,-2861.02 1534.42,-2809.6"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1536.48,-2810.13 1535.1,-2803.92 1532.31,-2809.63 1536.48,-2810.13"/>
</g>
</g>
</svg>
    <script>
      var gMode = new Mode();

var title2ElementMap = (function makeElementMap() {
  /** @type {NodeListOf<SVGGElement>} */
  var nodes = document.querySelectorAll(".node");
  /** @type {NodeListOf<SVGGElement>} */
  var edges = document.querySelectorAll(".edge");
  return new Title2ElementMap(edges, nodes);
})();

function getHoverHandler(pTitle2ElementMap) {
  /** @type {string} */
  var currentHighlightedTitle = "";

  /** @param {MouseEvent} pMouseEvent */
  return function hoverHighlightHandler(pMouseEvent) {
    var closestNodeOrEdge = pMouseEvent.target.closest(".edge, .node");
    var closestTitleText = getTitleText(closestNodeOrEdge);

    if (
      currentHighlightedTitle !== closestTitleText &&
      gMode.get() === gMode.HOVER
    ) {
      resetNodesAndEdges();
      addHighlight(closestNodeOrEdge);
      pTitle2ElementMap.get(closestTitleText).forEach(addHighlight);
      currentHighlightedTitle = closestTitleText;
    }
  };
}

function getSelectHandler(pTitle2ElementMap) {
  /** @type {string} */
  var currentHighlightedTitle = "";

  /** @param {MouseEvent} pMouseEvent */
  return function selectHighlightHandler(pMouseEvent) {
    pMouseEvent.preventDefault();

    var closestNodeOrEdge = pMouseEvent.target.closest(".edge, .node");
    var closestTitleText = getTitleText(closestNodeOrEdge);

    if (closestNodeOrEdge) {
      gMode.setToSelect();
    } else {
      gMode.setToHover();
    }
    if (currentHighlightedTitle !== closestTitleText) {
      resetNodesAndEdges();
      addHighlight(closestNodeOrEdge);
      pTitle2ElementMap.get(closestTitleText).forEach(addHighlight);
      currentHighlightedTitle = closestTitleText;
    }
  };
}
function Mode() {
  var HOVER = 1;
  var SELECT = 2;

  function setToHover() {
    this._mode = HOVER;
  }
  function setToSelect() {
    this._mode = SELECT;
  }

  /**
   * @returns {number}
   */
  function get() {
    return this._mode || HOVER;
  }

  return {
    HOVER: HOVER,
    SELECT: SELECT,
    setToHover: setToHover,
    setToSelect: setToSelect,
    get: get,
  };
}

/**
 *
 * @param {SVGGelement[]} pEdges
 * @param {SVGGElement[]} pNodes
 * @return {{get: (pTitleText:string) => SVGGElement[]}}
 */
function Title2ElementMap(pEdges, pNodes) {
  /* {{[key: string]: SVGGElement[]}} */
  var elementMap = buildMap(pEdges, pNodes);

  /**
   * @param {NodeListOf<SVGGElement>} pEdges
   * @param {NodeListOf<SVGGElement>} pNodes
   * @return {{[key: string]: SVGGElement[]}}
   */
  function buildMap(pEdges, pNodes) {
    var title2NodeMap = buildTitle2NodeMap(pNodes);

    return nodeListToArray(pEdges).reduce(addEdgeToMap(title2NodeMap), {});
  }
  /**
   * @param {NodeListOf<SVGGElement>} pNodes
   * @return {{[key: string]: SVGGElement}}
   */
  function buildTitle2NodeMap(pNodes) {
    return nodeListToArray(pNodes).reduce(addNodeToMap, {});
  }

  function addNodeToMap(pMap, pNode) {
    var titleText = getTitleText(pNode);

    if (titleText) {
      pMap[titleText] = pNode;
    }
    return pMap;
  }

  function addEdgeToMap(pNodeMap) {
    return function (pEdgeMap, pEdge) {
      /** @type {string} */
      var titleText = getTitleText(pEdge);

      if (titleText) {
        var edge = pryEdgeFromTitle(titleText);

        pEdgeMap[titleText] = [pNodeMap[edge.from], pNodeMap[edge.to]];
        (pEdgeMap[edge.from] || (pEdgeMap[edge.from] = [])).push(pEdge);
        (pEdgeMap[edge.to] || (pEdgeMap[edge.to] = [])).push(pEdge);
      }
      return pEdgeMap;
    };
  }

  /**
   *
   * @param {string} pString
   * @return {{from?: string; to?:string;}}
   */
  function pryEdgeFromTitle(pString) {
    var nodeNames = pString.split(/\s*->\s*/);

    return {
      from: nodeNames.shift(),
      to: nodeNames.shift(),
    };
  }
  /**
   *
   * @param {string} pTitleText
   * @return {SVGGElement[]}
   */
  function get(pTitleText) {
    return (pTitleText && elementMap[pTitleText]) || [];
  }
  return {
    get: get,
  };
}

/**
 * @param {SVGGElement} pGElement
 * @return {string?}
 */
function getTitleText(pGElement) {
  /** @type {SVGTitleElement} */
  var title = pGElement && pGElement.querySelector("title");
  /** @type {string} */
  var titleText = title && title.textContent;

  if (titleText) {
    titleText = titleText.trim();
  }
  return titleText;
}

/**
 * @param {NodeListOf<Element>} pNodeList
 * @return {Element[]}
 */
function nodeListToArray(pNodeList) {
  var lReturnValue = [];

  pNodeList.forEach(function (pElement) {
    lReturnValue.push(pElement);
  });

  return lReturnValue;
}

function resetNodesAndEdges() {
  nodeListToArray(document.querySelectorAll(".current")).forEach(
    removeHighlight,
  );
}

/**
 * @param {SVGGElement} pGElement
 */
function removeHighlight(pGElement) {
  if (pGElement && pGElement.classList) {
    pGElement.classList.remove("current");
  }
}

/**
 * @param {SVGGElement} pGroup
 */
function addHighlight(pGroup) {
  if (pGroup && pGroup.classList) {
    pGroup.classList.add("current");
  }
}

var gHints = {
  HIDDEN: 1,
  SHOWN: 2,
  state: 1, // === HIDDEN
  show: function () {
    document.getElementById("hints").removeAttribute("style");
    gHints.state = gHints.SHOWN;
  },
  hide: function () {
    document.getElementById("hints").style = "display:none";
    gHints.state = gHints.HIDDEN;
  },
  toggle: function () {
    if ((gHints.state || gHints.HIDDEN) === gHints.HIDDEN) {
      gHints.show();
    } else {
      gHints.hide();
    }
  },
};

/** @param {KeyboardEvent} pKeyboardEvent */
function keyboardEventHandler(pKeyboardEvent) {
  if (pKeyboardEvent.key === "Escape") {
    resetNodesAndEdges();
    gMode.setToHover();
    gHints.hide();
  }
  if (pKeyboardEvent.key === "F1") {
    pKeyboardEvent.preventDefault();
    gHints.toggle();
  }
}

document.addEventListener("contextmenu", getSelectHandler(title2ElementMap));
document.addEventListener("mouseover", getHoverHandler(title2ElementMap));
document.addEventListener("keydown", keyboardEventHandler);
document.getElementById("close-hints").addEventListener("click", gHints.hide);
document.getElementById("button_help").addEventListener("click", gHints.toggle);
document.querySelector("svg").insertAdjacentHTML(
  "afterbegin",
  `<linearGradient id="edgeGradient">
      <stop offset="0%" stop-color="fuchsia"/>
      <stop offset="100%" stop-color="purple"/>
   </linearGradient>
  `,
);

// Add a small increment to the last value of the path to make gradients on
// horizontal paths work. Without them all browsers I tested with (firefox,
// chrome) do not render the gradient, but instead make the line transparent
// (or the color of the background, I haven't looked into it that deeply,
// but for the hack it doesn't matter which).
function skewLineABit(lDrawingInstructions) {
  var lLastValue = lDrawingInstructions.match(/(\d+\.?\d*)$/)[0];
  // Smaller values than .001 _should_ work as well, but don't in all
  // cases. Even this value is so small that it is not visible to the
  // human eye (tested with the two I have at my disposal).
  var lIncrement = 0.001;
  var lNewLastValue = parseFloat(lLastValue) + lIncrement;

  return lDrawingInstructions.replace(lLastValue, lNewLastValue);
}

nodeListToArray(document.querySelectorAll("path"))
  .filter(function (pElement) {
    return pElement.parentElement.classList.contains("edge");
  })
  .forEach(function (pElement) {
    pElement.attributes.d.value = skewLineABit(pElement.attributes.d.value);
  });

    </script>
  </body>
</html>
