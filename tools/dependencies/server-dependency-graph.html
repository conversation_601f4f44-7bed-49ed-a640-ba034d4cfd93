<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="utf-8" />
    <title>dependency graph</title>
    <style>
      .node:active path,
.node:hover path,
.node.current path,
.node:active polygon,
.node:hover polygon,
.node.current polygon {
  stroke: fuchsia;
  stroke-width: 2;
}

.edge:active path,
.edge:hover path,
.edge.current path,
.edge:active ellipse,
.edge:hover ellipse,
.edge.current ellipse {
  stroke: url(#edgeGradient);
  stroke-width: 3;
  stroke-opacity: 1;
}

.edge:active polygon,
.edge:hover polygon,
.edge.current polygon {
  stroke: fuchsia;
  stroke-width: 3;
  fill: fuchsia;
  stroke-opacity: 1;
  fill-opacity: 1;
}

.edge:active text,
.edge:hover text {
  fill: fuchsia;
}

.cluster path {
  stroke-width: 3;
}

.cluster:active path,
.cluster:hover path {
  fill: #ffff0011;
}

div.hint {
  background-color: #000000aa;
  color: white;
  font-family: Arial, Helvetica, sans-serif;
  border-radius: 1rem;
  position: fixed;
  top: calc(50% - 4em);
  right: calc(50% - 10em);
  border: none;
  padding: 1em 3em 1em 1em;
}

.hint button {
  position: absolute;
  font-weight: bolder;
  right: 0.6em;
  top: 0.6em;
  color: inherit;
  background-color: inherit;
  border: 1px solid currentColor;
  border-radius: 1em;
  margin-left: 0.6em;
}

.hint a {
  color: inherit;
}

#button_help {
  color: white;
  background-color: #00000011;
  border-radius: 1em;
  position: fixed;
  top: 1em;
  right: 1em;
  font-size: 24pt;
  font-weight: bolder;
  width: 2em;
  height: 2em;
  border: none;
}

#button_help:hover {
  cursor: pointer;
  background-color: #00000077;
}

@media print {
  #button_help {
    display: none;
  }

  div.hint {
    display: none;
  }
}

    </style>
  </head>
  <body>
    <button id="button_help">?</button>
    <div id="hints" class="hint" style="display: none">
      <button id="close-hints">x</button>
      <span id="hint-text"></span>
      <ul>
        <li><b>Hover</b> - highlight</li>
        <li><b>Right-click</b> - pin highlight</li>
        <li><b>ESC</b> - clear</li>
      </ul>
    </div>
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.0 (20241103.1931)
 -->
<!-- Title: dependency&#45;cruiser output Pages: 1 -->
<svg width="904pt" height="2715pt"
 viewBox="0.00 0.00 903.50 2715.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 2711)">
<title>dependency&#45;cruiser output</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-2711 899.5,-2711 899.5,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster_app</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M99.38,-8C99.38,-8 432.25,-8 432.25,-8 438.25,-8 444.25,-14 444.25,-20 444.25,-20 444.25,-822 444.25,-822 444.25,-828 438.25,-834 432.25,-834 432.25,-834 99.38,-834 99.38,-834 93.38,-834 87.38,-828 87.38,-822 87.38,-822 87.38,-20 87.38,-20 87.38,-14 93.38,-8 99.38,-8"/>
<text text-anchor="middle" x="265.81" y="-821.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">app</text>
</g>
<g id="clust2" class="cluster">
<title>cluster_app/[locale]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M107.38,-16C107.38,-16 239.62,-16 239.62,-16 245.62,-16 251.62,-22 251.62,-28 251.62,-28 251.62,-738 251.62,-738 251.62,-744 245.62,-750 239.62,-750 239.62,-750 107.38,-750 107.38,-750 101.38,-750 95.38,-744 95.38,-738 95.38,-738 95.38,-28 95.38,-28 95.38,-22 101.38,-16 107.38,-16"/>
<text text-anchor="middle" x="173.5" y="-737.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[locale]</text>
</g>
<g id="clust3" class="cluster">
<title>cluster_app/[locale]/(connected)</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M115.38,-24C115.38,-24 231.62,-24 231.62,-24 237.62,-24 243.62,-30 243.62,-36 243.62,-36 243.62,-502 243.62,-502 243.62,-508 237.62,-514 231.62,-514 231.62,-514 115.38,-514 115.38,-514 109.38,-514 103.38,-508 103.38,-502 103.38,-502 103.38,-36 103.38,-36 103.38,-30 109.38,-24 115.38,-24"/>
<text text-anchor="middle" x="173.5" y="-501.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">(connected)</text>
</g>
<g id="clust4" class="cluster">
<title>cluster_app/[locale]/(connected)/games</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M123.38,-32C123.38,-32 223.62,-32 223.62,-32 229.62,-32 235.62,-38 235.62,-44 235.62,-44 235.62,-379 235.62,-379 235.62,-385 229.62,-391 223.62,-391 223.62,-391 123.38,-391 123.38,-391 117.38,-391 111.38,-385 111.38,-379 111.38,-379 111.38,-44 111.38,-44 111.38,-38 117.38,-32 123.38,-32"/>
<text text-anchor="middle" x="173.5" y="-378.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">games</text>
</g>
<g id="clust5" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M131.38,-40C131.38,-40 215.62,-40 215.62,-40 221.62,-40 227.62,-46 227.62,-52 227.62,-52 227.62,-326 227.62,-326 227.62,-332 221.62,-338 215.62,-338 215.62,-338 131.38,-338 131.38,-338 125.38,-338 119.38,-332 119.38,-326 119.38,-326 119.38,-52 119.38,-52 119.38,-46 125.38,-40 131.38,-40"/>
<text text-anchor="middle" x="173.5" y="-325.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[gameId]</text>
</g>
<g id="clust6" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/deck&#45;builder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M139.38,-48C139.38,-48 207.62,-48 207.62,-48 213.62,-48 219.62,-54 219.62,-60 219.62,-60 219.62,-212 219.62,-212 219.62,-218 213.62,-224 207.62,-224 207.62,-224 139.38,-224 139.38,-224 133.38,-224 127.38,-218 127.38,-212 127.38,-212 127.38,-60 127.38,-60 127.38,-54 133.38,-48 139.38,-48"/>
<text text-anchor="middle" x="173.5" y="-211.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">deck&#45;builder</text>
</g>
<g id="clust7" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M147.38,-56C147.38,-56 199.62,-56 199.62,-56 205.62,-56 211.62,-62 211.62,-68 211.62,-68 211.62,-128 211.62,-128 211.62,-134 205.62,-140 199.62,-140 199.62,-140 147.38,-140 147.38,-140 141.38,-140 135.38,-134 135.38,-128 135.38,-128 135.38,-68 135.38,-68 135.38,-62 141.38,-56 147.38,-56"/>
<text text-anchor="middle" x="173.5" y="-127.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[deckId]</text>
</g>
<g id="clust8" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/play</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M150.5,-232C150.5,-232 196.5,-232 196.5,-232 202.5,-232 208.5,-238 208.5,-244 208.5,-244 208.5,-273 208.5,-273 208.5,-279 202.5,-285 196.5,-285 196.5,-285 150.5,-285 150.5,-285 144.5,-285 138.5,-279 138.5,-273 138.5,-273 138.5,-244 138.5,-244 138.5,-238 144.5,-232 150.5,-232"/>
<text text-anchor="middle" x="173.5" y="-272.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">play</text>
</g>
<g id="clust9" class="cluster">
<title>cluster_app/[locale]/(connected)/matches</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M142.5,-399C142.5,-399 204.5,-399 204.5,-399 210.5,-399 216.5,-405 216.5,-411 216.5,-411 216.5,-475 216.5,-475 216.5,-481 210.5,-487 204.5,-487 204.5,-487 142.5,-487 142.5,-487 136.5,-487 130.5,-481 130.5,-475 130.5,-475 130.5,-411 130.5,-411 130.5,-405 136.5,-399 142.5,-399"/>
<text text-anchor="middle" x="173.5" y="-474.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">matches</text>
</g>
<g id="clust10" class="cluster">
<title>cluster_app/[locale]/(connected)/matches/[matchId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M150.5,-407C150.5,-407 196.5,-407 196.5,-407 202.5,-407 208.5,-413 208.5,-419 208.5,-419 208.5,-448 208.5,-448 208.5,-454 202.5,-460 196.5,-460 196.5,-460 150.5,-460 150.5,-460 144.5,-460 138.5,-454 138.5,-448 138.5,-448 138.5,-419 138.5,-419 138.5,-413 144.5,-407 150.5,-407"/>
<text text-anchor="middle" x="173.5" y="-447.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[matchId]</text>
</g>
<g id="clust11" class="cluster">
<title>cluster_app/[locale]/(not&#45;connected)</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M142.5,-522C142.5,-522 204.5,-522 204.5,-522 210.5,-522 216.5,-528 216.5,-534 216.5,-534 216.5,-624 216.5,-624 216.5,-630 210.5,-636 204.5,-636 204.5,-636 142.5,-636 142.5,-636 136.5,-636 130.5,-630 130.5,-624 130.5,-624 130.5,-534 130.5,-534 130.5,-528 136.5,-522 142.5,-522"/>
<text text-anchor="middle" x="173.5" y="-623.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">(not&#45;connected)</text>
</g>
<g id="clust12" class="cluster">
<title>cluster_app/[locale]/(not&#45;connected)/signin</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M150.5,-530C150.5,-530 196.5,-530 196.5,-530 202.5,-530 208.5,-536 208.5,-542 208.5,-542 208.5,-571 208.5,-571 208.5,-577 202.5,-583 196.5,-583 196.5,-583 150.5,-583 150.5,-583 144.5,-583 138.5,-577 138.5,-571 138.5,-571 138.5,-542 138.5,-542 138.5,-536 144.5,-530 150.5,-530"/>
<text text-anchor="middle" x="173.5" y="-570.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">signin</text>
</g>
<g id="clust13" class="cluster">
<title>cluster_app/[locale]/access&#45;denied</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M146.5,-644C146.5,-644 201.5,-644 201.5,-644 207.5,-644 213.5,-650 213.5,-656 213.5,-656 213.5,-685 213.5,-685 213.5,-691 207.5,-697 201.5,-697 201.5,-697 146.5,-697 146.5,-697 140.5,-697 134.5,-691 134.5,-685 134.5,-685 134.5,-656 134.5,-656 134.5,-650 140.5,-644 146.5,-644"/>
<text text-anchor="middle" x="174" y="-684.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">access&#45;denied</text>
</g>
<g id="clust14" class="cluster">
<title>cluster_src</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M20,-842C20,-842 875.5,-842 875.5,-842 881.5,-842 887.5,-848 887.5,-854 887.5,-854 887.5,-2687 887.5,-2687 887.5,-2693 881.5,-2699 875.5,-2699 875.5,-2699 20,-2699 20,-2699 14,-2699 8,-2693 8,-2687 8,-2687 8,-854 8,-854 8,-848 14,-842 20,-842"/>
<text text-anchor="middle" x="447.75" y="-2686.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">src</text>
</g>
<g id="clust15" class="cluster">
<title>cluster_src/server</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M28,-850C28,-850 867.5,-850 867.5,-850 873.5,-850 879.5,-856 879.5,-862 879.5,-862 879.5,-2660 879.5,-2660 879.5,-2666 873.5,-2672 867.5,-2672 867.5,-2672 28,-2672 28,-2672 22,-2672 16,-2666 16,-2660 16,-2660 16,-862 16,-862 16,-856 22,-850 28,-850"/>
<text text-anchor="middle" x="447.75" y="-2659.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">server</text>
</g>
<g id="clust16" class="cluster">
<title>cluster_src/server/application</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M36,-908C36,-908 526.75,-908 526.75,-908 532.75,-908 538.75,-914 538.75,-920 538.75,-920 538.75,-1860 538.75,-1860 538.75,-1866 532.75,-1872 526.75,-1872 526.75,-1872 36,-1872 36,-1872 30,-1872 24,-1866 24,-1860 24,-1860 24,-920 24,-920 24,-914 30,-908 36,-908"/>
<text text-anchor="middle" x="281.38" y="-1859.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">application</text>
</g>
<g id="clust17" class="cluster">
<title>cluster_src/server/application/commands</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M44,-916C44,-916 518.75,-916 518.75,-916 524.75,-916 530.75,-922 530.75,-928 530.75,-928 530.75,-1524 530.75,-1524 530.75,-1530 524.75,-1536 518.75,-1536 518.75,-1536 44,-1536 44,-1536 38,-1536 32,-1530 32,-1524 32,-1524 32,-928 32,-928 32,-922 38,-916 44,-916"/>
<text text-anchor="middle" x="281.38" y="-1523.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">commands</text>
</g>
<g id="clust18" class="cluster">
<title>cluster_src/server/application/commands/Deck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M98.5,-924C98.5,-924 464.25,-924 464.25,-924 470.25,-924 476.25,-930 476.25,-936 476.25,-936 476.25,-1000 476.25,-1000 476.25,-1006 470.25,-1012 464.25,-1012 464.25,-1012 98.5,-1012 98.5,-1012 92.5,-1012 86.5,-1006 86.5,-1000 86.5,-1000 86.5,-936 86.5,-936 86.5,-930 92.5,-924 98.5,-924"/>
<text text-anchor="middle" x="281.38" y="-999.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Deck</text>
</g>
<g id="clust19" class="cluster">
<title>cluster_src/server/application/commands/Deck/SaveDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M106.5,-932C106.5,-932 456.25,-932 456.25,-932 462.25,-932 468.25,-938 468.25,-944 468.25,-944 468.25,-973 468.25,-973 468.25,-979 462.25,-985 456.25,-985 456.25,-985 106.5,-985 106.5,-985 100.5,-985 94.5,-979 94.5,-973 94.5,-973 94.5,-944 94.5,-944 94.5,-938 100.5,-932 106.5,-932"/>
<text text-anchor="middle" x="281.38" y="-972.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SaveDeck</text>
</g>
<g id="clust20" class="cluster">
<title>cluster_src/server/application/commands/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M94.38,-1020C94.38,-1020 468.38,-1020 468.38,-1020 474.38,-1020 480.38,-1026 480.38,-1032 480.38,-1032 480.38,-1096 480.38,-1096 480.38,-1102 474.38,-1108 468.38,-1108 468.38,-1108 94.38,-1108 94.38,-1108 88.38,-1108 82.38,-1102 82.38,-1096 82.38,-1096 82.38,-1032 82.38,-1032 82.38,-1026 88.38,-1020 94.38,-1020"/>
<text text-anchor="middle" x="281.38" y="-1095.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust21" class="cluster">
<title>cluster_src/server/application/commands/Match/LeaveMatch</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M102.38,-1028C102.38,-1028 460.38,-1028 460.38,-1028 466.38,-1028 472.38,-1034 472.38,-1040 472.38,-1040 472.38,-1069 472.38,-1069 472.38,-1075 466.38,-1081 460.38,-1081 460.38,-1081 102.38,-1081 102.38,-1081 96.38,-1081 90.38,-1075 90.38,-1069 90.38,-1069 90.38,-1040 90.38,-1040 90.38,-1034 96.38,-1028 102.38,-1028"/>
<text text-anchor="middle" x="281.38" y="-1068.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">LeaveMatch</text>
</g>
<g id="clust22" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M52,-1116C52,-1116 510.75,-1116 510.75,-1116 516.75,-1116 522.75,-1122 522.75,-1128 522.75,-1128 522.75,-1497 522.75,-1497 522.75,-1503 516.75,-1509 510.75,-1509 510.75,-1509 52,-1509 52,-1509 46,-1509 40,-1503 40,-1497 40,-1497 40,-1128 40,-1128 40,-1122 46,-1116 52,-1116"/>
<text text-anchor="middle" x="281.38" y="-1496.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchMaking</text>
</g>
<g id="clust23" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M60,-1124C60,-1124 502.75,-1124 502.75,-1124 508.75,-1124 514.75,-1130 514.75,-1136 514.75,-1136 514.75,-1165 514.75,-1165 514.75,-1171 508.75,-1177 502.75,-1177 502.75,-1177 60,-1177 60,-1177 54,-1177 48,-1171 48,-1165 48,-1165 48,-1136 48,-1136 48,-1130 54,-1124 60,-1124"/>
<text text-anchor="middle" x="281.38" y="-1164.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AddPlayerToMatchMakingQueue</text>
</g>
<g id="clust24" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/CancelMatchRegistration</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M75.75,-1185C75.75,-1185 487,-1185 487,-1185 493,-1185 499,-1191 499,-1197 499,-1197 499,-1226 499,-1226 499,-1232 493,-1238 487,-1238 487,-1238 75.75,-1238 75.75,-1238 69.75,-1238 63.75,-1232 63.75,-1226 63.75,-1226 63.75,-1197 63.75,-1197 63.75,-1191 69.75,-1185 75.75,-1185"/>
<text text-anchor="middle" x="281.38" y="-1225.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">CancelMatchRegistration</text>
</g>
<g id="clust25" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M67.88,-1246C67.88,-1246 494.88,-1246 494.88,-1246 500.88,-1246 506.88,-1252 506.88,-1258 506.88,-1258 506.88,-1287 506.88,-1287 506.88,-1293 500.88,-1299 494.88,-1299 494.88,-1299 67.88,-1299 67.88,-1299 61.88,-1299 55.88,-1293 55.88,-1287 55.88,-1287 55.88,-1258 55.88,-1258 55.88,-1252 61.88,-1246 67.88,-1246"/>
<text text-anchor="middle" x="281.38" y="-1286.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">CleanUpMatchMakingQueue</text>
</g>
<g id="clust26" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/MakeMatch</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M103.88,-1307C103.88,-1307 458.88,-1307 458.88,-1307 464.88,-1307 470.88,-1313 470.88,-1319 470.88,-1319 470.88,-1348 470.88,-1348 470.88,-1354 464.88,-1360 458.88,-1360 458.88,-1360 103.88,-1360 103.88,-1360 97.88,-1360 91.88,-1354 91.88,-1348 91.88,-1348 91.88,-1319 91.88,-1319 91.88,-1313 97.88,-1307 103.88,-1307"/>
<text text-anchor="middle" x="281.38" y="-1347.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MakeMatch</text>
</g>
<g id="clust27" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/UpdatePlayersStatus</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M84.38,-1368C84.38,-1368 478.38,-1368 478.38,-1368 484.38,-1368 490.38,-1374 490.38,-1380 490.38,-1380 490.38,-1409 490.38,-1409 490.38,-1415 484.38,-1421 478.38,-1421 478.38,-1421 84.38,-1421 84.38,-1421 78.38,-1421 72.38,-1415 72.38,-1409 72.38,-1409 72.38,-1380 72.38,-1380 72.38,-1374 78.38,-1368 84.38,-1368"/>
<text text-anchor="middle" x="281.38" y="-1408.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">UpdatePlayersStatus</text>
</g>
<g id="clust28" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M73.5,-1429C73.5,-1429 489.25,-1429 489.25,-1429 495.25,-1429 501.25,-1435 501.25,-1441 501.25,-1441 501.25,-1470 501.25,-1470 501.25,-1476 495.25,-1482 489.25,-1482 489.25,-1482 73.5,-1482 73.5,-1482 67.5,-1482 61.5,-1476 61.5,-1470 61.5,-1470 61.5,-1441 61.5,-1441 61.5,-1435 67.5,-1429 73.5,-1429"/>
<text text-anchor="middle" x="281.38" y="-1469.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">UpdateSinglePlayerStatus</text>
</g>
<g id="clust29" class="cluster">
<title>cluster_src/server/application/ports</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M333.88,-1544C333.88,-1544 476.88,-1544 476.88,-1544 482.88,-1544 488.88,-1550 488.88,-1556 488.88,-1556 488.88,-1833 488.88,-1833 488.88,-1839 482.88,-1845 476.88,-1845 476.88,-1845 333.88,-1845 333.88,-1845 327.88,-1845 321.88,-1839 321.88,-1833 321.88,-1833 321.88,-1556 321.88,-1556 321.88,-1550 327.88,-1544 333.88,-1544"/>
<text text-anchor="middle" x="405.38" y="-1832.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ports</text>
</g>
<g id="clust30" class="cluster">
<title>cluster_src/server/application/queries</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M93,-1606C93,-1606 254,-1606 254,-1606 260,-1606 266,-1612 266,-1618 266,-1618 266,-1771 266,-1771 266,-1777 260,-1783 254,-1783 254,-1783 93,-1783 93,-1783 87,-1783 81,-1777 81,-1771 81,-1771 81,-1618 81,-1618 81,-1612 87,-1606 93,-1606"/>
<text text-anchor="middle" x="173.5" y="-1770.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">queries</text>
</g>
<g id="clust31" class="cluster">
<title>cluster_src/server/domain</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M558.75,-1694C558.75,-1694 859.5,-1694 859.5,-1694 865.5,-1694 871.5,-1700 871.5,-1706 871.5,-1706 871.5,-2268 871.5,-2268 871.5,-2274 865.5,-2280 859.5,-2280 859.5,-2280 558.75,-2280 558.75,-2280 552.75,-2280 546.75,-2274 546.75,-2268 546.75,-2268 546.75,-1706 546.75,-1706 546.75,-1700 552.75,-1694 558.75,-1694"/>
<text text-anchor="middle" x="709.12" y="-2267.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">domain</text>
</g>
<g id="clust32" class="cluster">
<title>cluster_src/server/domain/AppUser</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M612.62,-2104C612.62,-2104 831.38,-2104 831.38,-2104 837.38,-2104 843.38,-2110 843.38,-2116 843.38,-2116 843.38,-2180 843.38,-2180 843.38,-2186 837.38,-2192 831.38,-2192 831.38,-2192 612.62,-2192 612.62,-2192 606.62,-2192 600.62,-2186 600.62,-2180 600.62,-2180 600.62,-2116 600.62,-2116 600.62,-2110 606.62,-2104 612.62,-2104"/>
<text text-anchor="middle" x="722" y="-2179.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AppUser</text>
</g>
<g id="clust33" class="cluster">
<title>cluster_src/server/domain/AppUser/valueObjects</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M762.88,-2112C762.88,-2112 823.38,-2112 823.38,-2112 829.38,-2112 835.38,-2118 835.38,-2124 835.38,-2124 835.38,-2153 835.38,-2153 835.38,-2159 829.38,-2165 823.38,-2165 823.38,-2165 762.88,-2165 762.88,-2165 756.88,-2165 750.88,-2159 750.88,-2153 750.88,-2153 750.88,-2124 750.88,-2124 750.88,-2118 756.88,-2112 762.88,-2112"/>
<text text-anchor="middle" x="793.12" y="-2152.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">valueObjects</text>
</g>
<g id="clust34" class="cluster">
<title>cluster_src/server/domain/Deck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M580.25,-1982C580.25,-1982 697.25,-1982 697.25,-1982 703.25,-1982 709.25,-1988 709.25,-1994 709.25,-1994 709.25,-2084 709.25,-2084 709.25,-2090 703.25,-2096 697.25,-2096 697.25,-2096 580.25,-2096 580.25,-2096 574.25,-2096 568.25,-2090 568.25,-2084 568.25,-2084 568.25,-1994 568.25,-1994 568.25,-1988 574.25,-1982 580.25,-1982"/>
<text text-anchor="middle" x="638.75" y="-2083.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Deck</text>
</g>
<g id="clust35" class="cluster">
<title>cluster_src/server/domain/Deck/errors</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M588.25,-1990C588.25,-1990 689.25,-1990 689.25,-1990 695.25,-1990 701.25,-1996 701.25,-2002 701.25,-2002 701.25,-2031 701.25,-2031 701.25,-2037 695.25,-2043 689.25,-2043 689.25,-2043 588.25,-2043 588.25,-2043 582.25,-2043 576.25,-2037 576.25,-2031 576.25,-2031 576.25,-2002 576.25,-2002 576.25,-1996 582.25,-1990 588.25,-1990"/>
<text text-anchor="middle" x="638.75" y="-2030.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">errors</text>
</g>
<g id="clust36" class="cluster">
<title>cluster_src/server/domain/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M566.75,-1702C566.75,-1702 710.75,-1702 710.75,-1702 716.75,-1702 722.75,-1708 722.75,-1714 722.75,-1714 722.75,-1835 722.75,-1835 722.75,-1841 716.75,-1847 710.75,-1847 710.75,-1847 566.75,-1847 566.75,-1847 560.75,-1847 554.75,-1841 554.75,-1835 554.75,-1835 554.75,-1714 554.75,-1714 554.75,-1708 560.75,-1702 566.75,-1702"/>
<text text-anchor="middle" x="638.75" y="-1834.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust37" class="cluster">
<title>cluster_src/server/domain/Match/errors</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M574.75,-1710C574.75,-1710 702.75,-1710 702.75,-1710 708.75,-1710 714.75,-1716 714.75,-1722 714.75,-1722 714.75,-1782 714.75,-1782 714.75,-1788 708.75,-1794 702.75,-1794 702.75,-1794 574.75,-1794 574.75,-1794 568.75,-1794 562.75,-1788 562.75,-1782 562.75,-1782 562.75,-1722 562.75,-1722 562.75,-1716 568.75,-1710 574.75,-1710"/>
<text text-anchor="middle" x="638.75" y="-1781.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">errors</text>
</g>
<g id="clust38" class="cluster">
<title>cluster_src/server/domain/MatchmakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M589,-2200C589,-2200 851.5,-2200 851.5,-2200 857.5,-2200 863.5,-2206 863.5,-2212 863.5,-2212 863.5,-2241 863.5,-2241 863.5,-2247 857.5,-2253 851.5,-2253 851.5,-2253 589,-2253 589,-2253 583,-2253 577,-2247 577,-2241 577,-2241 577,-2212 577,-2212 577,-2206 583,-2200 589,-2200"/>
<text text-anchor="middle" x="720.25" y="-2240.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchmakingQueue</text>
</g>
<g id="clust39" class="cluster">
<title>cluster_src/server/domain/User</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M567.5,-1855C567.5,-1855 710,-1855 710,-1855 716,-1855 722,-1861 722,-1867 722,-1867 722,-1962 722,-1962 722,-1968 716,-1974 710,-1974 710,-1974 567.5,-1974 567.5,-1974 561.5,-1974 555.5,-1968 555.5,-1962 555.5,-1962 555.5,-1867 555.5,-1867 555.5,-1861 561.5,-1855 567.5,-1855"/>
<text text-anchor="middle" x="638.75" y="-1961.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">User</text>
</g>
<g id="clust40" class="cluster">
<title>cluster_src/server/domain/User/errors</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M575.5,-1863C575.5,-1863 702,-1863 702,-1863 708,-1863 714,-1869 714,-1875 714,-1875 714,-1935 714,-1935 714,-1941 708,-1947 702,-1947 702,-1947 575.5,-1947 575.5,-1947 569.5,-1947 563.5,-1941 563.5,-1935 563.5,-1935 563.5,-1875 563.5,-1875 563.5,-1869 569.5,-1863 575.5,-1863"/>
<text text-anchor="middle" x="638.75" y="-1934.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">errors</text>
</g>
<g id="clust41" class="cluster">
<title>cluster_src/server/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M65.75,-1880C65.75,-1880 281.25,-1880 281.25,-1880 287.25,-1880 293.25,-1886 293.25,-1892 293.25,-1892 293.25,-2607 293.25,-2607 293.25,-2613 287.25,-2619 281.25,-2619 281.25,-2619 65.75,-2619 65.75,-2619 59.75,-2619 53.75,-2613 53.75,-2607 53.75,-2607 53.75,-1892 53.75,-1892 53.75,-1886 59.75,-1880 65.75,-1880"/>
<text text-anchor="middle" x="173.5" y="-2606.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust42" class="cluster">
<title>cluster_src/server/infrastructure/IdentityProvider</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M123.75,-2106C123.75,-2106 223.25,-2106 223.25,-2106 229.25,-2106 235.25,-2112 235.25,-2118 235.25,-2118 235.25,-2147 235.25,-2147 235.25,-2153 229.25,-2159 223.25,-2159 223.25,-2159 123.75,-2159 123.75,-2159 117.75,-2159 111.75,-2153 111.75,-2147 111.75,-2147 111.75,-2118 111.75,-2118 111.75,-2112 117.75,-2106 123.75,-2106"/>
<text text-anchor="middle" x="173.5" y="-2146.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">IdentityProvider</text>
</g>
<g id="clust43" class="cluster">
<title>cluster_src/server/infrastructure/gateways</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M94.38,-1888C94.38,-1888 252.62,-1888 252.62,-1888 258.62,-1888 264.62,-1894 264.62,-1900 264.62,-1900 264.62,-2086 264.62,-2086 264.62,-2092 258.62,-2098 252.62,-2098 252.62,-2098 94.38,-2098 94.38,-2098 88.38,-2098 82.38,-2092 82.38,-2086 82.38,-2086 82.38,-1900 82.38,-1900 82.38,-1894 88.38,-1888 94.38,-1888"/>
<text text-anchor="middle" x="173.5" y="-2085.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">gateways</text>
</g>
<g id="clust44" class="cluster">
<title>cluster_src/server/infrastructure/gateways/AuthenticationGateway</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M102.38,-2018C102.38,-2018 244.62,-2018 244.62,-2018 250.62,-2018 256.62,-2024 256.62,-2030 256.62,-2030 256.62,-2059 256.62,-2059 256.62,-2065 250.62,-2071 244.62,-2071 244.62,-2071 102.38,-2071 102.38,-2071 96.38,-2071 90.38,-2065 90.38,-2059 90.38,-2059 90.38,-2030 90.38,-2030 90.38,-2024 96.38,-2018 102.38,-2018"/>
<text text-anchor="middle" x="173.5" y="-2058.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AuthenticationGateway</text>
</g>
<g id="clust45" class="cluster">
<title>cluster_src/server/infrastructure/gateways/Context</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M133.5,-1896C133.5,-1896 213.5,-1896 213.5,-1896 219.5,-1896 225.5,-1902 225.5,-1908 225.5,-1908 225.5,-1937 225.5,-1937 225.5,-1943 219.5,-1949 213.5,-1949 213.5,-1949 133.5,-1949 133.5,-1949 127.5,-1949 121.5,-1943 121.5,-1937 121.5,-1937 121.5,-1908 121.5,-1908 121.5,-1902 127.5,-1896 133.5,-1896"/>
<text text-anchor="middle" x="173.5" y="-1936.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Context</text>
</g>
<g id="clust46" class="cluster">
<title>cluster_src/server/infrastructure/gateways/Time</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M129.75,-1957C129.75,-1957 217.25,-1957 217.25,-1957 223.25,-1957 229.25,-1963 229.25,-1969 229.25,-1969 229.25,-1998 229.25,-1998 229.25,-2004 223.25,-2010 217.25,-2010 217.25,-2010 129.75,-2010 129.75,-2010 123.75,-2010 117.75,-2004 117.75,-1998 117.75,-1998 117.75,-1969 117.75,-1969 117.75,-1963 123.75,-1957 129.75,-1957"/>
<text text-anchor="middle" x="173.5" y="-1997.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Time</text>
</g>
<g id="clust47" class="cluster">
<title>cluster_src/server/infrastructure/repositories</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M73.75,-2167C73.75,-2167 273.25,-2167 273.25,-2167 279.25,-2167 285.25,-2173 285.25,-2179 285.25,-2179 285.25,-2580 285.25,-2580 285.25,-2586 279.25,-2592 273.25,-2592 273.25,-2592 73.75,-2592 73.75,-2592 67.75,-2592 61.75,-2586 61.75,-2580 61.75,-2580 61.75,-2179 61.75,-2179 61.75,-2173 67.75,-2167 73.75,-2167"/>
<text text-anchor="middle" x="173.5" y="-2579.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">repositories</text>
</g>
<g id="clust48" class="cluster">
<title>cluster_src/server/infrastructure/repositories/AppUser</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M109.5,-2175C109.5,-2175 237.5,-2175 237.5,-2175 243.5,-2175 249.5,-2181 249.5,-2187 249.5,-2187 249.5,-2216 249.5,-2216 249.5,-2222 243.5,-2228 237.5,-2228 237.5,-2228 109.5,-2228 109.5,-2228 103.5,-2228 97.5,-2222 97.5,-2216 97.5,-2216 97.5,-2187 97.5,-2187 97.5,-2181 103.5,-2175 109.5,-2175"/>
<text text-anchor="middle" x="173.5" y="-2215.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AppUser</text>
</g>
<g id="clust49" class="cluster">
<title>cluster_src/server/infrastructure/repositories/Deck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M117,-2236C117,-2236 230,-2236 230,-2236 236,-2236 242,-2242 242,-2248 242,-2248 242,-2277 242,-2277 242,-2283 236,-2289 230,-2289 230,-2289 117,-2289 117,-2289 111,-2289 105,-2283 105,-2277 105,-2277 105,-2248 105,-2248 105,-2242 111,-2236 117,-2236"/>
<text text-anchor="middle" x="173.5" y="-2276.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Deck</text>
</g>
<g id="clust50" class="cluster">
<title>cluster_src/server/infrastructure/repositories/InMemory</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M81.75,-2297C81.75,-2297 265.25,-2297 265.25,-2297 271.25,-2297 277.25,-2303 277.25,-2309 277.25,-2309 277.25,-2431 277.25,-2431 277.25,-2437 271.25,-2443 265.25,-2443 265.25,-2443 81.75,-2443 81.75,-2443 75.75,-2443 69.75,-2437 69.75,-2431 69.75,-2431 69.75,-2309 69.75,-2309 69.75,-2303 75.75,-2297 81.75,-2297"/>
<text text-anchor="middle" x="173.5" y="-2430.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">InMemory</text>
</g>
<g id="clust51" class="cluster">
<title>cluster_src/server/infrastructure/repositories/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M115.12,-2451C115.12,-2451 231.88,-2451 231.88,-2451 237.88,-2451 243.88,-2457 243.88,-2463 243.88,-2463 243.88,-2492 243.88,-2492 243.88,-2498 237.88,-2504 231.88,-2504 231.88,-2504 115.12,-2504 115.12,-2504 109.12,-2504 103.12,-2498 103.12,-2492 103.12,-2492 103.12,-2463 103.12,-2463 103.12,-2457 109.12,-2451 115.12,-2451"/>
<text text-anchor="middle" x="173.5" y="-2491.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust52" class="cluster">
<title>cluster_src/server/infrastructure/repositories/MatchmakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M86.25,-2512C86.25,-2512 260.75,-2512 260.75,-2512 266.75,-2512 272.75,-2518 272.75,-2524 272.75,-2524 272.75,-2553 272.75,-2553 272.75,-2559 266.75,-2565 260.75,-2565 260.75,-2565 86.25,-2565 86.25,-2565 80.25,-2565 74.25,-2559 74.25,-2553 74.25,-2553 74.25,-2524 74.25,-2524 74.25,-2518 80.25,-2512 86.25,-2512"/>
<text text-anchor="middle" x="173.5" y="-2552.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchmakingQueue</text>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/loading.tsx -->
<g id="node1" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/loading.tsx</title>
<g id="a_node1"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/[deckId]/loading.tsx" xlink:title="loading.tsx">
<path fill="#ccffcc" stroke="black" d="M197.46,-82.25C197.46,-82.25 149.54,-82.25 149.54,-82.25 146.46,-82.25 143.38,-79.17 143.38,-76.08 143.38,-76.08 143.38,-69.92 143.38,-69.92 143.38,-66.83 146.46,-63.75 149.54,-63.75 149.54,-63.75 197.46,-63.75 197.46,-63.75 200.54,-63.75 203.62,-66.83 203.62,-69.92 203.62,-69.92 203.62,-76.08 203.62,-76.08 203.62,-79.17 200.54,-82.25 197.46,-82.25"/>
<text text-anchor="start" x="151.38" y="-69.7" font-family="Helvetica,sans-Serif" font-size="9.00">loading.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx -->
<g id="node2" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx</title>
<g id="a_node2"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/[deckId]/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M194.33,-113.25C194.33,-113.25 152.67,-113.25 152.67,-113.25 149.58,-113.25 146.5,-110.17 146.5,-107.08 146.5,-107.08 146.5,-100.92 146.5,-100.92 146.5,-97.83 149.58,-94.75 152.67,-94.75 152.67,-94.75 194.33,-94.75 194.33,-94.75 197.42,-94.75 200.5,-97.83 200.5,-100.92 200.5,-100.92 200.5,-107.08 200.5,-107.08 200.5,-110.17 197.42,-113.25 194.33,-113.25"/>
<text text-anchor="start" x="156.25" y="-100.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx -->
<g id="node3" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx</title>
<g id="a_node3"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/layout.tsx" xlink:title="layout.tsx">
<path fill="#ccffcc" stroke="black" d="M194.46,-166.25C194.46,-166.25 152.54,-166.25 152.54,-166.25 149.46,-166.25 146.38,-163.17 146.38,-160.08 146.38,-160.08 146.38,-153.92 146.38,-153.92 146.38,-150.83 149.46,-147.75 152.54,-147.75 152.54,-147.75 194.46,-147.75 194.46,-147.75 197.54,-147.75 200.62,-150.83 200.62,-153.92 200.62,-153.92 200.62,-160.08 200.62,-160.08 200.62,-163.17 197.54,-166.25 194.46,-166.25"/>
<text text-anchor="start" x="154.38" y="-153.7" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx -->
<g id="node4" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx</title>
<g id="a_node4"><a xlink:href="app/[locale]/(connected)/games/[gameId]/deck-builder/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M194.33,-197.25C194.33,-197.25 152.67,-197.25 152.67,-197.25 149.58,-197.25 146.5,-194.17 146.5,-191.08 146.5,-191.08 146.5,-184.92 146.5,-184.92 146.5,-181.83 149.58,-178.75 152.67,-178.75 152.67,-178.75 194.33,-178.75 194.33,-178.75 197.42,-178.75 200.5,-181.83 200.5,-184.92 200.5,-184.92 200.5,-191.08 200.5,-191.08 200.5,-194.17 197.42,-197.25 194.33,-197.25"/>
<text text-anchor="start" x="156.25" y="-184.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/page.tsx -->
<g id="node5" class="node">
<title>app/[locale]/(connected)/games/[gameId]/page.tsx</title>
<g id="a_node5"><a xlink:href="app/[locale]/(connected)/games/[gameId]/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M194.33,-311.25C194.33,-311.25 152.67,-311.25 152.67,-311.25 149.58,-311.25 146.5,-308.17 146.5,-305.08 146.5,-305.08 146.5,-298.92 146.5,-298.92 146.5,-295.83 149.58,-292.75 152.67,-292.75 152.67,-292.75 194.33,-292.75 194.33,-292.75 197.42,-292.75 200.5,-295.83 200.5,-298.92 200.5,-298.92 200.5,-305.08 200.5,-305.08 200.5,-308.17 197.42,-311.25 194.33,-311.25"/>
<text text-anchor="start" x="156.25" y="-298.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/play/page.tsx -->
<g id="node6" class="node">
<title>app/[locale]/(connected)/games/[gameId]/play/page.tsx</title>
<g id="a_node6"><a xlink:href="app/[locale]/(connected)/games/[gameId]/play/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M194.33,-258.25C194.33,-258.25 152.67,-258.25 152.67,-258.25 149.58,-258.25 146.5,-255.17 146.5,-252.08 146.5,-252.08 146.5,-245.92 146.5,-245.92 146.5,-242.83 149.58,-239.75 152.67,-239.75 152.67,-239.75 194.33,-239.75 194.33,-239.75 197.42,-239.75 200.5,-242.83 200.5,-245.92 200.5,-245.92 200.5,-252.08 200.5,-252.08 200.5,-255.17 197.42,-258.25 194.33,-258.25"/>
<text text-anchor="start" x="156.25" y="-245.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/page.tsx -->
<g id="node7" class="node">
<title>app/[locale]/(connected)/games/page.tsx</title>
<g id="a_node7"><a xlink:href="app/[locale]/(connected)/games/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M194.33,-364.25C194.33,-364.25 152.67,-364.25 152.67,-364.25 149.58,-364.25 146.5,-361.17 146.5,-358.08 146.5,-358.08 146.5,-351.92 146.5,-351.92 146.5,-348.83 149.58,-345.75 152.67,-345.75 152.67,-345.75 194.33,-345.75 194.33,-345.75 197.42,-345.75 200.5,-348.83 200.5,-351.92 200.5,-351.92 200.5,-358.08 200.5,-358.08 200.5,-361.17 197.42,-364.25 194.33,-364.25"/>
<text text-anchor="start" x="156.25" y="-351.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/matches/[matchId]/page.tsx -->
<g id="node8" class="node">
<title>app/[locale]/(connected)/matches/[matchId]/page.tsx</title>
<g id="a_node8"><a xlink:href="app/[locale]/(connected)/matches/[matchId]/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M194.33,-433.25C194.33,-433.25 152.67,-433.25 152.67,-433.25 149.58,-433.25 146.5,-430.17 146.5,-427.08 146.5,-427.08 146.5,-420.92 146.5,-420.92 146.5,-417.83 149.58,-414.75 152.67,-414.75 152.67,-414.75 194.33,-414.75 194.33,-414.75 197.42,-414.75 200.5,-417.83 200.5,-420.92 200.5,-420.92 200.5,-427.08 200.5,-427.08 200.5,-430.17 197.42,-433.25 194.33,-433.25"/>
<text text-anchor="start" x="156.25" y="-420.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(not&#45;connected)/layout.tsx -->
<g id="node9" class="node">
<title>app/[locale]/(not&#45;connected)/layout.tsx</title>
<g id="a_node9"><a xlink:href="app/[locale]/(not-connected)/layout.tsx" xlink:title="layout.tsx">
<path fill="#ccffcc" stroke="black" d="M194.46,-609.25C194.46,-609.25 152.54,-609.25 152.54,-609.25 149.46,-609.25 146.38,-606.17 146.38,-603.08 146.38,-603.08 146.38,-596.92 146.38,-596.92 146.38,-593.83 149.46,-590.75 152.54,-590.75 152.54,-590.75 194.46,-590.75 194.46,-590.75 197.54,-590.75 200.62,-593.83 200.62,-596.92 200.62,-596.92 200.62,-603.08 200.62,-603.08 200.62,-606.17 197.54,-609.25 194.46,-609.25"/>
<text text-anchor="start" x="154.38" y="-596.7" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(not&#45;connected)/signin/page.tsx -->
<g id="node10" class="node">
<title>app/[locale]/(not&#45;connected)/signin/page.tsx</title>
<g id="a_node10"><a xlink:href="app/[locale]/(not-connected)/signin/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M194.33,-556.25C194.33,-556.25 152.67,-556.25 152.67,-556.25 149.58,-556.25 146.5,-553.17 146.5,-550.08 146.5,-550.08 146.5,-543.92 146.5,-543.92 146.5,-540.83 149.58,-537.75 152.67,-537.75 152.67,-537.75 194.33,-537.75 194.33,-537.75 197.42,-537.75 200.5,-540.83 200.5,-543.92 200.5,-543.92 200.5,-550.08 200.5,-550.08 200.5,-553.17 197.42,-556.25 194.33,-556.25"/>
<text text-anchor="start" x="156.25" y="-543.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/access&#45;denied/page.tsx -->
<g id="node11" class="node">
<title>app/[locale]/access&#45;denied/page.tsx</title>
<g id="a_node11"><a xlink:href="app/[locale]/access-denied/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M194.33,-670.25C194.33,-670.25 152.67,-670.25 152.67,-670.25 149.58,-670.25 146.5,-667.17 146.5,-664.08 146.5,-664.08 146.5,-657.92 146.5,-657.92 146.5,-654.83 149.58,-651.75 152.67,-651.75 152.67,-651.75 194.33,-651.75 194.33,-651.75 197.42,-651.75 200.5,-654.83 200.5,-657.92 200.5,-657.92 200.5,-664.08 200.5,-664.08 200.5,-667.17 197.42,-670.25 194.33,-670.25"/>
<text text-anchor="start" x="156.25" y="-657.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/page.tsx -->
<g id="node12" class="node">
<title>app/[locale]/page.tsx</title>
<g id="a_node12"><a xlink:href="app/[locale]/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M194.33,-723.25C194.33,-723.25 152.67,-723.25 152.67,-723.25 149.58,-723.25 146.5,-720.17 146.5,-717.08 146.5,-717.08 146.5,-710.92 146.5,-710.92 146.5,-707.83 149.58,-704.75 152.67,-704.75 152.67,-704.75 194.33,-704.75 194.33,-704.75 197.42,-704.75 200.5,-707.83 200.5,-710.92 200.5,-710.92 200.5,-717.08 200.5,-717.08 200.5,-720.17 197.42,-723.25 194.33,-723.25"/>
<text text-anchor="start" x="156.25" y="-710.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/globals.css -->
<g id="node13" class="node">
<title>app/globals.css</title>
<g id="a_node13"><a xlink:href="app/globals.css" xlink:title="globals.css">
<path fill="#ffffcc" stroke="black" d="M430.08,-776.25C430.08,-776.25 380.67,-776.25 380.67,-776.25 377.58,-776.25 374.5,-773.17 374.5,-770.08 374.5,-770.08 374.5,-763.92 374.5,-763.92 374.5,-760.83 377.58,-757.75 380.67,-757.75 380.67,-757.75 430.08,-757.75 430.08,-757.75 433.17,-757.75 436.25,-760.83 436.25,-763.92 436.25,-763.92 436.25,-770.08 436.25,-770.08 436.25,-773.17 433.17,-776.25 430.08,-776.25"/>
<text text-anchor="start" x="382.5" y="-763.7" font-family="Helvetica,sans-Serif" font-size="9.00">globals.css</text>
</a>
</g>
</g>
<!-- app/layout.tsx -->
<g id="node14" class="node">
<title>app/layout.tsx</title>
<g id="a_node14"><a xlink:href="app/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M194.46,-776.25C194.46,-776.25 152.54,-776.25 152.54,-776.25 149.46,-776.25 146.38,-773.17 146.38,-770.08 146.38,-770.08 146.38,-763.92 146.38,-763.92 146.38,-760.83 149.46,-757.75 152.54,-757.75 152.54,-757.75 194.46,-757.75 194.46,-757.75 197.54,-757.75 200.62,-760.83 200.62,-763.92 200.62,-763.92 200.62,-770.08 200.62,-770.08 200.62,-773.17 197.54,-776.25 194.46,-776.25"/>
<text text-anchor="start" x="154.38" y="-763.7" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;app/globals.css -->
<g id="edge1" class="edge">
<title>app/layout.tsx&#45;&gt;app/globals.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M200.87,-767C241.22,-767 318.54,-767 365.38,-767"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="365.3,-769.1 371.3,-767 365.3,-764.9 365.3,-769.1"/>
</g>
<!-- app/page.tsx -->
<g id="node15" class="node">
<title>app/page.tsx</title>
<g id="a_node15"><a xlink:href="app/page.tsx" xlink:title="page.tsx">
<path fill="#ccffcc" stroke="black" d="M194.33,-807.25C194.33,-807.25 152.67,-807.25 152.67,-807.25 149.58,-807.25 146.5,-804.17 146.5,-801.08 146.5,-801.08 146.5,-794.92 146.5,-794.92 146.5,-791.83 149.58,-788.75 152.67,-788.75 152.67,-788.75 194.33,-788.75 194.33,-788.75 197.42,-788.75 200.5,-791.83 200.5,-794.92 200.5,-794.92 200.5,-801.08 200.5,-801.08 200.5,-804.17 197.42,-807.25 194.33,-807.25"/>
<text text-anchor="start" x="156.25" y="-794.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/server/DependencyInjection.ts -->
<g id="node16" class="node">
<title>src/server/DependencyInjection.ts</title>
<g id="a_node16"><a xlink:href="src/server/DependencyInjection.ts" xlink:title="DependencyInjection.ts">
<path fill="#ccffcc" stroke="black" d="M223.33,-2645.25C223.33,-2645.25 123.67,-2645.25 123.67,-2645.25 120.58,-2645.25 117.5,-2642.17 117.5,-2639.08 117.5,-2639.08 117.5,-2632.92 117.5,-2632.92 117.5,-2629.83 120.58,-2626.75 123.67,-2626.75 123.67,-2626.75 223.33,-2626.75 223.33,-2626.75 226.42,-2626.75 229.5,-2629.83 229.5,-2632.92 229.5,-2632.92 229.5,-2639.08 229.5,-2639.08 229.5,-2642.17 226.42,-2645.25 223.33,-2645.25"/>
<text text-anchor="start" x="125.5" y="-2632.7" font-family="Helvetica,sans-Serif" font-size="9.00">DependencyInjection.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts -->
<g id="node17" class="node">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts</title>
<g id="a_node17"><a xlink:href="src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts" xlink:title="SaveDeckCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="460.25,-958.25 350.5,-958.25 350.5,-939.75 460.25,-939.75 460.25,-958.25"/>
<text text-anchor="start" x="358.5" y="-945.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">SaveDeckCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts -->
<g id="node18" class="node">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts</title>
<g id="a_node18"><a xlink:href="src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts" xlink:title="SaveDeckCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="244.5,-958.25 102.5,-958.25 102.5,-939.75 244.5,-939.75 244.5,-958.25"/>
<text text-anchor="start" x="110.5" y="-945.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">SaveDeckCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts -->
<g id="edge2" class="edge">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M244.72,-949C275.6,-949 311.59,-949 341.57,-949"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="341.24,-951.1 347.24,-949 341.24,-946.9 341.24,-951.1"/>
</g>
<!-- src/server/application/ports/DeckRepository.ts -->
<g id="node19" class="node">
<title>src/server/application/ports/DeckRepository.ts</title>
<g id="a_node19"><a xlink:href="src/server/application/ports/DeckRepository.ts" xlink:title="DeckRepository.ts">
<polygon fill="#dd1c1c" stroke="black" points="450.12,-1818.25 360.62,-1818.25 360.62,-1799.75 450.12,-1799.75 450.12,-1818.25"/>
<text text-anchor="start" x="368.62" y="-1805.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">DeckRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/application/ports/DeckRepository.ts -->
<g id="edge3" class="edge">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/application/ports/DeckRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M204.31,-958.71C232.84,-969.78 274.27,-990.88 293.25,-1025 314,-1062.29 276.08,-1760.73 304,-1793 315.71,-1806.53 333.89,-1811.85 351.58,-1813.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="351.07,-1815.42 357.17,-1813.63 351.29,-1811.23 351.07,-1815.42"/>
</g>
<!-- src/server/domain/Deck/Deck.ts -->
<g id="node20" class="node">
<title>src/server/domain/Deck/Deck.ts</title>
<g id="a_node20"><a xlink:href="src/server/domain/Deck/Deck.ts" xlink:title="Deck.ts">
<polygon fill="#dc6b0d" stroke="black" points="665.75,-2069.25 611.75,-2069.25 611.75,-2050.75 665.75,-2050.75 665.75,-2069.25"/>
<text text-anchor="start" x="623.75" y="-2056.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">Deck.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/domain/Deck/Deck.ts -->
<g id="edge4" class="edge">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M194.64,-939.44C256.22,-911.79 443.53,-839.46 538.75,-934 560.67,-955.76 526.55,-2022.63 546.75,-2046 560.19,-2061.56 583.1,-2065.01 602.57,-2064.6"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="602.6,-2066.71 608.49,-2064.3 602.39,-2062.51 602.6,-2066.71"/>
</g>
<!-- src/server/application/ports/DeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts -->
<g id="edge33" class="edge">
<title>src/server/application/ports/DeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M408.89,-1818.75C418.94,-1855.6 459.98,-1987.35 546.75,-2046 563.02,-2057 584.64,-2060.67 602.73,-2061.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="602.21,-2063.59 608.25,-2061.63 602.31,-2059.39 602.21,-2063.59"/>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts -->
<g id="node21" class="node">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts</title>
<g id="a_node21"><a xlink:href="src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts" xlink:title="LeaveMatchCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="464.38,-1054.25 346.38,-1054.25 346.38,-1035.75 464.38,-1035.75 464.38,-1054.25"/>
<text text-anchor="start" x="354.38" y="-1041.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">LeaveMatchCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts -->
<g id="node22" class="node">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts</title>
<g id="a_node22"><a xlink:href="src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts" xlink:title="LeaveMatchCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="248.62,-1054.25 98.38,-1054.25 98.38,-1035.75 248.62,-1035.75 248.62,-1054.25"/>
<text text-anchor="start" x="106.38" y="-1041.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">LeaveMatchCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts -->
<g id="edge5" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M248.82,-1045C277.22,-1045 309.43,-1045 337.14,-1045"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="336.94,-1047.1 342.94,-1045 336.94,-1042.9 336.94,-1047.1"/>
</g>
<!-- src/server/application/ports/Context.ts -->
<g id="node23" class="node">
<title>src/server/application/ports/Context.ts</title>
<g id="a_node23"><a xlink:href="src/server/application/ports/Context.ts" xlink:title="Context.ts">
<polygon fill="#dd1c1c" stroke="black" points="433.62,-1570.25 377.12,-1570.25 377.12,-1551.75 433.62,-1551.75 433.62,-1570.25"/>
<text text-anchor="start" x="385.12" y="-1557.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">Context.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge6" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M203.93,-1054.67C232.38,-1065.76 273.94,-1086.93 293.25,-1121 313.2,-1156.2 285.45,-1449.04 304,-1485 318.88,-1513.85 350.27,-1534.8 374.01,-1547.31"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="373.04,-1549.18 379.35,-1550.01 374.94,-1545.43 373.04,-1549.18"/>
</g>
<!-- src/server/application/ports/MatchRepository.ts -->
<g id="node24" class="node">
<title>src/server/application/ports/MatchRepository.ts</title>
<g id="a_node24"><a xlink:href="src/server/application/ports/MatchRepository.ts" xlink:title="MatchRepository.ts">
<polygon fill="#dd1c1c" stroke="black" points="452,-1663.25 358.75,-1663.25 358.75,-1644.75 452,-1644.75 452,-1663.25"/>
<text text-anchor="start" x="366.75" y="-1650.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge7" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M204.26,-1054.74C232.75,-1065.83 274.15,-1086.95 293.25,-1121 307.3,-1146.06 285.13,-1616.34 304,-1638 315.3,-1650.97 332.56,-1656.38 349.59,-1658.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="349.3,-1660.2 355.42,-1658.5 349.58,-1656.01 349.3,-1660.2"/>
</g>
<!-- src/server/domain/Match/errors/MatchAlreadyFinishedError.ts -->
<g id="node25" class="node">
<title>src/server/domain/Match/errors/MatchAlreadyFinishedError.ts</title>
<g id="a_node25"><a xlink:href="src/server/domain/Match/errors/MatchAlreadyFinishedError.ts" xlink:title="MatchAlreadyFinishedError.ts">
<polygon fill="#dc6b0d" stroke="black" points="706.75,-1736.25 570.75,-1736.25 570.75,-1717.75 706.75,-1717.75 706.75,-1736.25"/>
<text text-anchor="start" x="578.75" y="-1723.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchAlreadyFinishedError.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/errors/MatchAlreadyFinishedError.ts -->
<g id="edge8" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/errors/MatchAlreadyFinishedError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M218.58,-1035.31C244.63,-1027.19 275.83,-1012.79 293.25,-988 306.74,-968.81 286.59,-898.72 304,-883 342.72,-848.04 499.77,-848.33 538.75,-883 602.81,-939.99 632.17,-1589.48 637.02,-1708.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="634.92,-1708.45 637.26,-1714.36 639.12,-1708.28 634.92,-1708.45"/>
</g>
<!-- src/server/domain/Match/errors/MatchNotFoundError.ts -->
<g id="node26" class="node">
<title>src/server/domain/Match/errors/MatchNotFoundError.ts</title>
<g id="a_node26"><a xlink:href="src/server/domain/Match/errors/MatchNotFoundError.ts" xlink:title="MatchNotFoundError.ts">
<polygon fill="#dc6b0d" stroke="black" points="693.62,-1767.25 583.88,-1767.25 583.88,-1748.75 693.62,-1748.75 693.62,-1767.25"/>
<text text-anchor="start" x="591.88" y="-1754.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchNotFoundError.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/errors/MatchNotFoundError.ts -->
<g id="edge9" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/errors/MatchNotFoundError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M217.66,-1035.26C243.7,-1027.08 275.22,-1012.63 293.25,-988 313.4,-960.47 278.24,-934.37 304,-912 343.39,-877.79 501.69,-875.29 538.75,-912 555.13,-928.22 531.84,-1724.41 546.75,-1742 553.95,-1750.5 563.94,-1755.56 574.68,-1758.43"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="574.14,-1760.46 580.45,-1759.65 575.02,-1756.35 574.14,-1760.46"/>
</g>
<!-- src/server/domain/Match/Match.ts -->
<g id="node38" class="node">
<title>src/server/domain/Match/Match.ts</title>
<g id="a_node38"><a xlink:href="src/server/domain/Match/Match.ts" xlink:title="Match.ts">
<polygon fill="#dc6b0d" stroke="black" points="665.75,-1820.25 611.75,-1820.25 611.75,-1801.75 665.75,-1801.75 665.75,-1820.25"/>
<text text-anchor="start" x="621.88" y="-1807.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">Match.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/MatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts -->
<g id="edge34" class="edge">
<title>src/server/application/ports/MatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M452.44,-1646.14C481.39,-1644.05 517.22,-1647.19 538.75,-1670 558.16,-1690.56 527.67,-1776.13 546.75,-1797 560.53,-1812.07 583.22,-1815.59 602.5,-1815.33"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="602.42,-1817.43 608.33,-1815.08 602.24,-1813.24 602.42,-1817.43"/>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts -->
<g id="node27" class="node">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts</title>
<g id="a_node27"><a xlink:href="src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts" xlink:title="AddPlayerToMatchMakingQueueCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="506.75,-1150.25 304,-1150.25 304,-1131.75 506.75,-1131.75 506.75,-1150.25"/>
<text text-anchor="start" x="312" y="-1137.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AddPlayerToMatchMakingQueueCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts -->
<g id="node28" class="node">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts</title>
<g id="a_node28"><a xlink:href="src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts" xlink:title="AddPlayerToMatchMakingQueueCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="291,-1150.25 56,-1150.25 56,-1131.75 291,-1131.75 291,-1150.25"/>
<text text-anchor="start" x="64" y="-1137.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AddPlayerToMatchMakingQueueCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge11" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M245.43,-1150.66C263.84,-1156.68 281.71,-1166.39 293.25,-1182 313.28,-1209.08 288.47,-1455.1 304,-1485 318.96,-1513.8 350.34,-1534.76 374.05,-1547.29"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="373.08,-1549.15 379.38,-1549.99 374.98,-1545.4 373.08,-1549.15"/>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts -->
<g id="edge10" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M291.46,-1141C292.61,-1141 293.77,-1141 294.92,-1141"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="294.71,-1143.1 300.71,-1141 294.71,-1138.9 294.71,-1143.1"/>
</g>
<!-- src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="node29" class="node">
<title>src/server/application/ports/MatchmakingQueueRepository.ts</title>
<g id="a_node29"><a xlink:href="src/server/application/ports/MatchmakingQueueRepository.ts" xlink:title="MatchmakingQueueRepository.ts">
<polygon fill="#dd1c1c" stroke="black" points="480.88,-1632.25 329.88,-1632.25 329.88,-1613.75 480.88,-1613.75 480.88,-1632.25"/>
<text text-anchor="start" x="337.88" y="-1619.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchmakingQueueRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge12" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M245.85,-1150.68C264.14,-1156.72 281.85,-1166.42 293.25,-1182 307.2,-1201.06 288.44,-1589.23 304,-1607 308.93,-1612.63 314.98,-1616.84 321.64,-1619.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="320.55,-1621.76 326.9,-1621.98 322.07,-1617.84 320.55,-1621.76"/>
</g>
<!-- src/server/application/ports/TimeService.ts -->
<g id="node30" class="node">
<title>src/server/application/ports/TimeService.ts</title>
<g id="a_node30"><a xlink:href="src/server/application/ports/TimeService.ts" xlink:title="TimeService.ts">
<polygon fill="#dd1c1c" stroke="black" points="443.38,-1601.25 367.38,-1601.25 367.38,-1582.75 443.38,-1582.75 443.38,-1601.25"/>
<text text-anchor="start" x="375.38" y="-1588.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">TimeService.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/TimeService.ts -->
<g id="edge13" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/TimeService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M245.83,-1150.7C264.12,-1156.73 281.83,-1166.44 293.25,-1182 319.15,-1217.31 275.12,-1543.08 304,-1576 317.32,-1591.19 338.8,-1595.99 358.47,-1596.66"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="358.31,-1598.76 364.32,-1596.69 358.33,-1594.56 358.31,-1598.76"/>
</g>
<!-- src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="node31" class="node">
<title>src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<g id="a_node31"><a xlink:href="src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts" xlink:title="MatchmakingQueueItem.ts">
<polygon fill="#dc6b0d" stroke="black" points="855.5,-2226.25 730.75,-2226.25 730.75,-2207.75 855.5,-2207.75 855.5,-2226.25"/>
<text text-anchor="start" x="738.75" y="-2213.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchmakingQueueItem.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge14" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M219.82,-1131.27C245.81,-1123.19 276.51,-1108.85 293.25,-1084 314.65,-1052.24 276.09,-938.22 304,-912 380.04,-840.56 456.21,-848.19 538.75,-912 795.63,-1110.58 679.18,-1297.25 722.75,-1619 726.84,-1649.23 717.32,-2140.61 730.75,-2168 738.02,-2182.84 752.17,-2194.61 765.05,-2202.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="763.62,-2204.48 769.83,-2205.8 765.79,-2200.89 763.62,-2204.48"/>
</g>
<!-- src/server/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge36" class="edge">
<title>src/server/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M481.12,-1618.29C556.59,-1618.18 669.4,-1631.8 722.75,-1707 737.57,-1727.89 719.44,-2145.02 730.75,-2168 738.05,-2182.83 752.19,-2194.6 765.07,-2202.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="763.63,-2204.47 769.85,-2205.79 765.81,-2200.88 763.63,-2204.47"/>
</g>
<!-- src/server/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="node48" class="node">
<title>src/server/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<g id="a_node48"><a xlink:href="src/server/domain/MatchmakingQueue/MatchmakingQueue.ts" xlink:title="MatchmakingQueue.ts">
<polygon fill="#dc6b0d" stroke="black" points="692.5,-2226.25 585,-2226.25 585,-2207.75 692.5,-2207.75 692.5,-2226.25"/>
<text text-anchor="start" x="593" y="-2213.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchmakingQueue.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="edge35" class="edge">
<title>src/server/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M481.18,-1614.7C502.72,-1616.43 524.28,-1622.91 538.75,-1639 559.41,-1661.97 527.47,-2170.86 546.75,-2195 554.11,-2204.21 564.6,-2209.97 575.86,-2213.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="575.31,-2215.52 581.65,-2215 576.37,-2211.45 575.31,-2215.52"/>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts -->
<g id="node32" class="node">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts</title>
<g id="a_node32"><a xlink:href="src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts" xlink:title="CancelMatchRegistrationCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="491,-1211.25 319.75,-1211.25 319.75,-1192.75 491,-1192.75 491,-1211.25"/>
<text text-anchor="start" x="327.75" y="-1198.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CancelMatchRegistrationCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts -->
<g id="node33" class="node">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts</title>
<g id="a_node33"><a xlink:href="src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts" xlink:title="CancelMatchRegistrationCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="275.25,-1211.25 71.75,-1211.25 71.75,-1192.75 275.25,-1192.75 275.25,-1211.25"/>
<text text-anchor="start" x="79.75" y="-1198.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CancelMatchRegistrationCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge16" class="edge">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M245.34,-1211.72C263.75,-1217.75 281.64,-1227.45 293.25,-1243 309.35,-1264.57 291.49,-1461.17 304,-1485 319.09,-1513.74 350.45,-1534.71 374.12,-1547.25"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="373.14,-1549.11 379.44,-1549.96 375.05,-1545.37 373.14,-1549.11"/>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge17" class="edge">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M245.8,-1211.71C264.1,-1217.75 281.82,-1227.45 293.25,-1243 317.22,-1275.6 277.29,-1576.61 304,-1607 308.94,-1612.62 315,-1616.82 321.67,-1619.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="320.57,-1621.74 326.93,-1621.95 322.1,-1617.82 320.57,-1621.74"/>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts -->
<g id="edge15" class="edge">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M275.66,-1202C287.28,-1202 299.08,-1202 310.59,-1202"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="310.3,-1204.1 316.3,-1202 310.3,-1199.9 310.3,-1204.1"/>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts -->
<g id="node34" class="node">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts</title>
<g id="a_node34"><a xlink:href="src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts" xlink:title="CleanUpMatchMakingQueueCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="498.88,-1272.25 311.88,-1272.25 311.88,-1253.75 498.88,-1253.75 498.88,-1272.25"/>
<text text-anchor="start" x="319.88" y="-1259.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CleanUpMatchMakingQueueCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts -->
<g id="node35" class="node">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts</title>
<g id="a_node35"><a xlink:href="src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts" xlink:title="CleanUpMatchMakingQueueCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="283.12,-1272.25 63.88,-1272.25 63.88,-1253.75 283.12,-1253.75 283.12,-1272.25"/>
<text text-anchor="start" x="71.88" y="-1259.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CleanUpMatchMakingQueueCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge19" class="edge">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M244.89,-1272.73C263.39,-1278.76 281.45,-1288.45 293.25,-1304 317.6,-1336.11 285,-1449.47 304,-1485 319.3,-1513.62 350.63,-1534.61 374.23,-1547.19"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="373.23,-1549.04 379.53,-1549.91 375.15,-1545.3 373.23,-1549.04"/>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge21" class="edge">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M245.78,-1272.73C264.07,-1278.77 281.8,-1288.46 293.25,-1304 315.28,-1333.89 279.45,-1610.15 304,-1638 315.33,-1650.85 332.5,-1656.26 349.45,-1658.02"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="349.11,-1660.1 355.24,-1658.41 349.4,-1655.91 349.11,-1660.1"/>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge20" class="edge">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M245.43,-1272.66C263.84,-1278.68 281.71,-1288.39 293.25,-1304 313.28,-1331.08 281.68,-1581.76 304,-1607 308.96,-1612.61 315.03,-1616.79 321.7,-1619.88"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="320.61,-1621.71 326.97,-1621.92 322.13,-1617.79 320.61,-1621.71"/>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts -->
<g id="edge18" class="edge">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M283.38,-1263C289.84,-1263 296.33,-1263 302.76,-1263"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="302.46,-1265.1 308.46,-1263 302.46,-1260.9 302.46,-1265.1"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts -->
<g id="node36" class="node">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts</title>
<g id="a_node36"><a xlink:href="src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts" xlink:title="MakeMatchCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="462.88,-1333.25 347.88,-1333.25 347.88,-1314.75 462.88,-1314.75 462.88,-1333.25"/>
<text text-anchor="start" x="355.88" y="-1320.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MakeMatchCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts -->
<g id="node37" class="node">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts</title>
<g id="a_node37"><a xlink:href="src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts" xlink:title="MakeMatchCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="247.12,-1333.25 99.88,-1333.25 99.88,-1314.75 247.12,-1314.75 247.12,-1333.25"/>
<text text-anchor="start" x="107.88" y="-1320.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MakeMatchCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge23" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M243.66,-1333.66C262.48,-1339.67 281.02,-1349.37 293.25,-1365 309.75,-1386.09 291.02,-1461.58 304,-1485 319.83,-1513.56 351.36,-1534.67 374.88,-1547.3"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="373.85,-1549.12 380.14,-1550.02 375.78,-1545.39 373.85,-1549.12"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge25" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M245.39,-1333.68C263.8,-1339.71 281.68,-1349.42 293.25,-1365 311.35,-1389.37 283.85,-1615.3 304,-1638 315.37,-1650.81 332.55,-1656.21 349.5,-1657.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="349.16,-1660.05 355.29,-1658.37 349.45,-1655.86 349.16,-1660.05"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge24" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M245.34,-1333.72C263.75,-1339.75 281.64,-1349.45 293.25,-1365 309.35,-1386.57 286.08,-1586.92 304,-1607 308.94,-1612.54 314.97,-1616.69 321.59,-1619.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="320.43,-1621.56 326.78,-1621.78 321.95,-1617.64 320.43,-1621.56"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts -->
<g id="edge22" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M247.55,-1324C276.86,-1324 310.38,-1324 338.88,-1324"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="338.53,-1326.1 344.53,-1324 338.53,-1321.9 338.53,-1326.1"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/Match.ts -->
<g id="edge26" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M247.46,-1325.73C264.87,-1322.43 281.73,-1315.46 293.25,-1302 320.22,-1270.51 274.26,-957.89 304,-929 322.71,-910.82 520.22,-910.64 538.75,-929 555.88,-945.97 530.97,-1778.77 546.75,-1797 560.21,-1812.54 583.12,-1816 602.58,-1815.59"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="602.61,-1817.7 608.49,-1815.3 602.4,-1813.5 602.61,-1817.7"/>
</g>
<!-- src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts -->
<g id="node39" class="node">
<title>src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts</title>
<g id="a_node39"><a xlink:href="src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts" xlink:title="UpdatePlayersStatusCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="482.38,-1394.25 328.38,-1394.25 328.38,-1375.75 482.38,-1375.75 482.38,-1394.25"/>
<text text-anchor="start" x="336.38" y="-1381.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UpdatePlayersStatusCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts -->
<g id="node40" class="node">
<title>src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts</title>
<g id="a_node40"><a xlink:href="src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts" xlink:title="UpdatePlayersStatusCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="266.62,-1394.25 80.38,-1394.25 80.38,-1375.75 266.62,-1375.75 266.62,-1394.25"/>
<text text-anchor="start" x="88.38" y="-1381.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UpdatePlayersStatusCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts -->
<g id="edge27" class="edge">
<title>src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M267.01,-1385C284.22,-1385 302.13,-1385 319.11,-1385"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="318.87,-1387.1 324.87,-1385 318.87,-1382.9 318.87,-1387.1"/>
</g>
<!-- src/server/application/ports/AppUserRepository.ts -->
<g id="node41" class="node">
<title>src/server/application/ports/AppUserRepository.ts</title>
<g id="a_node41"><a xlink:href="src/server/application/ports/AppUserRepository.ts" xlink:title="AppUserRepository.ts">
<polygon fill="#dd1c1c" stroke="black" points="457.62,-1694.25 353.12,-1694.25 353.12,-1675.75 457.62,-1675.75 457.62,-1694.25"/>
<text text-anchor="start" x="361.12" y="-1681.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AppUserRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts -->
<g id="edge28" class="edge">
<title>src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M245.35,-1394.72C263.75,-1400.75 281.64,-1410.45 293.25,-1426 309.42,-1447.66 286.01,-1648.83 304,-1669 314.11,-1680.34 328.78,-1685.86 343.79,-1688.19"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="343.43,-1690.27 349.63,-1688.87 343.91,-1686.09 343.43,-1690.27"/>
</g>
<!-- src/server/domain/AppUser/AppUser.ts -->
<g id="node44" class="node">
<title>src/server/domain/AppUser/AppUser.ts</title>
<g id="a_node44"><a xlink:href="src/server/domain/AppUser/AppUser.ts" xlink:title="AppUser.ts">
<polygon fill="#dc6b0d" stroke="black" points="668.88,-2134.25 608.62,-2134.25 608.62,-2115.75 668.88,-2115.75 668.88,-2134.25"/>
<text text-anchor="start" x="616.62" y="-2121.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AppUser.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/AppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge31" class="edge">
<title>src/server/application/ports/AppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M457.98,-1676.5C486.04,-1675.06 518.95,-1679.11 538.75,-1701 553.59,-1717.4 533.26,-2081.48 546.75,-2099 559.04,-2114.96 580.43,-2121.59 599.38,-2124.18"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="598.95,-2126.24 605.14,-2124.8 599.4,-2122.07 598.95,-2126.24"/>
</g>
<!-- src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts -->
<g id="node42" class="node">
<title>src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts</title>
<g id="a_node42"><a xlink:href="src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts" xlink:title="UpdateSinglePlayerStatusCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="493.25,-1455.25 317.5,-1455.25 317.5,-1436.75 493.25,-1436.75 493.25,-1455.25"/>
<text text-anchor="start" x="325.5" y="-1442.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UpdateSinglePlayerStatusCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts -->
<g id="node43" class="node">
<title>src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts</title>
<g id="a_node43"><a xlink:href="src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts" xlink:title="UpdateSinglePlayerStatusCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="277.5,-1455.25 69.5,-1455.25 69.5,-1436.75 277.5,-1436.75 277.5,-1455.25"/>
<text text-anchor="start" x="77.5" y="-1442.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UpdateSinglePlayerStatusCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts -->
<g id="edge30" class="edge">
<title>src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M184.5,-1455.54C207.85,-1478.48 266.08,-1539.54 293.25,-1603 304.95,-1630.32 282.95,-1648.02 304,-1669 314.61,-1679.57 329.21,-1684.89 343.99,-1687.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="343.47,-1689.33 349.68,-1687.99 343.99,-1685.16 343.47,-1689.33"/>
</g>
<!-- src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts -->
<g id="edge29" class="edge">
<title>src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M277.67,-1446C287.92,-1446 298.29,-1446 308.46,-1446"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="308.31,-1448.1 314.31,-1446 308.31,-1443.9 308.31,-1448.1"/>
</g>
<!-- src/server/domain/AppUser/valueObjects/AppUserId.ts -->
<g id="node54" class="node">
<title>src/server/domain/AppUser/valueObjects/AppUserId.ts</title>
<g id="a_node54"><a xlink:href="src/server/domain/AppUser/valueObjects/AppUserId.ts" xlink:title="AppUserId.ts">
<polygon fill="#dc6b0d" stroke="black" points="827.38,-2138.25 758.88,-2138.25 758.88,-2119.75 827.38,-2119.75 827.38,-2138.25"/>
<text text-anchor="start" x="766.88" y="-2125.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AppUserId.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/AppUser/AppUser.ts&#45;&gt;src/server/domain/AppUser/valueObjects/AppUserId.ts -->
<g id="edge37" class="edge">
<title>src/server/domain/AppUser/AppUser.ts&#45;&gt;src/server/domain/AppUser/valueObjects/AppUserId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M669.13,-2125.77C692.03,-2126.37 724.14,-2127.22 749.81,-2127.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="749.51,-2129.98 755.57,-2128.04 749.62,-2125.78 749.51,-2129.98"/>
</g>
<!-- src/server/application/ports/AuthenticationGateway.ts -->
<g id="node45" class="node">
<title>src/server/application/ports/AuthenticationGateway.ts</title>
<g id="a_node45"><a xlink:href="src/server/application/ports/AuthenticationGateway.ts" xlink:title="AuthenticationGateway.ts">
<polygon fill="#dd1c1c" stroke="black" points="464.75,-1725.25 346,-1725.25 346,-1706.75 464.75,-1706.75 464.75,-1725.25"/>
<text text-anchor="start" x="354" y="-1712.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AuthenticationGateway.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/AuthenticationGateway.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge32" class="edge">
<title>src/server/application/ports/AuthenticationGateway.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M464.99,-1707.28C491.35,-1706.86 520.56,-1711.93 538.75,-1732 552.44,-1747.11 534.29,-2082.85 546.75,-2099 559.05,-2114.95 580.44,-2121.58 599.39,-2124.17"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="598.96,-2126.24 605.15,-2124.79 599.41,-2122.06 598.96,-2126.24"/>
</g>
<!-- src/server/application/ports/CryptoPort.ts -->
<g id="node46" class="node">
<title>src/server/application/ports/CryptoPort.ts</title>
<g id="a_node46"><a xlink:href="src/server/application/ports/CryptoPort.ts" xlink:title="CryptoPort.ts">
<polygon fill="#dd1c1c" stroke="black" points="439.62,-1787.25 371.12,-1787.25 371.12,-1768.75 439.62,-1768.75 439.62,-1787.25"/>
<text text-anchor="start" x="379.12" y="-1774.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CryptoPort.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/IdentityProvider.ts -->
<g id="node47" class="node">
<title>src/server/application/ports/IdentityProvider.ts</title>
<g id="a_node47"><a xlink:href="src/server/application/ports/IdentityProvider.ts" xlink:title="IdentityProvider.ts">
<polygon fill="#dd1c1c" stroke="black" points="449.38,-1756.25 361.38,-1756.25 361.38,-1737.75 449.38,-1737.75 449.38,-1756.25"/>
<text text-anchor="start" x="369.38" y="-1743.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">IdentityProvider.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/MatchmakingQueue/MatchmakingQueue.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge38" class="edge">
<title>src/server/domain/MatchmakingQueue/MatchmakingQueue.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M692.62,-2217C701.96,-2217 711.82,-2217 721.56,-2217"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="721.43,-2219.1 727.43,-2217 721.43,-2214.9 721.43,-2219.1"/>
</g>
<!-- src/server/application/queries/loadDeckBuilderSettingsByGameId.ts -->
<g id="node49" class="node">
<title>src/server/application/queries/loadDeckBuilderSettingsByGameId.ts</title>
<g id="a_node49"><a xlink:href="src/server/application/queries/loadDeckBuilderSettingsByGameId.ts" xlink:title="loadDeckBuilderSettingsByGameId.ts">
<polygon fill="#dd1c1c" stroke="black" points="258,-1632.25 89,-1632.25 89,-1613.75 258,-1613.75 258,-1632.25"/>
<text text-anchor="start" x="97" y="-1619.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadDeckBuilderSettingsByGameId.ts</text>
</a>
</g>
</g>
<!-- src/server/application/queries/loadGameById.ts -->
<g id="node50" class="node">
<title>src/server/application/queries/loadGameById.ts</title>
<g id="a_node50"><a xlink:href="src/server/application/queries/loadGameById.ts" xlink:title="loadGameById.ts">
<polygon fill="#dd1c1c" stroke="black" points="216.38,-1663.25 130.62,-1663.25 130.62,-1644.75 216.38,-1644.75 216.38,-1663.25"/>
<text text-anchor="start" x="138.62" y="-1650.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadGameById.ts</text>
</a>
</g>
</g>
<!-- src/server/application/queries/loadGameList.ts -->
<g id="node51" class="node">
<title>src/server/application/queries/loadGameList.ts</title>
<g id="a_node51"><a xlink:href="src/server/application/queries/loadGameList.ts" xlink:title="loadGameList.ts">
<polygon fill="#dd1c1c" stroke="black" points="214.5,-1694.25 132.5,-1694.25 132.5,-1675.75 214.5,-1675.75 214.5,-1694.25"/>
<text text-anchor="start" x="140.5" y="-1681.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadGameList.ts</text>
</a>
</g>
</g>
<!-- src/server/application/queries/loadGameSettingsByGameId.ts -->
<g id="node52" class="node">
<title>src/server/application/queries/loadGameSettingsByGameId.ts</title>
<g id="a_node52"><a xlink:href="src/server/application/queries/loadGameSettingsByGameId.ts" xlink:title="loadGameSettingsByGameId.ts">
<polygon fill="#dd1c1c" stroke="black" points="245.25,-1725.25 101.75,-1725.25 101.75,-1706.75 245.25,-1706.75 245.25,-1725.25"/>
<text text-anchor="start" x="109.75" y="-1712.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadGameSettingsByGameId.ts</text>
</a>
</g>
</g>
<!-- src/server/application/queries/loadMatchById.ts -->
<g id="node53" class="node">
<title>src/server/application/queries/loadMatchById.ts</title>
<g id="a_node53"><a xlink:href="src/server/application/queries/loadMatchById.ts" xlink:title="loadMatchById.ts">
<polygon fill="#dd1c1c" stroke="black" points="216.38,-1756.25 130.62,-1756.25 130.62,-1737.75 216.38,-1737.75 216.38,-1756.25"/>
<text text-anchor="start" x="138.62" y="-1743.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadMatchById.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/Deck/errors/DeckNotOwnedError.ts -->
<g id="node55" class="node">
<title>src/server/domain/Deck/errors/DeckNotOwnedError.ts</title>
<g id="a_node55"><a xlink:href="src/server/domain/Deck/errors/DeckNotOwnedError.ts" xlink:title="DeckNotOwnedError.ts">
<polygon fill="#dc6b0d" stroke="black" points="693.25,-2016.25 584.25,-2016.25 584.25,-1997.75 693.25,-1997.75 693.25,-2016.25"/>
<text text-anchor="start" x="592.25" y="-2003.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">DeckNotOwnedError.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/User/errors/UserNotAuthenticatedError.ts -->
<g id="node56" class="node">
<title>src/server/domain/User/errors/UserNotAuthenticatedError.ts</title>
<g id="a_node56"><a xlink:href="src/server/domain/User/errors/UserNotAuthenticatedError.ts" xlink:title="UserNotAuthenticatedError.ts">
<polygon fill="#dc6b0d" stroke="black" points="706,-1920.25 571.5,-1920.25 571.5,-1901.75 706,-1901.75 706,-1920.25"/>
<text text-anchor="start" x="579.5" y="-1907.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UserNotAuthenticatedError.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/User/errors/UserNotRegisteredError.ts -->
<g id="node57" class="node">
<title>src/server/domain/User/errors/UserNotRegisteredError.ts</title>
<g id="a_node57"><a xlink:href="src/server/domain/User/errors/UserNotRegisteredError.ts" xlink:title="UserNotRegisteredError.ts">
<polygon fill="#dc6b0d" stroke="black" points="700.38,-1889.25 577.12,-1889.25 577.12,-1870.75 700.38,-1870.75 700.38,-1889.25"/>
<text text-anchor="start" x="585.12" y="-1876.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UserNotRegisteredError.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts -->
<g id="node58" class="node">
<title>src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts</title>
<g id="a_node58"><a xlink:href="src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts" xlink:title="UuidIdentityProvider.ts">
<polygon fill="#248cea" stroke="black" points="227.25,-2132.25 119.75,-2132.25 119.75,-2113.75 227.25,-2113.75 227.25,-2132.25"/>
<text text-anchor="start" x="127.75" y="-2119.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UuidIdentityProvider.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/application/ports/CryptoPort.ts -->
<g id="edge39" class="edge">
<title>src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/application/ports/CryptoPort.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M227.7,-2114.45C251.79,-2107.68 278.2,-2095.59 293.25,-2074 311.06,-2048.46 283.34,-1817.29 304,-1794 318.18,-1778.02 341.4,-1773.57 361.94,-1773.33"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="361.78,-1775.43 367.81,-1773.42 361.85,-1771.23 361.78,-1775.43"/>
</g>
<!-- src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/application/ports/IdentityProvider.ts -->
<g id="edge40" class="edge">
<title>src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/application/ports/IdentityProvider.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M227.73,-2114.48C251.84,-2107.72 278.24,-2095.62 293.25,-2074 312.97,-2045.6 281.11,-1788.91 304,-1763 316,-1749.42 334.52,-1744.16 352.41,-1742.73"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="352.19,-1744.84 358.08,-1742.46 351.99,-1740.65 352.19,-1744.84"/>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts -->
<g id="node59" class="node">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts</title>
<g id="a_node59"><a xlink:href="src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts" xlink:title="ConvexAuthenticationGateway.ts">
<polygon fill="#248cea" stroke="black" points="248.62,-2044.25 98.38,-2044.25 98.38,-2025.75 248.62,-2025.75 248.62,-2044.25"/>
<text text-anchor="start" x="106.38" y="-2031.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexAuthenticationGateway.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge42" class="edge">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M224.52,-2044.72C317.74,-2062.83 516.16,-2101.38 599.56,-2117.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="598.95,-2119.6 605.24,-2118.68 599.75,-2115.48 598.95,-2119.6"/>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/application/ports/AuthenticationGateway.ts -->
<g id="edge41" class="edge">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/application/ports/AuthenticationGateway.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M248.92,-2036.35C265.78,-2032.94 282,-2026.01 293.25,-2013 313.69,-1989.36 283.27,-1755.38 304,-1732 312.51,-1722.4 324.29,-1716.96 336.79,-1714.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="337.02,-1716.19 342.55,-1713.05 336.27,-1712.06 337.02,-1716.19"/>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/User/errors/UserNotAuthenticatedError.ts -->
<g id="edge43" class="edge">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/User/errors/UserNotAuthenticatedError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M233.65,-2025.3C252.76,-2021.83 273.98,-2017.62 293.25,-2013 406.5,-1985.82 537.81,-1944.03 600.6,-1923.4"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="601.07,-1925.45 606.12,-1921.58 599.76,-1921.46 601.07,-1925.45"/>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/User/errors/UserNotRegisteredError.ts -->
<g id="edge44" class="edge">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/User/errors/UserNotRegisteredError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M248.84,-2035.24C265.4,-2031.79 281.5,-2025.12 293.25,-2013 315.16,-1990.4 282.18,-1965.69 304,-1943 371.9,-1872.41 492.19,-1866.9 568.3,-1871.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="567.75,-1873.8 573.88,-1872.13 568.04,-1869.61 567.75,-1873.8"/>
</g>
<!-- src/server/infrastructure/gateways/Context/ConvexContext.ts -->
<g id="node60" class="node">
<title>src/server/infrastructure/gateways/Context/ConvexContext.ts</title>
<g id="a_node60"><a xlink:href="src/server/infrastructure/gateways/Context/ConvexContext.ts" xlink:title="ConvexContext.ts">
<polygon fill="#248cea" stroke="black" points="217.5,-1922.25 129.5,-1922.25 129.5,-1903.75 217.5,-1903.75 217.5,-1922.25"/>
<text text-anchor="start" x="137.5" y="-1909.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexContext.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/gateways/Context/ConvexContext.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge45" class="edge">
<title>src/server/infrastructure/gateways/Context/ConvexContext.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M188.4,-1903.45C214.55,-1884.58 270.48,-1839.62 293.25,-1786 302.34,-1764.6 288.46,-1594.3 304,-1577 319.76,-1559.47 346.4,-1555.87 368.28,-1556.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="367.89,-1558.53 373.99,-1556.74 368.11,-1554.34 367.89,-1558.53"/>
</g>
<!-- src/server/infrastructure/gateways/Time/RealTimeService.ts -->
<g id="node61" class="node">
<title>src/server/infrastructure/gateways/Time/RealTimeService.ts</title>
<g id="a_node61"><a xlink:href="src/server/infrastructure/gateways/Time/RealTimeService.ts" xlink:title="RealTimeService.ts">
<polygon fill="#248cea" stroke="black" points="221.25,-1983.25 125.75,-1983.25 125.75,-1964.75 221.25,-1964.75 221.25,-1983.25"/>
<text text-anchor="start" x="133.75" y="-1970.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">RealTimeService.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/gateways/Time/RealTimeService.ts&#45;&gt;src/server/application/ports/TimeService.ts -->
<g id="edge46" class="edge">
<title>src/server/infrastructure/gateways/Time/RealTimeService.ts&#45;&gt;src/server/application/ports/TimeService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M221.66,-1978.26C246.77,-1977.73 275.97,-1972.12 293.25,-1952 318.16,-1922.99 278.73,-1636.7 304,-1608 317.25,-1592.96 338.5,-1588.11 358.05,-1587.38"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="357.87,-1589.48 363.85,-1587.33 357.84,-1585.28 357.87,-1589.48"/>
</g>
<!-- src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts -->
<g id="node62" class="node">
<title>src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts</title>
<g id="a_node62"><a xlink:href="src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts" xlink:title="ConvexAppUserRepository.ts">
<polygon fill="#248cea" stroke="black" points="241.5,-2201.25 105.5,-2201.25 105.5,-2182.75 241.5,-2182.75 241.5,-2201.25"/>
<text text-anchor="start" x="113.5" y="-2188.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexAppUserRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts -->
<g id="edge47" class="edge">
<title>src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M241.69,-2190C261.26,-2185.91 280.72,-2177.71 293.25,-2162 309.23,-2141.97 287.15,-1720.29 304,-1701 314.04,-1689.5 328.76,-1683.95 343.85,-1681.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="344.01,-1683.73 349.74,-1680.97 343.54,-1679.56 344.01,-1683.73"/>
</g>
<!-- src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge48" class="edge">
<title>src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M241.86,-2182.26C340.57,-2167.98 521.26,-2141.85 599.72,-2130.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="599.69,-2132.63 605.33,-2129.69 599.09,-2128.47 599.69,-2132.63"/>
</g>
<!-- src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts -->
<g id="node63" class="node">
<title>src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts</title>
<g id="a_node63"><a xlink:href="src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts" xlink:title="ConvexDeckRepository.ts">
<polygon fill="#248cea" stroke="black" points="234,-2262.25 113,-2262.25 113,-2243.75 234,-2243.75 234,-2262.25"/>
<text text-anchor="start" x="121" y="-2249.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexDeckRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/application/ports/DeckRepository.ts -->
<g id="edge49" class="edge">
<title>src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/application/ports/DeckRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M234.43,-2255.26C255.61,-2252.93 277.95,-2246.39 293.25,-2231 312.35,-2211.79 297.99,-2197.42 304,-2171 334.39,-2037.52 382.42,-1879.57 398.74,-1827.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="400.64,-1827.95 400.42,-1821.6 396.63,-1826.7 400.64,-1827.95"/>
</g>
<!-- src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts -->
<g id="edge50" class="edge">
<title>src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M234.44,-2251.14C254.43,-2248.21 275.99,-2242.34 293.25,-2231 300.41,-2226.3 297.5,-2220.58 304,-2215 394.46,-2137.38 438.47,-2150.82 546.75,-2101 567.23,-2091.58 590.39,-2081.15 608.22,-2073.16"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="608.63,-2075.28 613.25,-2070.92 606.91,-2071.45 608.63,-2075.28"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts -->
<g id="node64" class="node">
<title>src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts</title>
<g id="a_node64"><a xlink:href="src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts" xlink:title="InMemoryAppUserRepository.ts">
<polygon fill="#248cea" stroke="black" points="246,-2354.25 101,-2354.25 101,-2335.75 246,-2335.75 246,-2354.25"/>
<text text-anchor="start" x="109" y="-2341.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">InMemoryAppUserRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts -->
<g id="edge51" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M246.32,-2350.89C264.06,-2348.56 281.36,-2342.37 293.25,-2329 316.44,-2302.93 281.12,-1727.35 304,-1701 314.01,-1689.47 328.72,-1683.91 343.81,-1681.59"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="343.97,-1683.69 349.69,-1680.93 343.49,-1679.52 343.97,-1683.69"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge52" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M246.39,-2349.96C263.81,-2347.54 280.93,-2341.54 293.25,-2329 322.1,-2299.64 275.23,-2266.45 304,-2237 377.63,-2161.64 442.95,-2247.85 538.75,-2204 543.05,-2202.03 543.12,-2200.02 546.75,-2197 571.08,-2176.75 599.83,-2154.23 618.41,-2139.86"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="619.37,-2141.77 622.83,-2136.44 616.8,-2138.44 619.37,-2141.77"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts -->
<g id="node65" class="node">
<title>src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts</title>
<g id="a_node65"><a xlink:href="src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts" xlink:title="InMemoryDeckRepository.ts">
<polygon fill="#248cea" stroke="black" points="238.5,-2385.25 108.5,-2385.25 108.5,-2366.75 238.5,-2366.75 238.5,-2385.25"/>
<text text-anchor="start" x="116.5" y="-2372.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">InMemoryDeckRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts&#45;&gt;src/server/application/ports/DeckRepository.ts -->
<g id="edge53" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts&#45;&gt;src/server/application/ports/DeckRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M238.93,-2382.07C258.93,-2380.46 279.34,-2374.62 293.25,-2360 303.09,-2349.66 301.75,-2246.09 304,-2232 329.37,-2073.15 382.07,-1885.6 399.01,-1827.25"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="400.98,-1827.98 400.65,-1821.63 396.95,-1826.81 400.98,-1827.98"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts -->
<g id="edge54" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M238.88,-2382.02C258.87,-2380.41 279.3,-2374.58 293.25,-2360 311.61,-2340.82 285.86,-2260.39 304,-2241 376.17,-2163.88 467.06,-2281.57 538.75,-2204 554.33,-2187.14 533.55,-2119.78 546.75,-2101 559.77,-2082.47 583.2,-2072.18 603.03,-2066.54"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="603.34,-2068.63 608.62,-2065.09 602.29,-2064.56 603.34,-2068.63"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts -->
<g id="node66" class="node">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts</title>
<g id="a_node66"><a xlink:href="src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts" xlink:title="InMemoryMatchRepository.ts">
<polygon fill="#248cea" stroke="black" points="240.38,-2323.25 106.62,-2323.25 106.62,-2304.75 240.38,-2304.75 240.38,-2323.25"/>
<text text-anchor="start" x="114.62" y="-2310.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">InMemoryMatchRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge55" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M240.75,-2316.92C260.5,-2314.23 280.31,-2307.28 293.25,-2292 315.58,-2265.62 281.34,-1696.1 304,-1670 315.28,-1657.01 332.53,-1651.59 349.56,-1649.86"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="349.55,-1651.97 355.4,-1649.48 349.27,-1647.78 349.55,-1651.97"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts -->
<g id="edge56" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M240.75,-2315.96C260.14,-2313.13 279.74,-2306.35 293.25,-2292 306.6,-2277.82 296.99,-2223.18 304,-2205 371.46,-2030.07 406.52,-1984.44 546.75,-1860 563.74,-1844.92 586.19,-1832.77 604.53,-1824.34"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="605.24,-1826.32 609.87,-1821.97 603.54,-1822.49 605.24,-1826.32"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts -->
<g id="node67" class="node">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts</title>
<g id="a_node67"><a xlink:href="src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts" xlink:title="InMemoryMatchmakingQueueRepository.ts">
<polygon fill="#248cea" stroke="black" points="269.25,-2416.25 77.75,-2416.25 77.75,-2397.75 269.25,-2397.75 269.25,-2416.25"/>
<text text-anchor="start" x="85.75" y="-2403.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">InMemoryMatchmakingQueueRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge57" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M269.74,-2407.05C278.65,-2403.48 286.76,-2398.31 293.25,-2391 320.98,-2359.74 276.65,-1670.59 304,-1639 308.9,-1633.34 314.93,-1629.12 321.58,-1626.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="322,-1628.1 326.82,-1623.96 320.47,-1624.19 322,-1628.1"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge59" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M261.78,-2397.29C272.44,-2395.5 283.13,-2393.42 293.25,-2391 475.73,-2347.36 685.09,-2262.37 761.88,-2229.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="762.25,-2232.1 766.95,-2227.83 760.61,-2228.23 762.25,-2232.1"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="edge58" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M269.53,-2406.48C278.42,-2403 286.59,-2398.01 293.25,-2391 313.07,-2370.15 283.76,-2282.45 304,-2262 340.86,-2224.76 487.03,-2248.39 538.75,-2240 556.55,-2237.11 575.91,-2232.86 592.76,-2228.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="593.04,-2230.9 598.37,-2227.43 592.04,-2226.82 593.04,-2230.9"/>
</g>
<!-- src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts -->
<g id="node68" class="node">
<title>src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts</title>
<g id="a_node68"><a xlink:href="src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts" xlink:title="ConvexMatchRepository.ts">
<polygon fill="#248cea" stroke="black" points="235.88,-2477.25 111.12,-2477.25 111.12,-2458.75 235.88,-2458.75 235.88,-2477.25"/>
<text text-anchor="start" x="119.12" y="-2464.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexMatchRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge60" class="edge">
<title>src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M236.13,-2471.51C257.38,-2469.29 279.33,-2462.51 293.25,-2446 321.05,-2413.04 275.79,-1702.6 304,-1670 315.26,-1656.99 332.5,-1651.57 349.54,-1649.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="349.53,-1651.95 355.37,-1649.46 349.25,-1647.76 349.53,-1651.95"/>
</g>
<!-- src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts -->
<g id="edge61" class="edge">
<title>src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M236.25,-2470.03C256.87,-2467.52 278.37,-2460.92 293.25,-2446 312.07,-2427.12 293.93,-2411.68 304,-2387 374.36,-2214.61 480.25,-2216.77 538.75,-2040 545.04,-2020.99 536.11,-1876.96 546.75,-1860 559.36,-1839.92 583.19,-1827.52 603.3,-1820.21"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="603.78,-1822.27 608.79,-1818.36 602.43,-1818.29 603.78,-1822.27"/>
</g>
<!-- src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts -->
<g id="node69" class="node">
<title>src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts</title>
<g id="a_node69"><a xlink:href="src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts" xlink:title="ConvexMatchmakingQueueRepository.ts">
<polygon fill="#248cea" stroke="black" points="264.75,-2538.25 82.25,-2538.25 82.25,-2519.75 264.75,-2519.75 264.75,-2538.25"/>
<text text-anchor="start" x="90.25" y="-2525.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexMatchmakingQueueRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge62" class="edge">
<title>src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M265.13,-2526.03C275.93,-2521.98 285.75,-2515.9 293.25,-2507 324.32,-2470.11 272.47,-1675.49 304,-1639 308.89,-1633.34 314.92,-1629.11 321.57,-1626"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="321.99,-1628.09 326.81,-1623.95 320.46,-1624.18 321.99,-1628.09"/>
</g>
<!-- src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge64" class="edge">
<title>src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M265.08,-2522.09C341.65,-2513.24 452.36,-2493.19 538.75,-2448 647.72,-2391 745.83,-2275.56 779.63,-2233.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="781.07,-2234.68 783.14,-2228.67 777.78,-2232.08 781.07,-2234.68"/>
</g>
<!-- src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="edge63" class="edge">
<title>src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M244.7,-2519.3C260.86,-2516.16 277.83,-2512.12 293.25,-2507 408.7,-2468.67 449.7,-2468.86 538.75,-2386 586.53,-2341.54 618.32,-2268.74 631.23,-2235.09"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="633.18,-2235.89 633.31,-2229.53 629.24,-2234.42 633.18,-2235.89"/>
</g>
</g>
</svg>
    <script>
      var gMode = new Mode();

var title2ElementMap = (function makeElementMap() {
  /** @type {NodeListOf<SVGGElement>} */
  var nodes = document.querySelectorAll(".node");
  /** @type {NodeListOf<SVGGElement>} */
  var edges = document.querySelectorAll(".edge");
  return new Title2ElementMap(edges, nodes);
})();

function getHoverHandler(pTitle2ElementMap) {
  /** @type {string} */
  var currentHighlightedTitle = "";

  /** @param {MouseEvent} pMouseEvent */
  return function hoverHighlightHandler(pMouseEvent) {
    var closestNodeOrEdge = pMouseEvent.target.closest(".edge, .node");
    var closestTitleText = getTitleText(closestNodeOrEdge);

    if (
      currentHighlightedTitle !== closestTitleText &&
      gMode.get() === gMode.HOVER
    ) {
      resetNodesAndEdges();
      addHighlight(closestNodeOrEdge);
      pTitle2ElementMap.get(closestTitleText).forEach(addHighlight);
      currentHighlightedTitle = closestTitleText;
    }
  };
}

function getSelectHandler(pTitle2ElementMap) {
  /** @type {string} */
  var currentHighlightedTitle = "";

  /** @param {MouseEvent} pMouseEvent */
  return function selectHighlightHandler(pMouseEvent) {
    pMouseEvent.preventDefault();

    var closestNodeOrEdge = pMouseEvent.target.closest(".edge, .node");
    var closestTitleText = getTitleText(closestNodeOrEdge);

    if (closestNodeOrEdge) {
      gMode.setToSelect();
    } else {
      gMode.setToHover();
    }
    if (currentHighlightedTitle !== closestTitleText) {
      resetNodesAndEdges();
      addHighlight(closestNodeOrEdge);
      pTitle2ElementMap.get(closestTitleText).forEach(addHighlight);
      currentHighlightedTitle = closestTitleText;
    }
  };
}
function Mode() {
  var HOVER = 1;
  var SELECT = 2;

  function setToHover() {
    this._mode = HOVER;
  }
  function setToSelect() {
    this._mode = SELECT;
  }

  /**
   * @returns {number}
   */
  function get() {
    return this._mode || HOVER;
  }

  return {
    HOVER: HOVER,
    SELECT: SELECT,
    setToHover: setToHover,
    setToSelect: setToSelect,
    get: get,
  };
}

/**
 *
 * @param {SVGGelement[]} pEdges
 * @param {SVGGElement[]} pNodes
 * @return {{get: (pTitleText:string) => SVGGElement[]}}
 */
function Title2ElementMap(pEdges, pNodes) {
  /* {{[key: string]: SVGGElement[]}} */
  var elementMap = buildMap(pEdges, pNodes);

  /**
   * @param {NodeListOf<SVGGElement>} pEdges
   * @param {NodeListOf<SVGGElement>} pNodes
   * @return {{[key: string]: SVGGElement[]}}
   */
  function buildMap(pEdges, pNodes) {
    var title2NodeMap = buildTitle2NodeMap(pNodes);

    return nodeListToArray(pEdges).reduce(addEdgeToMap(title2NodeMap), {});
  }
  /**
   * @param {NodeListOf<SVGGElement>} pNodes
   * @return {{[key: string]: SVGGElement}}
   */
  function buildTitle2NodeMap(pNodes) {
    return nodeListToArray(pNodes).reduce(addNodeToMap, {});
  }

  function addNodeToMap(pMap, pNode) {
    var titleText = getTitleText(pNode);

    if (titleText) {
      pMap[titleText] = pNode;
    }
    return pMap;
  }

  function addEdgeToMap(pNodeMap) {
    return function (pEdgeMap, pEdge) {
      /** @type {string} */
      var titleText = getTitleText(pEdge);

      if (titleText) {
        var edge = pryEdgeFromTitle(titleText);

        pEdgeMap[titleText] = [pNodeMap[edge.from], pNodeMap[edge.to]];
        (pEdgeMap[edge.from] || (pEdgeMap[edge.from] = [])).push(pEdge);
        (pEdgeMap[edge.to] || (pEdgeMap[edge.to] = [])).push(pEdge);
      }
      return pEdgeMap;
    };
  }

  /**
   *
   * @param {string} pString
   * @return {{from?: string; to?:string;}}
   */
  function pryEdgeFromTitle(pString) {
    var nodeNames = pString.split(/\s*->\s*/);

    return {
      from: nodeNames.shift(),
      to: nodeNames.shift(),
    };
  }
  /**
   *
   * @param {string} pTitleText
   * @return {SVGGElement[]}
   */
  function get(pTitleText) {
    return (pTitleText && elementMap[pTitleText]) || [];
  }
  return {
    get: get,
  };
}

/**
 * @param {SVGGElement} pGElement
 * @return {string?}
 */
function getTitleText(pGElement) {
  /** @type {SVGTitleElement} */
  var title = pGElement && pGElement.querySelector("title");
  /** @type {string} */
  var titleText = title && title.textContent;

  if (titleText) {
    titleText = titleText.trim();
  }
  return titleText;
}

/**
 * @param {NodeListOf<Element>} pNodeList
 * @return {Element[]}
 */
function nodeListToArray(pNodeList) {
  var lReturnValue = [];

  pNodeList.forEach(function (pElement) {
    lReturnValue.push(pElement);
  });

  return lReturnValue;
}

function resetNodesAndEdges() {
  nodeListToArray(document.querySelectorAll(".current")).forEach(
    removeHighlight,
  );
}

/**
 * @param {SVGGElement} pGElement
 */
function removeHighlight(pGElement) {
  if (pGElement && pGElement.classList) {
    pGElement.classList.remove("current");
  }
}

/**
 * @param {SVGGElement} pGroup
 */
function addHighlight(pGroup) {
  if (pGroup && pGroup.classList) {
    pGroup.classList.add("current");
  }
}

var gHints = {
  HIDDEN: 1,
  SHOWN: 2,
  state: 1, // === HIDDEN
  show: function () {
    document.getElementById("hints").removeAttribute("style");
    gHints.state = gHints.SHOWN;
  },
  hide: function () {
    document.getElementById("hints").style = "display:none";
    gHints.state = gHints.HIDDEN;
  },
  toggle: function () {
    if ((gHints.state || gHints.HIDDEN) === gHints.HIDDEN) {
      gHints.show();
    } else {
      gHints.hide();
    }
  },
};

/** @param {KeyboardEvent} pKeyboardEvent */
function keyboardEventHandler(pKeyboardEvent) {
  if (pKeyboardEvent.key === "Escape") {
    resetNodesAndEdges();
    gMode.setToHover();
    gHints.hide();
  }
  if (pKeyboardEvent.key === "F1") {
    pKeyboardEvent.preventDefault();
    gHints.toggle();
  }
}

document.addEventListener("contextmenu", getSelectHandler(title2ElementMap));
document.addEventListener("mouseover", getHoverHandler(title2ElementMap));
document.addEventListener("keydown", keyboardEventHandler);
document.getElementById("close-hints").addEventListener("click", gHints.hide);
document.getElementById("button_help").addEventListener("click", gHints.toggle);
document.querySelector("svg").insertAdjacentHTML(
  "afterbegin",
  `<linearGradient id="edgeGradient">
      <stop offset="0%" stop-color="fuchsia"/>
      <stop offset="100%" stop-color="purple"/>
   </linearGradient>
  `,
);

// Add a small increment to the last value of the path to make gradients on
// horizontal paths work. Without them all browsers I tested with (firefox,
// chrome) do not render the gradient, but instead make the line transparent
// (or the color of the background, I haven't looked into it that deeply,
// but for the hack it doesn't matter which).
function skewLineABit(lDrawingInstructions) {
  var lLastValue = lDrawingInstructions.match(/(\d+\.?\d*)$/)[0];
  // Smaller values than .001 _should_ work as well, but don't in all
  // cases. Even this value is so small that it is not visible to the
  // human eye (tested with the two I have at my disposal).
  var lIncrement = 0.001;
  var lNewLastValue = parseFloat(lLastValue) + lIncrement;

  return lDrawingInstructions.replace(lLastValue, lNewLastValue);
}

nodeListToArray(document.querySelectorAll("path"))
  .filter(function (pElement) {
    return pElement.parentElement.classList.contains("edge");
  })
  .forEach(function (pElement) {
    pElement.attributes.d.value = skewLineABit(pElement.attributes.d.value);
  });

    </script>
  </body>
</html>
